(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{33:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var c=o(n(34));e.__webpack_require_UNI_MP_PLUGIN__=n,t(c.default)}).call(this,n(1)["default"],n(2)["createPage"])},34:function(e,t,n){"use strict";n.r(t);var o=n(35),c=n(37);for(var i in c)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return c[e]}))}(i);n(39);var s,a=n(32),r=Object(a["default"])(c["default"],o["render"],o["staticRenderFns"],!1,null,"57280228",null,!1,o["components"],s);r.options.__file="pages/index/index.vue",t["default"]=r.exports},35:function(e,t,n){"use strict";n.r(t);var o=n(36);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},36:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return c})),n.d(t,"staticRenderFns",(function(){return s})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return o}));var c=function(){var e=this,t=e.$createElement,n=(e._self._c,e.devices.length),o=e.connected&&e.sensorData.lastUpdate>0?e.formatTime(e.sensorData.lastUpdate):null,c=e.devices.length,i=e.devices.length,s=e.receivedMessages.length;e.$mp.data=Object.assign({},{$root:{g0:n,m0:o,g1:c,g2:i,g3:s}})},i=!1,s=[];c._withStripped=!0},37:function(e,t,n){"use strict";n.r(t);var o=n(38),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},38:function(e,t,n){"use strict";(function(e){function n(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=o(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var c=0,i=function(){};return{s:i,n:function(){return c>=e.length?{done:!0}:{done:!1,value:e[c++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,r=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){r=!0,s=e},f:function(){try{a||null==n.return||n.return()}finally{if(r)throw s}}}}function o(e,t){if(e){if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isConnect:0,deviceId:"",serviceId:"",notifyId:"",receivedTxt:[],receiveBuf:[],sentWiFiTxt:"ZHHC",sentPwdTxt:"666888999",isBluetoothAvailable:!1,isScanning:!1,connected:!1,devices:[],deviceName:"",characteristicId_write:"",characteristicId_notify:"",receivedMessages:[],gearValue:0,buzzerStatus:0,protectionResetType:1,fanGear:0,buzzerFrequency:1e3,buzzerDuration:100,currentBuzzerState:!1,currentVoltageGear:0,currentAirQuality:{level:0,text:"未知",color:"#999",icon:"❓"},sensorData:{pm25:0,voc:0,anionConcentration:0,temperature:0,humidity:0,lastUpdate:0},connectionMonitorTimer:null,lastHeartbeatTime:0,autoReconnectEnabled:!0,reconnectAttempts:0,maxReconnectAttempts:5}},onLoad:function(){this.initBluetoothAdapter(),e.onBluetoothAdapterStateChange(this.onBluetoothAdapterStateChange),e.onBLEConnectionStateChange(this.onBLEConnectionStateChange),e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),e.onBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onLoad中重新注册")},onShow:function(){},onUnload:function(){e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onUnload中移除"),this.stopConnectionMonitor(),this.connected&&this.deviceId&&e.closeBLEConnection({deviceId:this.deviceId,success:function(){return console.log("页面卸载时已断开蓝牙连接")},fail:function(){return console.log("页面卸载时断开蓝牙连接失败")}})},methods:{ceshi:function(){e.redirectTo({url:"/pages/index/ceshi"})},receiveDataFromBle:function(e){console.log("--- receiveDataFromBle entered with data ---",new Uint8Array(e));var t=new Uint8Array(e),o=Array.from(t).map((function(e){return e.toString(16).padStart(2,"0")})).join(" "),c="📡 ".concat(o);if(this.receivedMessages.push(c),!(t.length<5||170!==t[0]||85!==t[1])){var i=t[2],s=t[3],a=t[t.length-1];if(t.length===5+s){var r=t.slice(2,t.length-1),l=t.slice(0,t.length-1),u=this.crc8(r,r.length),d=this.crc8(l,l.length),h=255&this.simpleSum(r),f=255&this.simpleSum(l),v=this.xorChecksum(r),g=[];135===t[2]&&(g.push({name:"心跳SUM1",value:134}),g.push({name:"心跳CMD",value:135}),g.push({name:"心跳FIXED",value:60}),g.push({name:"心跳NOT",value:120}),g.push({name:"心跳SPEC1",value:60}),g.push({name:"心跳SPEC2",value:44})),console.log("校验和测试:"),console.log("  CRC-8(cmd+data): 0x".concat(u.toString(16).padStart(2,"0"))),console.log("  CRC-8(all): 0x".concat(d.toString(16).padStart(2,"0"))),console.log("  SUM(cmd+data): 0x".concat(h.toString(16).padStart(2,"0"))),console.log("  SUM(all): 0x".concat(f.toString(16).padStart(2,"0"))),console.log("  XOR(cmd+data): 0x".concat(v.toString(16).padStart(2,"0"))),g.length>0&&g.forEach((function(e){console.log("  ".concat(e.name,": 0x").concat(e.value.toString(16).padStart(2,"0")))})),console.log("  接收到的: 0x".concat(a.toString(16).padStart(2,"0")));var m=!1,C="";if(u===a)m=!0,C="CRC-8(cmd+data)";else if(d===a)m=!0,C="CRC-8(all)";else if(h===a)m=!0,C="SUM(cmd+data)";else if(f===a)m=!0,C="SUM(all)";else if(v===a)m=!0,C="XOR(cmd+data)";else{var p,w=n(g);try{for(w.s();!(p=w.n()).done;){var B=p.value;if(B.value===a){m=!0,C=B.name;break}}}catch(S){w.e(S)}finally{w.f()}}if(m){console.log("✅ 校验和匹配，使用方式: ".concat(C));var b="✅ 校验和验证成功 (".concat(C,")");this.receivedMessages.push(b)}else{console.warn("所有校验方式都不匹配，跳过校验继续处理");var T="⚠️ 校验和不匹配，但继续处理数据";this.receivedMessages.push(T)}var y=t.slice(4,4+s);console.log("接收到有效数据包:",t),console.log("命令字:",i.toString(16)),console.log("数据长度:",s),console.log("数据区:",y),this.decodeDataFromBle(i,y)}}},bytesToUint16:function(e,t){return e[t+1]<<8|e[t]},bytesToUint32:function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},decodeDataFromBle:function(t,n){switch(console.log("接收到命令: 0x"+t.toString(16),"数据:",n),t){case 129:case 161:if(n.length>=2){var o=n[0],c=n[1],i=1&o?"打开":"关闭",s=(6&o)>>1,a="";0===s?a="关闭":1===s?a="低挡":2===s?a="中挡":3===s&&(a="高挡");var r=8&o?"打开":"关闭",l=16&o?"异常":"正常",u=3&c,d="";0===u?d="正常模式":1===u?d="节能模式":2===u&&(d="强力模式");var h=this.currentBuzzerState,f=this.currentVoltageGear,v=!!(8&o),g=s;this.currentBuzzerState=v,this.currentVoltageGear=g,console.log("设备状态: 开关: ".concat(i,", 挡位: ").concat(a,", 蜂鸣器: ").concat(r,", 运行: ").concat(l,", 工作模式: ").concat(d));var m="📊 设备状态更新: 挡位=".concat(a,", 蜂鸣器=").concat(r,", 运行=").concat(l);if(this.receivedMessages.push(m),h!==v&&e.showToast({title:"蜂鸣器已".concat(v?"开启":"关闭"),icon:"success",duration:1500}),f!==g){var C=0===g?"关闭":"".concat(g,"挡");e.showToast({title:"电压挡位已设置为".concat(C),icon:"success",duration:1500})}}else console.warn("设备状态响应数据长度不足");break;case 130:case 162:if(n.length>=10){var p=this.bytesToUint16(n,0),w=this.bytesToUint16(n,2),B=this.bytesToUint16(n,4),b=this.bytesToUint16(n,6)/10,T=this.bytesToUint16(n,8)/10;this.sensorData={pm25:p,voc:w,anionConcentration:B,temperature:b,humidity:T,lastUpdate:Date.now()},console.log("传感器数据: PM2.5: ".concat(p,"μg/m³, VOC: ").concat(w,"ppm, 负离子浓度: ").concat(B,"个/cm³, 温度: ").concat(b,"°C, 湿度: ").concat(T,"%RH"));var y="🌡️ 传感器数据已更新 - PM2.5: ".concat(p,"μg/m³, 温度: ").concat(b,"°C");this.receivedMessages.push(y),162===t&&e.showToast({title:"传感器数据已更新",icon:"success",duration:1500})}else console.warn("传感器数据响应数据长度不足");break;case 131:if(n.length>=4){var S=this.bytesToUint32(n,0);console.log("保养信息: 已使用时间: ".concat(S,"小时"))}else console.warn("保养信息响应数据长度不足");break;case 132:if(n.length>=6){var I=this.bytesToUint16(n,0)/100,x=this.bytesToUint16(n,2)/1e3,D=this.bytesToUint16(n,4)/10;console.log("电气参数: 电压: ".concat(I,"V, 电流: ").concat(x,"A, 功率: ").concat(D,"W"))}else console.warn("电气参数响应数据长度不足");break;case 133:case 165:if(n.length>=1){var A=n[0],L=1&A?"已触发":"未触发",E=2&A?"已触发":"未触发",M=4&A?"已触发":"未触发",U=8&A?"已触发":"未触发";console.log("保护状态: 短路: ".concat(L,", 过流: ").concat(E,", 低压: ").concat(M,", 过温: ").concat(U))}else console.warn("保护状态响应数据长度不足");break;case 134:case 166:if(n.length>=1){var k=n[0];this.updateAirQualityDisplay(k),console.log("空气质量: ".concat(this.currentAirQuality.text));var F="🌬️ 空气质量: ".concat(this.currentAirQuality.text);this.receivedMessages.push(F),166===t&&e.showToast({title:"空气质量变化: ".concat(this.currentAirQuality.text),icon:"none",duration:2e3})}else console.warn("空气质量响应数据长度不足");break;case 135:console.log("✅ 心跳响应成功"),this.lastHeartbeatTime=Date.now();var _="💓 心跳响应成功 - 设备连接正常";this.receivedMessages.push(_),e.showToast({title:"心跳响应成功",icon:"success",duration:1e3});break;case 145:case 146:case 147:case 148:case 149:case 150:case 151:case 152:if(n.length>=1){var R=n[0],V="0x".concat(t.toString(16));1===R?(console.log("".concat(V," 命令执行成功")),e.showToast({title:"".concat(V," 命令执行成功"),icon:"none"})):(console.log("".concat(V," 命令执行失败")),e.showToast({title:"".concat(V," 命令执行失败"),icon:"none"}))}else console.warn("控制命令响应数据长度不足");break;case 137:if(n.length>=5){var z=this.bytesToUint32(n,0),H=n[4],O="";switch(H){case 0:O="不支持";break;case 1:O="正常";break;case 2:O="故障";break;default:O="未知";break}console.log("负离子数据: 计数值: ".concat(z,"个/cm³, 传感器状态: ").concat(O))}else console.warn("负离子数据响应数据长度不足");break;case 138:if(n.length>=8){var P=n[0],q="";switch(P){case 0:q="停机状态";break;case 1:q="正常工作";break;case 2:q="待机状态";break;case 3:q="保护状态";break;case 4:q="故障状态";break;default:q="未知";break}var j=1===n[1]?"正常":"异常",G=1===n[2]?"正常":"异常",Q=this.bytesToUint32(n,3);console.log("设备工作状态: ".concat(q,", 电压状态: ").concat(j,", 电流状态: ").concat(G,", 累计工作时间: ").concat(Q,"秒"))}else console.warn("设备工作状态详细响应数据长度不足");break;case 143:if(n.length>=2){var N=n[0],W=n[1],$="";switch(W){case 1:$="参数错误";break;case 2:$="命令不支持";break;case 3:$="硬件错误";break;default:$="未知错误";break}console.warn("错误响应: 原始命令字: 0x".concat(N.toString(16),", 错误代码: 0x").concat(W.toString(16)," (").concat($,")"))}else console.warn("错误响应数据长度不足");break;case 163:n.length>=1&&1===n[0]?(console.log("保养提醒: 需要更换滤芯"),e.showToast({title:"需要更换滤芯",icon:"none"})):console.warn("保养提醒通知数据异常或长度不足");break;default:console.log("未知命令: 0x"+t.toString(16));break}},sendBleCommand:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!this.connected||!this.deviceId||!this.serviceId||!this.characteristicId_write)return e.showToast({title:"蓝牙未连接或特征值不可用",icon:"none"}),void console.warn("发送失败：蓝牙未连接或特征值不可用");var o=n.length,c=new Uint8Array(5+o);c[0]=170,c[1]=85,c[2]=t,c[3]=o;for(var i=0;i<o;i++)c[4+i]=n[i];var s=c.slice(2,4+o),a=this.crc8(s,s.length);return c[4+o]=a,console.log("准备发送BLE命令包:",Array.from(c).map((function(e){return e.toString(16).padStart(2,"0")})).join(" ")),e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:c.buffer,success:function(t){console.log("发送成功",t),e.showToast({title:"命令发送成功",icon:"success",duration:1e3})},fail:function(t){console.error("发送失败",t),e.showToast({title:"命令发送失败",icon:"error",duration:1e3})}}),c},crc8:function(e,t){for(var n=0,o=0;o<t;o++){n^=e[o];for(var c=0;c<8;c++)128&n?n=n<<1^7:n<<=1,n&=255}return 255&n},simpleSum:function(e){for(var t=0,n=0;n<e.length;n++)t+=e[n];return 255&t},xorChecksum:function(e){for(var t=0,n=0;n<e.length;n++)t^=e[n];return 255&t},queryDeviceStatus:function(){return console.log("发送：查询设备状态 (0x01)"),this.sendBleCommand(1)},querySensorData:function(){return console.log("发送：查询传感器数据 (0x02)"),this.sendBleCommand(2)},queryMaintenanceInfo:function(){return console.log("发送：查询保养信息 (0x03)"),this.sendBleCommand(3)},queryElectricalParameters:function(){return console.log("发送：查询电气参数 (0x04)"),this.sendBleCommand(4)},queryProtectionStatus:function(){return console.log("发送：查询保护状态 (0x05)"),this.sendBleCommand(5)},queryAirQuality:function(){return console.log("发送：查询空气质量 (0x06)"),this.sendBleCommand(6)},sendHeartbeat:function(){console.log("发送：心跳包 (0x07)");var t=this.sendBleCommand(7);return t&&e.showToast({title:"心跳包已发送",icon:"loading",duration:1e3}),t},setGear:function(e){return console.log("发送：设置挡位 (0x11), 挡位: ".concat(e)),this.sendBleCommand(17,[e])},controlBuzzer:function(e){return console.log("发送：控制蜂鸣器 (0x12), 状态: ".concat(e)),this.sendBleCommand(18,[e])},resetDevice:function(){return console.log("发送：复位设备 (0x13)"),this.sendBleCommand(19,[1])},resetTimer:function(){return console.log("发送：复位计时器 (0x14)"),this.sendBleCommand(20,[1])},resetProtectionStatus:function(e){return console.log("发送：复位保护状态 (0x15), 类型: ".concat(e)),this.sendBleCommand(21,[e])},setFanGear:function(e){return console.log("发送：档位控制 (0x16), 档位: ".concat(e)),this.sendBleCommand(22,[e])},controlBuzzerAdvanced:function(e,t){console.log("发送：蜂鸣器高级控制 (0x17), 频率: ".concat(e,"Hz, 持续时间: ").concat(t,"ms"));var n=[255&e,e>>8&255],o=[255&t,t>>8&255];return this.sendBleCommand(23,[].concat(n,o))},resetAnionCounter:function(){return console.log("发送：负离子计数器复位 (0x18)"),this.sendBleCommand(24,[1])},testRawData:function(){console.log("测试发送简单数据");var t=new Uint8Array([1,2,3,4]);this.connected&&this.deviceId&&this.serviceId&&this.characteristicId_write?e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:t.buffer,success:function(t){console.log("测试数据发送成功",t),e.showToast({title:"测试数据发送成功",icon:"success"})},fail:function(t){console.error("测试数据发送失败",t),e.showToast({title:"测试数据发送失败",icon:"error"})}}):e.showToast({title:"蓝牙未连接",icon:"none"})},clearReceivedData:function(){this.receivedMessages=[],e.showToast({title:"数据已清空",icon:"success",duration:1e3})},toggleBuzzer:function(){var t=this,n=!this.currentBuzzerState,o=n?1:0;console.log("快捷控制：切换蜂鸣器 ".concat(n?"开启":"关闭"," (0x12)"));var c=this.sendBleCommand(18,[o]);c&&(e.showToast({title:"正在".concat(n?"开启":"关闭","蜂鸣器..."),icon:"loading",duration:1e3}),setTimeout((function(){t.queryDeviceStatus()}),500))},setVoltageGear:function(t){var n=this;console.log("快捷控制：设置电压挡位 ".concat(t," (0x11)"));var o=this.sendBleCommand(17,[t]);if(o){var c=0===t?"关闭":"".concat(t,"挡");e.showToast({title:"正在设置为".concat(c,"..."),icon:"loading",duration:1e3}),setTimeout((function(){n.queryDeviceStatus()}),500)}},onBluetoothAdapterStateChange:function(t){console.log("蓝牙适配器状态变化:",t.available,t.discovering),this.isBluetoothAvailable=t.available,this.isScanning=t.discovering,t.available?console.log("蓝牙适配器已恢复可用"):(this.resetConnectionState(),this.devices=[],e.showToast({title:"蓝牙适配器不可用，请检查蓝牙是否开启",icon:"none"}))},onBluetoothDeviceFound:function(e){var t=this;e.devices.forEach((function(e){t.devices.some((function(t){return t.deviceId===e.deviceId}))||t.devices.push(e)}))},onBLEConnectionStateChange:function(t){console.log("BLE连接状态变化:",t.deviceId,t.connected),this.connected=t.connected,t.connected||(e.showToast({title:"设备 ".concat(t.deviceId," 已断开连接"),icon:"none"}),this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="")},onBLECharacteristicValueChange:function(e){console.log("--- onBLECharacteristicValueChange triggered ---",e);var t=new Uint8Array(e.value),n=Array.prototype.map.call(t,(function(e){return("00"+e.toString(16)).slice(-2)})).join(" "),o="收到通知: CharacteristicId ".concat(e.characteristicId,", Value: [").concat(t,"], Hex: ").concat(n);this.receivedMessages.push(o),console.log("接收数据的特征值UUID:",e.characteristicId),console.log("接收到的原始数据:",n),this.receiveDataFromBle(e.value)},initBluetoothAdapter:function(){var t=this;e.openBluetoothAdapter({success:function(n){console.log("initBluetoothAdapter success",n),t.isBluetoothAvailable=!0,e.showToast({title:"蓝牙适配器初始化成功",icon:"success"})},fail:function(n){console.error("initBluetoothAdapter fail",n),t.isBluetoothAvailable=!1,e.showToast({title:"蓝牙适配器初始化失败，请检查蓝牙是否打开",icon:"none"})}})},startScan:function(){var t=this;this.isBluetoothAvailable?e.startBluetoothDevicesDiscovery({services:[],allowDuplicatesKey:!0,interval:300,powerLevel:"high",success:function(n){console.log("startBluetoothDevicesDiscovery success",n),t.isScanning=!0,t.devices=[],e.onBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"开始扫描蓝牙设备",icon:"success"})},fail:function(n){console.error("startBluetoothDevicesDiscovery fail",n),t.isScanning=!1,e.showToast({title:"扫描失败",icon:"error"})}}):e.showToast({title:"蓝牙适配器不可用",icon:"none"})},stopScan:function(){var t=this;e.stopBluetoothDevicesDiscovery({success:function(n){console.log("stopBluetoothDevicesDiscovery success",n),t.isScanning=!1,e.offBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"停止扫描",icon:"success"})},fail:function(t){console.error("stopBluetoothDevicesDiscovery fail",t),e.showToast({title:"停止扫描失败",icon:"error"})}})},connectDevice:function(e){this.connectDeviceWithRetry(e,0)},connectDeviceWithRetry:function(t,n){var o=this,c=3;e.showLoading({title:"连接中".concat(n>0?" (重试".concat(n,"/").concat(c,")"):"","...")}),this.isScanning&&this.stopScan(),this.connected&&this.deviceId&&(console.log("断开之前的连接..."),e.closeBLEConnection({deviceId:this.deviceId,success:function(){return console.log("之前连接已断开")},fail:function(){return console.log("断开之前连接失败，继续新连接")}})),setTimeout((function(){n>1?(console.log("尝试重置蓝牙适配器..."),e.closeBluetoothAdapter({success:function(){console.log("蓝牙适配器已关闭，准备重新打开"),setTimeout((function(){e.openBluetoothAdapter({success:function(){console.log("蓝牙适配器已重新打开"),o.doCreateBLEConnection(t,n)},fail:function(){console.error("重新打开蓝牙适配器失败"),e.hideLoading(),e.showToast({title:"蓝牙初始化失败",icon:"none"})}})}),1e3)},fail:function(){console.error("关闭蓝牙适配器失败"),o.doCreateBLEConnection(t,n)}})):o.doCreateBLEConnection(t,n)}),n>0?1e3:500)},doCreateBLEConnection:function(t,n){var o=this,c=3;e.createBLEConnection({deviceId:t,timeout:15e3,success:function(n){console.log("createBLEConnection success",n),o.connected=!0,o.deviceId=t;var c=o.devices.find((function(e){return e.deviceId===t}));c&&(o.deviceName=c.name||"未知设备"),e.hideLoading(),e.showToast({title:"连接成功",icon:"success"}),o.onConnectionSuccess(),o.getServicesAndCharacteristics(t)},fail:function(i){console.error("createBLEConnection fail",i),o.connected=!1;var s="连接失败";10003===i.errCode?s="设备连接失败，可能设备忙碌或距离过远":10012===i.errCode?s="连接超时，请检查设备是否开启":10001===i.errCode&&(s="蓝牙适配器未初始化"),n<c?(e.hideLoading(),e.showToast({title:"".concat(s,"，").concat(2,"秒后重试..."),icon:"none",duration:1500}),setTimeout((function(){o.connectDeviceWithRetry(t,n+1)}),2e3)):(e.hideLoading(),e.showModal({title:"连接失败",content:"".concat(s,"\n\n建议操作：\n1. 确认设备已开启且在附近\n2. 重启设备蓝牙功能\n3. 清除设备缓存后重试\n4. 检查设备是否被其他应用占用"),showCancel:!0,cancelText:"取消",confirmText:"重试",success:function(e){e.confirm&&o.connectDeviceWithRetry(t,0)}}))}})},disconnectDevice:function(){var t=this;this.deviceId?e.closeBLEConnection({deviceId:this.deviceId,success:function(n){console.log("closeBLEConnection success",n),t.resetConnectionState(),e.showToast({title:"断开连接成功",icon:"success"})},fail:function(n){console.error("closeBLEConnection fail",n),t.resetConnectionState(),e.showToast({title:"断开连接失败，已重置状态",icon:"none"})}}):e.showToast({title:"没有连接的设备",icon:"none"})},resetConnectionState:function(){this.connected=!1,this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="",this.receivedMessages=[],this.currentBuzzerState=!1,this.currentVoltageGear=0,this.currentAirQuality={level:0,text:"未知",color:"#999",icon:"❓"},this.sensorData={pm25:0,voc:0,anionConcentration:0,temperature:0,humidity:0,lastUpdate:0},this.stopConnectionMonitor(),this.reconnectAttempts=0,this.lastHeartbeatTime=0},clearBluetoothCache:function(){},showConnectionTips:function(){e.showModal({title:"蓝牙连接帮助",content:'连接失败的常见原因和解决方法：\n\n1. 设备距离过远\n   → 靠近设备重试\n\n2. 设备被其他应用占用\n   → 关闭其他蓝牙应用\n\n3. 蓝牙缓存问题\n   → 点击"清理缓存"按钮\n\n4. 设备蓝牙故障\n   → 重启设备蓝牙功能\n\n5. 系统蓝牙异常\n   → 重启手机蓝牙\n\n错误码说明：\n• 10003: 连接失败，设备忙碌\n• 10012: 连接超时\n• 10001: 蓝牙未初始化',showCancel:!1,confirmText:"知道了"})},startConnectionMonitor:function(){var e=this;this.connectionMonitorTimer&&clearInterval(this.connectionMonitorTimer),this.connectionMonitorTimer=setInterval((function(){if(e.connected&&e.deviceId){e.sendHeartbeat();var t=Date.now();e.lastHeartbeatTime>0&&t-e.lastHeartbeatTime>6e4&&(console.warn("心跳超时，可能连接已断开"),e.handleConnectionLost())}}),3e4)},stopConnectionMonitor:function(){this.connectionMonitorTimer&&(clearInterval(this.connectionMonitorTimer),this.connectionMonitorTimer=null)},handleConnectionLost:function(){var t=this;console.log("检测到连接丢失"),this.connected=!1,this.autoReconnectEnabled&&this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("尝试自动重连 (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),e.showToast({title:"连接丢失，正在重连 (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")"),icon:"loading",duration:2e3}),setTimeout((function(){t.deviceId&&t.connectDeviceWithRetry(t.deviceId,0)}),3e3)):(e.showToast({title:"连接已断开",icon:"error"}),this.resetConnectionState())},onConnectionSuccess:function(){this.reconnectAttempts=0,this.lastHeartbeatTime=Date.now(),this.startConnectionMonitor()},debugModeConnect:function(){var t=this;if(0!==this.devices.length){var n=this.devices[0];e.showModal({title:"调试模式连接",content:"将使用调试工具的连接方式连接到设备：\n".concat(n.name||"未知设备","\n").concat(n.deviceId),success:function(e){e.confirm&&t.debugConnect(n.deviceId)}})}else e.showToast({title:"请先扫描设备",icon:"none"})},debugConnect:function(t){var n=this;console.log("=== 开始调试模式连接 ==="),e.showLoading({title:"调试连接中..."}),e.closeBluetoothAdapter({complete:function(){console.log("蓝牙适配器已关闭"),setTimeout((function(){e.openBluetoothAdapter({mode:"central",success:function(){console.log("蓝牙适配器重新初始化成功"),setTimeout((function(){n.directConnect(t)}),500)},fail:function(t){console.error("重新初始化蓝牙适配器失败",t),e.hideLoading(),e.showToast({title:"蓝牙初始化失败",icon:"error"})}})}),1e3)}})},directConnect:function(t){var n=this;console.log("开始直接连接:",t),e.createBLEConnection({deviceId:t,timeout:2e4,success:function(o){console.log("=== 调试连接成功 ===",o),n.connected=!0,n.deviceId=t;var c=n.devices.find((function(e){return e.deviceId===t}));c&&(n.deviceName=c.name||"未知设备"),e.hideLoading(),e.showToast({title:"调试连接成功！",icon:"success"}),n.onConnectionSuccess(),setTimeout((function(){n.getServicesAndCharacteristics(t)}),1e3)},fail:function(t){console.error("=== 调试连接失败 ===",t),e.hideLoading();var n="调试连接失败\n错误码: ".concat(t.errCode,"\n错误信息: ").concat(t.errMsg);10003===t.errCode&&(n+="\n\n可能原因：\n1. 设备已被其他应用连接\n2. 设备蓝牙栈异常\n3. 系统蓝牙缓存问题"),e.showModal({title:"调试连接失败",content:n,showCancel:!1})}})},getServicesAndCharacteristics:function(t){var n=this;e.getBLEDeviceServices({deviceId:t,success:function(o){console.log("getBLEDeviceServices success",o.services);var c=o.services.find((function(e){return e.uuid.toUpperCase().includes("0000FF00")}));c?(n.serviceId=c.uuid,e.getBLEDeviceCharacteristics({deviceId:t,serviceId:c.uuid,success:function(o){console.log("getBLEDeviceCharacteristics success",o.characteristics),o.characteristics.forEach((function(e,t){console.log("特征值".concat(t,": UUID=").concat(e.uuid,", 属性="),e.properties)}));var i=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.write}));i||(i=o.characteristics.find((function(e){return e.properties.write})),i&&console.log("未找到0xFF02，使用其他可写特征值:",i.uuid));var s=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.notify}));s&&console.log("找到协议定义的通知特征值:",s.uuid),i?(n.characteristicId_write=i.uuid,console.log("找到可写特征值 (FF02):",n.characteristicId_write)):console.warn("未找到可写特征值 (0x0000FF02)"),s?(n.characteristicId_notify=s.uuid,console.log("找到可通知特征值:",n.characteristicId_notify),setTimeout((function(){n.notifyCharacteristicValueChange(t,c.uuid,s.uuid)}),200)):console.warn("未找到可通知特征值 (0x0000FF02)"),e.showToast({title:"服务和特征值发现成功",icon:"success"})},fail:function(t){console.error("getBLEDeviceCharacteristics fail",t),e.showToast({title:"获取特征值失败",icon:"error"})}})):(console.warn("未找到服务 0x0000FF00"),e.showToast({title:"未找到指定服务",icon:"none"}))},fail:function(t){console.error("getBLEDeviceServices fail",t),e.showToast({title:"获取服务失败",icon:"error"})}})},notifyCharacteristicValueChange:function(t,n,o){e.notifyBLECharacteristicValueChange({state:!0,deviceId:t,serviceId:n,characteristicId:o,success:function(t){console.log("notifyBLECharacteristicValueChange success",t.errMsg),e.showToast({title:"已订阅通知",icon:"success"})},fail:function(t){console.error("notifyBLECharacteristicValueChange fail",t),e.showToast({title:"订阅通知失败",icon:"error"})}})},updateAirQualityDisplay:function(e){var t={level:e,text:"未知",color:"#999",icon:"❓"};switch(e){case 1:t={level:e,text:"优",color:"#00C851",icon:"😊"};break;case 2:t={level:e,text:"良",color:"#ffbb33",icon:"🙂"};break;case 3:t={level:e,text:"轻度污染",color:"#ff8800",icon:"😐"};break;case 4:t={level:e,text:"中度污染",color:"#ff4444",icon:"😷"};break;case 5:t={level:e,text:"重度污染",color:"#CC0000",icon:"😵"};break}this.currentAirQuality=t},formatTime:function(e){if(!e)return"--:--";var t=new Date(e),n=t.getHours().toString().padStart(2,"0"),o=t.getMinutes().toString().padStart(2,"0"),c=t.getSeconds().toString().padStart(2,"0");return"".concat(n,":").concat(o,":").concat(c)}}};t.default=i}).call(this,n(2)["default"])},39:function(e,t,n){"use strict";n.r(t);var o=n(40),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},40:function(e,t,n){}},[[33,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map