(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{33:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var c=o(n(34));e.__webpack_require_UNI_MP_PLUGIN__=n,t(c.default)}).call(this,n(1)["default"],n(2)["createPage"])},34:function(e,t,n){"use strict";n.r(t);var o=n(35),c=n(37);for(var i in c)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return c[e]}))}(i);n(39);var s,a=n(32),r=Object(a["default"])(c["default"],o["render"],o["staticRenderFns"],!1,null,"57280228",null,!1,o["components"],s);r.options.__file="pages/index/index.vue",t["default"]=r.exports},35:function(e,t,n){"use strict";n.r(t);var o=n(36);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},36:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return c})),n.d(t,"staticRenderFns",(function(){return s})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return o}));var c=function(){var e=this,t=e.$createElement,n=(e._self._c,e.devices.length),o=e.devices.length,c=e.receivedMessages.length;e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:c}})},i=!1,s=[];c._withStripped=!0},37:function(e,t,n){"use strict";n.r(t);var o=n(38),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},38:function(e,t,n){"use strict";(function(e){function n(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=o(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var c=0,i=function(){};return{s:i,n:function(){return c>=e.length?{done:!0}:{done:!1,value:e[c++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,r=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){r=!0,s=e},f:function(){try{a||null==n.return||n.return()}finally{if(r)throw s}}}}function o(e,t){if(e){if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isConnect:0,deviceId:"",serviceId:"",notifyId:"",receivedTxt:[],receiveBuf:[],sentWiFiTxt:"ZHHC",sentPwdTxt:"666888999",isBluetoothAvailable:!1,isScanning:!1,connected:!1,devices:[],deviceName:"",characteristicId_write:"",characteristicId_notify:"",receivedMessages:[],gearValue:0,buzzerStatus:0,protectionResetType:1,fanGear:0,buzzerFrequency:1e3,buzzerDuration:100,currentBuzzerState:!1,currentVoltageGear:0,connectionMonitorTimer:null,lastHeartbeatTime:0,autoReconnectEnabled:!0,reconnectAttempts:0,maxReconnectAttempts:5}},onLoad:function(){this.initBluetoothAdapter(),e.onBluetoothAdapterStateChange(this.onBluetoothAdapterStateChange),e.onBLEConnectionStateChange(this.onBLEConnectionStateChange),e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),e.onBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onLoad中重新注册")},onShow:function(){},onUnload:function(){e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onUnload中移除"),this.stopConnectionMonitor(),this.connected&&this.deviceId&&e.closeBLEConnection({deviceId:this.deviceId,success:function(){return console.log("页面卸载时已断开蓝牙连接")},fail:function(){return console.log("页面卸载时断开蓝牙连接失败")}})},methods:{ceshi:function(){e.redirectTo({url:"/pages/index/ceshi"})},receiveDataFromBle:function(e){console.log("--- receiveDataFromBle entered with data ---",new Uint8Array(e));var t=new Uint8Array(e),o=Array.from(t).map((function(e){return e.toString(16).padStart(2,"0")})).join(" "),c="📡 ".concat(o);if(this.receivedMessages.push(c),!(t.length<5||170!==t[0]||85!==t[1])){var i=t[2],s=t[3],a=t[t.length-1];if(t.length===5+s){var r=t.slice(2,t.length-1),l=t.slice(0,t.length-1),u=this.crc8(r,r.length),h=this.crc8(l,l.length),d=255&this.simpleSum(r),f=255&this.simpleSum(l),v=this.xorChecksum(r),g=[];135===t[2]&&(g.push({name:"心跳SUM1",value:134}),g.push({name:"心跳CMD",value:135}),g.push({name:"心跳FIXED",value:60}),g.push({name:"心跳NOT",value:120}),g.push({name:"心跳SPEC1",value:60}),g.push({name:"心跳SPEC2",value:44})),console.log("校验和测试:"),console.log("  CRC-8(cmd+data): 0x".concat(u.toString(16).padStart(2,"0"))),console.log("  CRC-8(all): 0x".concat(h.toString(16).padStart(2,"0"))),console.log("  SUM(cmd+data): 0x".concat(d.toString(16).padStart(2,"0"))),console.log("  SUM(all): 0x".concat(f.toString(16).padStart(2,"0"))),console.log("  XOR(cmd+data): 0x".concat(v.toString(16).padStart(2,"0"))),g.length>0&&g.forEach((function(e){console.log("  ".concat(e.name,": 0x").concat(e.value.toString(16).padStart(2,"0")))})),console.log("  接收到的: 0x".concat(a.toString(16).padStart(2,"0")));var m=!1,C="";if(u===a)m=!0,C="CRC-8(cmd+data)";else if(h===a)m=!0,C="CRC-8(all)";else if(d===a)m=!0,C="SUM(cmd+data)";else if(f===a)m=!0,C="SUM(all)";else if(v===a)m=!0,C="XOR(cmd+data)";else{var p,w=n(g);try{for(w.s();!(p=w.n()).done;){var b=p.value;if(b.value===a){m=!0,C=b.name;break}}}catch(y){w.e(y)}finally{w.f()}}if(m){console.log("✅ 校验和匹配，使用方式: ".concat(C));var B="✅ 校验和验证成功 (".concat(C,")");this.receivedMessages.push(B)}else{console.warn("所有校验方式都不匹配，跳过校验继续处理");var T="⚠️ 校验和不匹配，但继续处理数据";this.receivedMessages.push(T)}var S=t.slice(4,4+s);console.log("接收到有效数据包:",t),console.log("命令字:",i.toString(16)),console.log("数据长度:",s),console.log("数据区:",S),this.decodeDataFromBle(i,S)}}},bytesToUint16:function(e,t){return e[t+1]<<8|e[t]},bytesToUint32:function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},decodeDataFromBle:function(t,n){switch(console.log("接收到命令: 0x"+t.toString(16),"数据:",n),t){case 129:case 161:if(n.length>=2){var o=n[0],c=n[1],i=1&o?"打开":"关闭",s=(6&o)>>1,a="";0===s?a="关闭":1===s?a="低挡":2===s?a="中挡":3===s&&(a="高挡");var r=8&o?"打开":"关闭",l=16&o?"异常":"正常",u=3&c,h="";0===u?h="正常模式":1===u?h="节能模式":2===u&&(h="强力模式");var d=this.currentBuzzerState,f=this.currentVoltageGear,v=!!(8&o),g=s;this.currentBuzzerState=v,this.currentVoltageGear=g,console.log("设备状态: 开关: ".concat(i,", 挡位: ").concat(a,", 蜂鸣器: ").concat(r,", 运行: ").concat(l,", 工作模式: ").concat(h));var m="📊 设备状态更新: 挡位=".concat(a,", 蜂鸣器=").concat(r,", 运行=").concat(l);if(this.receivedMessages.push(m),d!==v&&e.showToast({title:"蜂鸣器已".concat(v?"开启":"关闭"),icon:"success",duration:1500}),f!==g){var C=0===g?"关闭":"".concat(g,"挡");e.showToast({title:"电压挡位已设置为".concat(C),icon:"success",duration:1500})}}else console.warn("设备状态响应数据长度不足");break;case 130:case 162:if(n.length>=10){var p=this.bytesToUint16(n,0),w=this.bytesToUint16(n,2),b=this.bytesToUint16(n,4),B=this.bytesToUint16(n,6)/10,T=this.bytesToUint16(n,8)/10;console.log("传感器数据: PM2.5: ".concat(p,"μg/m³, VOC: ").concat(w,"ppm, 负离子浓度: ").concat(b,"个/cm³, 温度: ").concat(B,"°C, 湿度: ").concat(T,"%RH"))}else console.warn("传感器数据响应数据长度不足");break;case 131:if(n.length>=4){var S=this.bytesToUint32(n,0);console.log("保养信息: 已使用时间: ".concat(S,"小时"))}else console.warn("保养信息响应数据长度不足");break;case 132:if(n.length>=6){var y=this.bytesToUint16(n,0)/100,I=this.bytesToUint16(n,2)/1e3,x=this.bytesToUint16(n,4)/10;console.log("电气参数: 电压: ".concat(y,"V, 电流: ").concat(I,"A, 功率: ").concat(x,"W"))}else console.warn("电气参数响应数据长度不足");break;case 133:case 165:if(n.length>=1){var D=n[0],E=1&D?"已触发":"未触发",A=2&D?"已触发":"未触发",L=4&D?"已触发":"未触发",k=8&D?"已触发":"未触发";console.log("保护状态: 短路: ".concat(E,", 过流: ").concat(A,", 低压: ").concat(L,", 过温: ").concat(k))}else console.warn("保护状态响应数据长度不足");break;case 134:case 166:if(n.length>=1){var M=n[0],U="";switch(M){case 1:U="优";break;case 2:U="良";break;case 3:U="轻度污染";break;case 4:U="中度污染";break;case 5:U="重度污染";break;default:U="未知";break}console.log("空气质量: ".concat(U))}else console.warn("空气质量响应数据长度不足");break;case 135:console.log("✅ 心跳响应成功"),this.lastHeartbeatTime=Date.now();var F="💓 心跳响应成功 - 设备连接正常";this.receivedMessages.push(F),e.showToast({title:"心跳响应成功",icon:"success",duration:1e3});break;case 145:case 146:case 147:case 148:case 149:case 150:case 151:case 152:if(n.length>=1){var _=n[0],R="0x".concat(t.toString(16));1===_?(console.log("".concat(R," 命令执行成功")),e.showToast({title:"".concat(R," 命令执行成功"),icon:"none"})):(console.log("".concat(R," 命令执行失败")),e.showToast({title:"".concat(R," 命令执行失败"),icon:"none"}))}else console.warn("控制命令响应数据长度不足");break;case 137:if(n.length>=5){var V=this.bytesToUint32(n,0),z=n[4],H="";switch(z){case 0:H="不支持";break;case 1:H="正常";break;case 2:H="故障";break;default:H="未知";break}console.log("负离子数据: 计数值: ".concat(V,"个/cm³, 传感器状态: ").concat(H))}else console.warn("负离子数据响应数据长度不足");break;case 138:if(n.length>=8){var O=n[0],P="";switch(O){case 0:P="停机状态";break;case 1:P="正常工作";break;case 2:P="待机状态";break;case 3:P="保护状态";break;case 4:P="故障状态";break;default:P="未知";break}var q=1===n[1]?"正常":"异常",j=1===n[2]?"正常":"异常",G=this.bytesToUint32(n,3);console.log("设备工作状态: ".concat(P,", 电压状态: ").concat(q,", 电流状态: ").concat(j,", 累计工作时间: ").concat(G,"秒"))}else console.warn("设备工作状态详细响应数据长度不足");break;case 143:if(n.length>=2){var N=n[0],W=n[1],$="";switch(W){case 1:$="参数错误";break;case 2:$="命令不支持";break;case 3:$="硬件错误";break;default:$="未知错误";break}console.warn("错误响应: 原始命令字: 0x".concat(N.toString(16),", 错误代码: 0x").concat(W.toString(16)," (").concat($,")"))}else console.warn("错误响应数据长度不足");break;case 163:n.length>=1&&1===n[0]?(console.log("保养提醒: 需要更换滤芯"),e.showToast({title:"需要更换滤芯",icon:"none"})):console.warn("保养提醒通知数据异常或长度不足");break;default:console.log("未知命令: 0x"+t.toString(16));break}},sendBleCommand:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!this.connected||!this.deviceId||!this.serviceId||!this.characteristicId_write)return e.showToast({title:"蓝牙未连接或特征值不可用",icon:"none"}),void console.warn("发送失败：蓝牙未连接或特征值不可用");var o=n.length,c=new Uint8Array(5+o);c[0]=170,c[1]=85,c[2]=t,c[3]=o;for(var i=0;i<o;i++)c[4+i]=n[i];var s=c.slice(2,4+o),a=this.crc8(s,s.length);return c[4+o]=a,console.log("准备发送BLE命令包:",Array.from(c).map((function(e){return e.toString(16).padStart(2,"0")})).join(" ")),e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:c.buffer,success:function(t){console.log("发送成功",t),e.showToast({title:"命令发送成功",icon:"success",duration:1e3})},fail:function(t){console.error("发送失败",t),e.showToast({title:"命令发送失败",icon:"error",duration:1e3})}}),c},crc8:function(e,t){for(var n=0,o=0;o<t;o++){n^=e[o];for(var c=0;c<8;c++)128&n?n=n<<1^7:n<<=1,n&=255}return 255&n},simpleSum:function(e){for(var t=0,n=0;n<e.length;n++)t+=e[n];return 255&t},xorChecksum:function(e){for(var t=0,n=0;n<e.length;n++)t^=e[n];return 255&t},queryDeviceStatus:function(){return console.log("发送：查询设备状态 (0x01)"),this.sendBleCommand(1)},querySensorData:function(){return console.log("发送：查询传感器数据 (0x02)"),this.sendBleCommand(2)},queryMaintenanceInfo:function(){return console.log("发送：查询保养信息 (0x03)"),this.sendBleCommand(3)},queryElectricalParameters:function(){return console.log("发送：查询电气参数 (0x04)"),this.sendBleCommand(4)},queryProtectionStatus:function(){return console.log("发送：查询保护状态 (0x05)"),this.sendBleCommand(5)},queryAirQuality:function(){return console.log("发送：查询空气质量 (0x06)"),this.sendBleCommand(6)},sendHeartbeat:function(){console.log("发送：心跳包 (0x07)");var t=this.sendBleCommand(7);return t&&e.showToast({title:"心跳包已发送",icon:"loading",duration:1e3}),t},setGear:function(e){return console.log("发送：设置挡位 (0x11), 挡位: ".concat(e)),this.sendBleCommand(17,[e])},controlBuzzer:function(e){return console.log("发送：控制蜂鸣器 (0x12), 状态: ".concat(e)),this.sendBleCommand(18,[e])},resetDevice:function(){return console.log("发送：复位设备 (0x13)"),this.sendBleCommand(19,[1])},resetTimer:function(){return console.log("发送：复位计时器 (0x14)"),this.sendBleCommand(20,[1])},resetProtectionStatus:function(e){return console.log("发送：复位保护状态 (0x15), 类型: ".concat(e)),this.sendBleCommand(21,[e])},setFanGear:function(e){return console.log("发送：档位控制 (0x16), 档位: ".concat(e)),this.sendBleCommand(22,[e])},controlBuzzerAdvanced:function(e,t){console.log("发送：蜂鸣器高级控制 (0x17), 频率: ".concat(e,"Hz, 持续时间: ").concat(t,"ms"));var n=[255&e,e>>8&255],o=[255&t,t>>8&255];return this.sendBleCommand(23,[].concat(n,o))},resetAnionCounter:function(){return console.log("发送：负离子计数器复位 (0x18)"),this.sendBleCommand(24,[1])},testRawData:function(){console.log("测试发送简单数据");var t=new Uint8Array([1,2,3,4]);this.connected&&this.deviceId&&this.serviceId&&this.characteristicId_write?e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:t.buffer,success:function(t){console.log("测试数据发送成功",t),e.showToast({title:"测试数据发送成功",icon:"success"})},fail:function(t){console.error("测试数据发送失败",t),e.showToast({title:"测试数据发送失败",icon:"error"})}}):e.showToast({title:"蓝牙未连接",icon:"none"})},clearReceivedData:function(){this.receivedMessages=[],e.showToast({title:"数据已清空",icon:"success",duration:1e3})},toggleBuzzer:function(){var t=this,n=!this.currentBuzzerState,o=n?1:0;console.log("快捷控制：切换蜂鸣器 ".concat(n?"开启":"关闭"," (0x12)"));var c=this.sendBleCommand(18,[o]);c&&(e.showToast({title:"正在".concat(n?"开启":"关闭","蜂鸣器..."),icon:"loading",duration:1e3}),setTimeout((function(){t.queryDeviceStatus()}),500))},setVoltageGear:function(t){var n=this;console.log("快捷控制：设置电压挡位 ".concat(t," (0x11)"));var o=this.sendBleCommand(17,[t]);if(o){var c=0===t?"关闭":"".concat(t,"挡");e.showToast({title:"正在设置为".concat(c,"..."),icon:"loading",duration:1e3}),setTimeout((function(){n.queryDeviceStatus()}),500)}},onBluetoothAdapterStateChange:function(t){console.log("蓝牙适配器状态变化:",t.available,t.discovering),this.isBluetoothAvailable=t.available,this.isScanning=t.discovering,t.available?console.log("蓝牙适配器已恢复可用"):(this.resetConnectionState(),this.devices=[],e.showToast({title:"蓝牙适配器不可用，请检查蓝牙是否开启",icon:"none"}))},onBluetoothDeviceFound:function(e){var t=this;e.devices.forEach((function(e){t.devices.some((function(t){return t.deviceId===e.deviceId}))||t.devices.push(e)}))},onBLEConnectionStateChange:function(t){console.log("BLE连接状态变化:",t.deviceId,t.connected),this.connected=t.connected,t.connected||(e.showToast({title:"设备 ".concat(t.deviceId," 已断开连接"),icon:"none"}),this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="")},onBLECharacteristicValueChange:function(e){console.log("--- onBLECharacteristicValueChange triggered ---",e);var t=new Uint8Array(e.value),n=Array.prototype.map.call(t,(function(e){return("00"+e.toString(16)).slice(-2)})).join(" "),o="收到通知: CharacteristicId ".concat(e.characteristicId,", Value: [").concat(t,"], Hex: ").concat(n);this.receivedMessages.push(o),console.log("接收数据的特征值UUID:",e.characteristicId),console.log("接收到的原始数据:",n),this.receiveDataFromBle(e.value)},initBluetoothAdapter:function(){var t=this;e.openBluetoothAdapter({success:function(n){console.log("initBluetoothAdapter success",n),t.isBluetoothAvailable=!0,e.showToast({title:"蓝牙适配器初始化成功",icon:"success"})},fail:function(n){console.error("initBluetoothAdapter fail",n),t.isBluetoothAvailable=!1,e.showToast({title:"蓝牙适配器初始化失败，请检查蓝牙是否打开",icon:"none"})}})},startScan:function(){var t=this;this.isBluetoothAvailable?e.startBluetoothDevicesDiscovery({allowDuplicatesKey:!1,interval:0,success:function(n){console.log("startBluetoothDevicesDiscovery success",n),t.isScanning=!0,t.devices=[],e.onBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"开始扫描蓝牙设备",icon:"success"})},fail:function(n){console.error("startBluetoothDevicesDiscovery fail",n),t.isScanning=!1,e.showToast({title:"扫描失败",icon:"error"})}}):e.showToast({title:"蓝牙适配器不可用",icon:"none"})},stopScan:function(){var t=this;e.stopBluetoothDevicesDiscovery({success:function(n){console.log("stopBluetoothDevicesDiscovery success",n),t.isScanning=!1,e.offBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"停止扫描",icon:"success"})},fail:function(t){console.error("stopBluetoothDevicesDiscovery fail",t),e.showToast({title:"停止扫描失败",icon:"error"})}})},connectDevice:function(e){this.connectDeviceWithRetry(e,0)},connectDeviceWithRetry:function(t,n){var o=this,c=3,i=2e3;e.showLoading({title:"连接中".concat(n>0?" (重试".concat(n,"/").concat(c,")"):"","...")}),this.isScanning&&this.stopScan(),this.connected&&this.deviceId&&(console.log("断开之前的连接..."),e.closeBLEConnection({deviceId:this.deviceId,success:function(){return console.log("之前连接已断开")},fail:function(){return console.log("断开之前连接失败，继续新连接")}})),setTimeout((function(){e.createBLEConnection({deviceId:t,timeout:15e3,success:function(n){console.log("createBLEConnection success",n),o.connected=!0,o.deviceId=t;var c=o.devices.find((function(e){return e.deviceId===t}));c&&(o.deviceName=c.name||"未知设备"),e.hideLoading(),e.showToast({title:"连接成功",icon:"success"}),o.onConnectionSuccess(),o.getServicesAndCharacteristics(t)},fail:function(s){console.error("createBLEConnection fail",s),o.connected=!1;var a="连接失败";10003===s.errCode?a="设备连接失败，可能设备忙碌或距离过远":10012===s.errCode?a="连接超时，请检查设备是否开启":10001===s.errCode&&(a="蓝牙适配器未初始化"),n<c?(e.hideLoading(),e.showToast({title:"".concat(a,"，").concat(i/1e3,"秒后重试..."),icon:"none",duration:1500}),setTimeout((function(){o.connectDeviceWithRetry(t,n+1)}),i)):(e.hideLoading(),e.showModal({title:"连接失败",content:"".concat(a,"\n\n建议操作：\n1. 确认设备已开启且在附近\n2. 重启设备蓝牙功能\n3. 清除设备缓存后重试\n4. 检查设备是否被其他应用占用"),showCancel:!0,cancelText:"取消",confirmText:"重试",success:function(e){e.confirm&&o.connectDeviceWithRetry(t,0)}}))}})}),n>0?1e3:500)},disconnectDevice:function(){var t=this;this.deviceId?e.closeBLEConnection({deviceId:this.deviceId,success:function(n){console.log("closeBLEConnection success",n),t.resetConnectionState(),e.showToast({title:"断开连接成功",icon:"success"})},fail:function(n){console.error("closeBLEConnection fail",n),t.resetConnectionState(),e.showToast({title:"断开连接失败，已重置状态",icon:"none"})}}):e.showToast({title:"没有连接的设备",icon:"none"})},resetConnectionState:function(){this.connected=!1,this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="",this.receivedMessages=[],this.currentBuzzerState=!1,this.currentVoltageGear=0,this.stopConnectionMonitor(),this.reconnectAttempts=0,this.lastHeartbeatTime=0},clearBluetoothCache:function(){},showConnectionTips:function(){e.showModal({title:"蓝牙连接帮助",content:'连接失败的常见原因和解决方法：\n\n1. 设备距离过远\n   → 靠近设备重试\n\n2. 设备被其他应用占用\n   → 关闭其他蓝牙应用\n\n3. 蓝牙缓存问题\n   → 点击"清理缓存"按钮\n\n4. 设备蓝牙故障\n   → 重启设备蓝牙功能\n\n5. 系统蓝牙异常\n   → 重启手机蓝牙\n\n错误码说明：\n• 10003: 连接失败，设备忙碌\n• 10012: 连接超时\n• 10001: 蓝牙未初始化',showCancel:!1,confirmText:"知道了"})},startConnectionMonitor:function(){var e=this;this.connectionMonitorTimer&&clearInterval(this.connectionMonitorTimer),this.connectionMonitorTimer=setInterval((function(){if(e.connected&&e.deviceId){e.sendHeartbeat();var t=Date.now();e.lastHeartbeatTime>0&&t-e.lastHeartbeatTime>6e4&&(console.warn("心跳超时，可能连接已断开"),e.handleConnectionLost())}}),3e4)},stopConnectionMonitor:function(){this.connectionMonitorTimer&&(clearInterval(this.connectionMonitorTimer),this.connectionMonitorTimer=null)},handleConnectionLost:function(){var t=this;console.log("检测到连接丢失"),this.connected=!1,this.autoReconnectEnabled&&this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("尝试自动重连 (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")")),e.showToast({title:"连接丢失，正在重连 (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")"),icon:"loading",duration:2e3}),setTimeout((function(){t.deviceId&&t.connectDeviceWithRetry(t.deviceId,0)}),3e3)):(e.showToast({title:"连接已断开",icon:"error"}),this.resetConnectionState())},onConnectionSuccess:function(){this.reconnectAttempts=0,this.lastHeartbeatTime=Date.now(),this.startConnectionMonitor()},getServicesAndCharacteristics:function(t){var n=this;e.getBLEDeviceServices({deviceId:t,success:function(o){console.log("getBLEDeviceServices success",o.services);var c=o.services.find((function(e){return e.uuid.toUpperCase().includes("0000FF00")}));c?(n.serviceId=c.uuid,e.getBLEDeviceCharacteristics({deviceId:t,serviceId:c.uuid,success:function(o){console.log("getBLEDeviceCharacteristics success",o.characteristics),o.characteristics.forEach((function(e,t){console.log("特征值".concat(t,": UUID=").concat(e.uuid,", 属性="),e.properties)}));var i=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.write}));i||(i=o.characteristics.find((function(e){return e.properties.write})),i&&console.log("未找到0xFF02，使用其他可写特征值:",i.uuid));var s=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.notify}));s&&console.log("找到协议定义的通知特征值:",s.uuid),i?(n.characteristicId_write=i.uuid,console.log("找到可写特征值 (FF02):",n.characteristicId_write)):console.warn("未找到可写特征值 (0x0000FF02)"),s?(n.characteristicId_notify=s.uuid,console.log("找到可通知特征值:",n.characteristicId_notify),setTimeout((function(){n.notifyCharacteristicValueChange(t,c.uuid,s.uuid)}),200)):console.warn("未找到可通知特征值 (0x0000FF02)"),e.showToast({title:"服务和特征值发现成功",icon:"success"})},fail:function(t){console.error("getBLEDeviceCharacteristics fail",t),e.showToast({title:"获取特征值失败",icon:"error"})}})):(console.warn("未找到服务 0x0000FF00"),e.showToast({title:"未找到指定服务",icon:"none"}))},fail:function(t){console.error("getBLEDeviceServices fail",t),e.showToast({title:"获取服务失败",icon:"error"})}})},notifyCharacteristicValueChange:function(t,n,o){e.notifyBLECharacteristicValueChange({state:!0,deviceId:t,serviceId:n,characteristicId:o,success:function(t){console.log("notifyBLECharacteristicValueChange success",t.errMsg),e.showToast({title:"已订阅通知",icon:"success"})},fail:function(t){console.error("notifyBLECharacteristicValueChange fail",t),e.showToast({title:"订阅通知失败",icon:"error"})}})}}};t.default=i}).call(this,n(2)["default"])},39:function(e,t,n){"use strict";n.r(t);var o=n(40),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},40:function(e,t,n){}},[[33,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map