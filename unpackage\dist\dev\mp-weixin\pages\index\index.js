(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{33:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var c=o(n(34));e.__webpack_require_UNI_MP_PLUGIN__=n,t(c.default)}).call(this,n(1)["default"],n(2)["createPage"])},34:function(e,t,n){"use strict";n.r(t);var o=n(35),c=n(37);for(var i in c)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return c[e]}))}(i);n(39);var a,s=n(32),r=Object(s["default"])(c["default"],o["render"],o["staticRenderFns"],!1,null,"57280228",null,!1,o["components"],a);r.options.__file="pages/index/index.vue",t["default"]=r.exports},35:function(e,t,n){"use strict";n.r(t);var o=n(36);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},36:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return c})),n.d(t,"staticRenderFns",(function(){return a})),n.d(t,"recyclableRender",(function(){return i})),n.d(t,"components",(function(){return o}));var c=function(){var e=this,t=e.$createElement,n=(e._self._c,e.devices.length),o=e.devices.length,c=e.receivedMessages.length;e.$mp.data=Object.assign({},{$root:{g0:n,g1:o,g2:c}})},i=!1,a=[];c._withStripped=!0},37:function(e,t,n){"use strict";n.r(t);var o=n(38),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},38:function(e,t,n){"use strict";(function(e){function n(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=o(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var c=0,i=function(){};return{s:i,n:function(){return c>=e.length?{done:!0}:{done:!1,value:e[c++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,r=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){r=!0,a=e},f:function(){try{s||null==n.return||n.return()}finally{if(r)throw a}}}}function o(e,t){if(e){if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={data:function(){return{isConnect:0,deviceId:"",serviceId:"",notifyId:"",receivedTxt:[],receiveBuf:[],sentWiFiTxt:"ZHHC",sentPwdTxt:"666888999",isBluetoothAvailable:!1,isScanning:!1,connected:!1,devices:[],deviceName:"",characteristicId_write:"",characteristicId_notify:"",receivedMessages:[],gearValue:0,buzzerStatus:0,protectionResetType:1,fanGear:0,buzzerFrequency:1e3,buzzerDuration:100,currentBuzzerState:!1,currentVoltageGear:0}},onLoad:function(){this.initBluetoothAdapter(),e.onBluetoothAdapterStateChange(this.onBluetoothAdapterStateChange),e.onBLEConnectionStateChange(this.onBLEConnectionStateChange),e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),e.onBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onLoad中重新注册")},onShow:function(){},onUnload:function(){e.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange),console.log("onBLECharacteristicValueChange 监听器已在onUnload中移除")},methods:{ceshi:function(){e.redirectTo({url:"/pages/index/ceshi"})},receiveDataFromBle:function(e){console.log("--- receiveDataFromBle entered with data ---",new Uint8Array(e));var t=new Uint8Array(e),o=Array.from(t).map((function(e){return e.toString(16).padStart(2,"0")})).join(" "),c="📡 ".concat(o);if(this.receivedMessages.push(c),!(t.length<5||170!==t[0]||85!==t[1])){var i=t[2],a=t[3],s=t[t.length-1];if(t.length===5+a){var r=t.slice(2,t.length-1),l=t.slice(0,t.length-1),u=this.crc8(r,r.length),d=this.crc8(l,l.length),h=255&this.simpleSum(r),f=255&this.simpleSum(l),v=this.xorChecksum(r),g=[];135===t[2]&&(g.push({name:"心跳SUM1",value:134}),g.push({name:"心跳CMD",value:135}),g.push({name:"心跳FIXED",value:60}),g.push({name:"心跳NOT",value:120}),g.push({name:"心跳SPEC1",value:60}),g.push({name:"心跳SPEC2",value:44})),console.log("校验和测试:"),console.log("  CRC-8(cmd+data): 0x".concat(u.toString(16).padStart(2,"0"))),console.log("  CRC-8(all): 0x".concat(d.toString(16).padStart(2,"0"))),console.log("  SUM(cmd+data): 0x".concat(h.toString(16).padStart(2,"0"))),console.log("  SUM(all): 0x".concat(f.toString(16).padStart(2,"0"))),console.log("  XOR(cmd+data): 0x".concat(v.toString(16).padStart(2,"0"))),g.length>0&&g.forEach((function(e){console.log("  ".concat(e.name,": 0x").concat(e.value.toString(16).padStart(2,"0")))})),console.log("  接收到的: 0x".concat(s.toString(16).padStart(2,"0")));var m=!1,C="";if(u===s)m=!0,C="CRC-8(cmd+data)";else if(d===s)m=!0,C="CRC-8(all)";else if(h===s)m=!0,C="SUM(cmd+data)";else if(f===s)m=!0,C="SUM(all)";else if(v===s)m=!0,C="XOR(cmd+data)";else{var p,w=n(g);try{for(w.s();!(p=w.n()).done;){var B=p.value;if(B.value===s){m=!0,C=B.name;break}}}catch(I){w.e(I)}finally{w.f()}}if(m){console.log("✅ 校验和匹配，使用方式: ".concat(C));var b="✅ 校验和验证成功 (".concat(C,")");this.receivedMessages.push(b)}else{console.warn("所有校验方式都不匹配，跳过校验继续处理");var y="⚠️ 校验和不匹配，但继续处理数据";this.receivedMessages.push(y)}var S=t.slice(4,4+a);console.log("接收到有效数据包:",t),console.log("命令字:",i.toString(16)),console.log("数据长度:",a),console.log("数据区:",S),this.decodeDataFromBle(i,S)}}},bytesToUint16:function(e,t){return e[t+1]<<8|e[t]},bytesToUint32:function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},decodeDataFromBle:function(t,n){switch(console.log("接收到命令: 0x"+t.toString(16),"数据:",n),t){case 129:case 161:if(n.length>=2){var o=n[0],c=n[1],i=1&o?"打开":"关闭",a=(6&o)>>1,s="";0===a?s="关闭":1===a?s="低挡":2===a?s="中挡":3===a&&(s="高挡");var r=8&o?"打开":"关闭",l=16&o?"异常":"正常",u=3&c,d="";0===u?d="正常模式":1===u?d="节能模式":2===u&&(d="强力模式");var h=this.currentBuzzerState,f=this.currentVoltageGear,v=!!(8&o),g=a;this.currentBuzzerState=v,this.currentVoltageGear=g,console.log("设备状态: 开关: ".concat(i,", 挡位: ").concat(s,", 蜂鸣器: ").concat(r,", 运行: ").concat(l,", 工作模式: ").concat(d));var m="📊 设备状态更新: 挡位=".concat(s,", 蜂鸣器=").concat(r,", 运行=").concat(l);if(this.receivedMessages.push(m),h!==v&&e.showToast({title:"蜂鸣器已".concat(v?"开启":"关闭"),icon:"success",duration:1500}),f!==g){var C=0===g?"关闭":"".concat(g,"挡");e.showToast({title:"电压挡位已设置为".concat(C),icon:"success",duration:1500})}}else console.warn("设备状态响应数据长度不足");break;case 130:case 162:if(n.length>=10){var p=this.bytesToUint16(n,0),w=this.bytesToUint16(n,2),B=this.bytesToUint16(n,4),b=this.bytesToUint16(n,6)/10,y=this.bytesToUint16(n,8)/10;console.log("传感器数据: PM2.5: ".concat(p,"μg/m³, VOC: ").concat(w,"ppm, 负离子浓度: ").concat(B,"个/cm³, 温度: ").concat(b,"°C, 湿度: ").concat(y,"%RH"))}else console.warn("传感器数据响应数据长度不足");break;case 131:if(n.length>=4){var S=this.bytesToUint32(n,0);console.log("保养信息: 已使用时间: ".concat(S,"小时"))}else console.warn("保养信息响应数据长度不足");break;case 132:if(n.length>=6){var I=this.bytesToUint16(n,0)/100,T=this.bytesToUint16(n,2)/1e3,x=this.bytesToUint16(n,4)/10;console.log("电气参数: 电压: ".concat(I,"V, 电流: ").concat(T,"A, 功率: ").concat(x,"W"))}else console.warn("电气参数响应数据长度不足");break;case 133:case 165:if(n.length>=1){var D=n[0],E=1&D?"已触发":"未触发",k=2&D?"已触发":"未触发",L=4&D?"已触发":"未触发",U=8&D?"已触发":"未触发";console.log("保护状态: 短路: ".concat(E,", 过流: ").concat(k,", 低压: ").concat(L,", 过温: ").concat(U))}else console.warn("保护状态响应数据长度不足");break;case 134:case 166:if(n.length>=1){var F=n[0],A="";switch(F){case 1:A="优";break;case 2:A="良";break;case 3:A="轻度污染";break;case 4:A="中度污染";break;case 5:A="重度污染";break;default:A="未知";break}console.log("空气质量: ".concat(A))}else console.warn("空气质量响应数据长度不足");break;case 135:console.log("✅ 心跳响应成功");var _="💓 心跳响应成功 - 设备连接正常";this.receivedMessages.push(_),e.showToast({title:"心跳响应成功",icon:"success",duration:1e3});break;case 145:case 146:case 147:case 148:case 149:case 150:case 151:case 152:if(n.length>=1){var V=n[0],z="0x".concat(t.toString(16));1===V?(console.log("".concat(z," 命令执行成功")),e.showToast({title:"".concat(z," 命令执行成功"),icon:"none"})):(console.log("".concat(z," 命令执行失败")),e.showToast({title:"".concat(z," 命令执行失败"),icon:"none"}))}else console.warn("控制命令响应数据长度不足");break;case 137:if(n.length>=5){var M=this.bytesToUint32(n,0),R=n[4],O="";switch(R){case 0:O="不支持";break;case 1:O="正常";break;case 2:O="故障";break;default:O="未知";break}console.log("负离子数据: 计数值: ".concat(M,"个/cm³, 传感器状态: ").concat(O))}else console.warn("负离子数据响应数据长度不足");break;case 138:if(n.length>=8){var P=n[0],q="";switch(P){case 0:q="停机状态";break;case 1:q="正常工作";break;case 2:q="待机状态";break;case 3:q="保护状态";break;case 4:q="故障状态";break;default:q="未知";break}var j=1===n[1]?"正常":"异常",G=1===n[2]?"正常":"异常",N=this.bytesToUint32(n,3);console.log("设备工作状态: ".concat(q,", 电压状态: ").concat(j,", 电流状态: ").concat(G,", 累计工作时间: ").concat(N,"秒"))}else console.warn("设备工作状态详细响应数据长度不足");break;case 143:if(n.length>=2){var H=n[0],$=n[1],X="";switch($){case 1:X="参数错误";break;case 2:X="命令不支持";break;case 3:X="硬件错误";break;default:X="未知错误";break}console.warn("错误响应: 原始命令字: 0x".concat(H.toString(16),", 错误代码: 0x").concat($.toString(16)," (").concat(X,")"))}else console.warn("错误响应数据长度不足");break;case 163:n.length>=1&&1===n[0]?(console.log("保养提醒: 需要更换滤芯"),e.showToast({title:"需要更换滤芯",icon:"none"})):console.warn("保养提醒通知数据异常或长度不足");break;default:console.log("未知命令: 0x"+t.toString(16));break}},sendBleCommand:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(!this.connected||!this.deviceId||!this.serviceId||!this.characteristicId_write)return e.showToast({title:"蓝牙未连接或特征值不可用",icon:"none"}),void console.warn("发送失败：蓝牙未连接或特征值不可用");var o=n.length,c=new Uint8Array(5+o);c[0]=170,c[1]=85,c[2]=t,c[3]=o;for(var i=0;i<o;i++)c[4+i]=n[i];var a=c.slice(2,4+o),s=this.crc8(a,a.length);return c[4+o]=s,console.log("准备发送BLE命令包:",Array.from(c).map((function(e){return e.toString(16).padStart(2,"0")})).join(" ")),e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:c.buffer,success:function(t){console.log("发送成功",t),e.showToast({title:"命令发送成功",icon:"success",duration:1e3})},fail:function(t){console.error("发送失败",t),e.showToast({title:"命令发送失败",icon:"error",duration:1e3})}}),c},crc8:function(e,t){for(var n=0,o=0;o<t;o++){n^=e[o];for(var c=0;c<8;c++)128&n?n=n<<1^7:n<<=1,n&=255}return 255&n},simpleSum:function(e){for(var t=0,n=0;n<e.length;n++)t+=e[n];return 255&t},xorChecksum:function(e){for(var t=0,n=0;n<e.length;n++)t^=e[n];return 255&t},queryDeviceStatus:function(){return console.log("发送：查询设备状态 (0x01)"),this.sendBleCommand(1)},querySensorData:function(){return console.log("发送：查询传感器数据 (0x02)"),this.sendBleCommand(2)},queryMaintenanceInfo:function(){return console.log("发送：查询保养信息 (0x03)"),this.sendBleCommand(3)},queryElectricalParameters:function(){return console.log("发送：查询电气参数 (0x04)"),this.sendBleCommand(4)},queryProtectionStatus:function(){return console.log("发送：查询保护状态 (0x05)"),this.sendBleCommand(5)},queryAirQuality:function(){return console.log("发送：查询空气质量 (0x06)"),this.sendBleCommand(6)},sendHeartbeat:function(){console.log("发送：心跳包 (0x07)");var t=this.sendBleCommand(7);return t&&e.showToast({title:"心跳包已发送",icon:"loading",duration:1e3}),t},setGear:function(e){return console.log("发送：设置挡位 (0x11), 挡位: ".concat(e)),this.sendBleCommand(17,[e])},controlBuzzer:function(e){return console.log("发送：控制蜂鸣器 (0x12), 状态: ".concat(e)),this.sendBleCommand(18,[e])},resetDevice:function(){return console.log("发送：复位设备 (0x13)"),this.sendBleCommand(19,[1])},resetTimer:function(){return console.log("发送：复位计时器 (0x14)"),this.sendBleCommand(20,[1])},resetProtectionStatus:function(e){return console.log("发送：复位保护状态 (0x15), 类型: ".concat(e)),this.sendBleCommand(21,[e])},setFanGear:function(e){return console.log("发送：档位控制 (0x16), 档位: ".concat(e)),this.sendBleCommand(22,[e])},controlBuzzerAdvanced:function(e,t){console.log("发送：蜂鸣器高级控制 (0x17), 频率: ".concat(e,"Hz, 持续时间: ").concat(t,"ms"));var n=[255&e,e>>8&255],o=[255&t,t>>8&255];return this.sendBleCommand(23,[].concat(n,o))},resetAnionCounter:function(){return console.log("发送：负离子计数器复位 (0x18)"),this.sendBleCommand(24,[1])},testRawData:function(){console.log("测试发送简单数据");var t=new Uint8Array([1,2,3,4]);this.connected&&this.deviceId&&this.serviceId&&this.characteristicId_write?e.writeBLECharacteristicValue({deviceId:this.deviceId,serviceId:this.serviceId,characteristicId:this.characteristicId_write,value:t.buffer,success:function(t){console.log("测试数据发送成功",t),e.showToast({title:"测试数据发送成功",icon:"success"})},fail:function(t){console.error("测试数据发送失败",t),e.showToast({title:"测试数据发送失败",icon:"error"})}}):e.showToast({title:"蓝牙未连接",icon:"none"})},clearReceivedData:function(){this.receivedMessages=[],e.showToast({title:"数据已清空",icon:"success",duration:1e3})},toggleBuzzer:function(){var t=this,n=!this.currentBuzzerState,o=n?1:0;console.log("快捷控制：切换蜂鸣器 ".concat(n?"开启":"关闭"," (0x12)"));var c=this.sendBleCommand(18,[o]);c&&(e.showToast({title:"正在".concat(n?"开启":"关闭","蜂鸣器..."),icon:"loading",duration:1e3}),setTimeout((function(){t.queryDeviceStatus()}),500))},setVoltageGear:function(t){var n=this;console.log("快捷控制：设置电压挡位 ".concat(t," (0x11)"));var o=this.sendBleCommand(17,[t]);if(o){var c=0===t?"关闭":"".concat(t,"挡");e.showToast({title:"正在设置为".concat(c,"..."),icon:"loading",duration:1e3}),setTimeout((function(){n.queryDeviceStatus()}),500)}},onBluetoothAdapterStateChange:function(t){console.log("蓝牙适配器状态变化:",t.available,t.discovering),this.isBluetoothAvailable=t.available,this.isScanning=t.discovering,t.available||(this.connected=!1,this.devices=[],this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="",e.showToast({title:"蓝牙适配器不可用",icon:"none"}))},onBluetoothDeviceFound:function(e){var t=this;e.devices.forEach((function(e){t.devices.some((function(t){return t.deviceId===e.deviceId}))||t.devices.push(e)}))},onBLEConnectionStateChange:function(t){console.log("BLE连接状态变化:",t.deviceId,t.connected),this.connected=t.connected,t.connected||(e.showToast({title:"设备 ".concat(t.deviceId," 已断开连接"),icon:"none"}),this.deviceName="",this.deviceId="",this.serviceId="",this.characteristicId_write="",this.characteristicId_notify="")},onBLECharacteristicValueChange:function(e){console.log("--- onBLECharacteristicValueChange triggered ---",e);var t=new Uint8Array(e.value),n=Array.prototype.map.call(t,(function(e){return("00"+e.toString(16)).slice(-2)})).join(" "),o="收到通知: CharacteristicId ".concat(e.characteristicId,", Value: [").concat(t,"], Hex: ").concat(n);this.receivedMessages.push(o),console.log("接收数据的特征值UUID:",e.characteristicId),console.log("接收到的原始数据:",n),this.receiveDataFromBle(e.value)},initBluetoothAdapter:function(){var t=this;e.openBluetoothAdapter({success:function(n){console.log("initBluetoothAdapter success",n),t.isBluetoothAvailable=!0,e.showToast({title:"蓝牙适配器初始化成功",icon:"success"})},fail:function(n){console.error("initBluetoothAdapter fail",n),t.isBluetoothAvailable=!1,e.showToast({title:"蓝牙适配器初始化失败，请检查蓝牙是否打开",icon:"none"})}})},startScan:function(){var t=this;this.isBluetoothAvailable?e.startBluetoothDevicesDiscovery({allowDuplicatesKey:!1,interval:0,success:function(n){console.log("startBluetoothDevicesDiscovery success",n),t.isScanning=!0,t.devices=[],e.onBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"开始扫描蓝牙设备",icon:"success"})},fail:function(n){console.error("startBluetoothDevicesDiscovery fail",n),t.isScanning=!1,e.showToast({title:"扫描失败",icon:"error"})}}):e.showToast({title:"蓝牙适配器不可用",icon:"none"})},stopScan:function(){var t=this;e.stopBluetoothDevicesDiscovery({success:function(n){console.log("stopBluetoothDevicesDiscovery success",n),t.isScanning=!1,e.offBluetoothDeviceFound(t.onBluetoothDeviceFound),e.showToast({title:"停止扫描",icon:"success"})},fail:function(t){console.error("stopBluetoothDevicesDiscovery fail",t),e.showToast({title:"停止扫描失败",icon:"error"})}})},connectDevice:function(t){var n=this;e.showLoading({title:"连接中..."}),this.isScanning&&this.stopScan(),e.createBLEConnection({deviceId:t,timeout:1e4,success:function(o){console.log("createBLEConnection success",o),n.connected=!0,n.deviceId=t;var c=n.devices.find((function(e){return e.deviceId===t}));c&&(n.deviceName=c.name||"未知设备"),e.hideLoading(),e.showToast({title:"连接成功",icon:"success"}),n.getServicesAndCharacteristics(t)},fail:function(t){console.error("createBLEConnection fail",t),n.connected=!1,e.hideLoading(),e.showToast({title:"连接失败",icon:"error"})}})},disconnectDevice:function(){var t=this;e.closeBLEConnection({deviceId:this.deviceId,success:function(n){console.log("closeBLEConnection success",n),t.connected=!1,t.deviceName="",t.deviceId="",t.serviceId="",t.characteristicId_write="",t.characteristicId_notify="",e.showToast({title:"断开连接成功",icon:"success"})},fail:function(t){console.error("closeBLEConnection fail",t),e.showToast({title:"断开连接失败",icon:"error"})}})},getServicesAndCharacteristics:function(t){var n=this;e.getBLEDeviceServices({deviceId:t,success:function(o){console.log("getBLEDeviceServices success",o.services);var c=o.services.find((function(e){return e.uuid.toUpperCase().includes("0000FF00")}));c?(n.serviceId=c.uuid,e.getBLEDeviceCharacteristics({deviceId:t,serviceId:c.uuid,success:function(o){console.log("getBLEDeviceCharacteristics success",o.characteristics),o.characteristics.forEach((function(e,t){console.log("特征值".concat(t,": UUID=").concat(e.uuid,", 属性="),e.properties)}));var i=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.write}));i||(i=o.characteristics.find((function(e){return e.properties.write})),i&&console.log("未找到0xFF02，使用其他可写特征值:",i.uuid));var a=o.characteristics.find((function(e){return e.uuid.toUpperCase().includes("0000FF02")&&e.properties.notify}));a&&console.log("找到协议定义的通知特征值:",a.uuid),i?(n.characteristicId_write=i.uuid,console.log("找到可写特征值 (FF02):",n.characteristicId_write)):console.warn("未找到可写特征值 (0x0000FF02)"),a?(n.characteristicId_notify=a.uuid,console.log("找到可通知特征值:",n.characteristicId_notify),setTimeout((function(){n.notifyCharacteristicValueChange(t,c.uuid,a.uuid)}),200)):console.warn("未找到可通知特征值 (0x0000FF02)"),e.showToast({title:"服务和特征值发现成功",icon:"success"})},fail:function(t){console.error("getBLEDeviceCharacteristics fail",t),e.showToast({title:"获取特征值失败",icon:"error"})}})):(console.warn("未找到服务 0x0000FF00"),e.showToast({title:"未找到指定服务",icon:"none"}))},fail:function(t){console.error("getBLEDeviceServices fail",t),e.showToast({title:"获取服务失败",icon:"error"})}})},notifyCharacteristicValueChange:function(t,n,o){e.notifyBLECharacteristicValueChange({state:!0,deviceId:t,serviceId:n,characteristicId:o,success:function(t){console.log("notifyBLECharacteristicValueChange success",t.errMsg),e.showToast({title:"已订阅通知",icon:"success"})},fail:function(t){console.error("notifyBLECharacteristicValueChange fail",t),e.showToast({title:"订阅通知失败",icon:"error"})}})}}};t.default=i}).call(this,n(2)["default"])},39:function(e,t,n){"use strict";n.r(t);var o=n(40),c=n.n(o);for(var i in o)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(i);t["default"]=c.a},40:function(e,t,n){}},[[33,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map