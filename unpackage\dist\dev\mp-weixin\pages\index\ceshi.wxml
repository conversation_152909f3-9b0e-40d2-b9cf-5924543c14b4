<view class="container data-v-5ca119a8"><button class="avatar-btn data-v-5ca119a8" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image class="avatar data-v-5ca119a8" src="{{avatarUrl||defaultAvatar}}" mode="aspectFill"></image></button><view class="form data-v-5ca119a8"><view class="form-item data-v-5ca119a8"><view class="form-label data-v-5ca119a8">昵称</view><input class="form-input data-v-5ca119a8" type="text" placeholder="请输入昵称" data-event-opts="{{[['input',[['__set_model',['','nickname','$event',[]]]]]]}}" value="{{nickname}}" bindinput="__e"/></view></view><button data-event-opts="{{[['tap',[['saveUserInfo',['$event']]]]]}}" class="save-btn data-v-5ca119a8" bindtap="__e">保存</button></view>