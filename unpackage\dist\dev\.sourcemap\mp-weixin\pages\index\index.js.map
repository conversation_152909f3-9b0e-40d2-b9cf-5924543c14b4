{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/yingxiao/bleTest/pages/index/index.vue?e281", "webpack:///E:/yingxiao/bleTest/pages/index/index.vue?26f5", "webpack:///E:/yingxiao/bleTest/pages/index/index.vue?fddb", "webpack:///E:/yingxiao/bleTest/pages/index/index.vue?6543", "uni-app:///pages/index/index.vue", "webpack:///E:/yingxiao/bleTest/pages/index/index.vue?2a41"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "devices", "length", "g1", "g2", "receivedMessages", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "isConnect", "deviceId", "serviceId", "notifyId", "receivedTxt", "receiveBuf", "sentWiFiTxt", "sentPwdTxt", "isBluetoothAvailable", "isScanning", "connected", "deviceName", "characteristicId_write", "characteristicId_notify", "gearValue", "buzzerStatus", "protectionResetType", "fanGear", "buzzerFrequency", "buzzerDuration", "currentBuzzerState", "currentVoltageGear", "connectionMonitorTimer", "lastHeartbeatTime", "autoReconnectEnabled", "reconnectAttempts", "maxReconnectAttempts", "onLoad", "uni", "console", "onShow", "onUnload", "success", "fail", "methods", "ceshi", "url", "receiveDataFromBle", "heartbeat_checksums", "name", "value", "checksumMatch", "matchMethod", "bytesToUint16", "bytesToUint32", "decodeDataFromBle", "title", "icon", "duration", "qualityText", "statusText", "workStatusText", "errorText", "sendBleCommand", "packet", "characteristicId", "crc8", "crc", "simpleSum", "sum", "xorChecksum", "xor", "queryDeviceStatus", "querySensorData", "queryMaintenanceInfo", "queryElectricalParameters", "queryProtectionStatus", "queryAirQuality", "sendHeartbeat", "setGear", "controlBuzzer", "resetDevice", "resetTimer", "resetProtectionStatus", "setFanGear", "controlBuzzerAdvanced", "frequency", "resetAnionCounter", "testRawData", "clearReceivedData", "toggle<PERSON><PERSON>zer", "setTimeout", "setVoltageGear", "onBluetoothAdapterStateChange", "onBluetoothDeviceFound", "res", "onBLEConnectionStateChange", "onBLECharacteristicValueChange", "initBluetoothAdapter", "startScan", "allowDuplicatesKey", "interval", "stopScan", "connectDevice", "connectDeviceWithRetry", "timeout", "errorMsg", "content", "showCancel", "cancelText", "confirmText", "disconnectDevice", "resetConnectionState", "clearBluetoothCache", "showConnectionTips", "startConnectionMonitor", "clearInterval", "stopConnectionMonitor", "handleConnectionLost", "onConnectionSuccess", "getServicesAndCharacteristics", "char<PERSON>es", "writeChar", "c", "notifyCharacteristicValueChange", "state"], "mappings": "2IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,QAAQC,QACjBC,EAAKT,EAAIO,QAAQC,OACjBE,EAAKV,EAAIW,iBAAiBH,OAC9BR,EAAIY,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLZ,GAAIA,EACJK,GAAIA,EACJC,GAAIA,MAKRO,GAAmB,EACnBC,EAAkB,GACtBnB,EAAOoB,eAAgB,G,gCCrBvB,wHAA+mB,eAAG,G,koCC0QlnB,CACAN,gBACA,OACAO,YACAC,YACAC,aACAC,YACAC,eACAC,cACAC,mBACAC,uBAGAC,wBACAC,cACAC,aACAvB,WACAwB,cACAC,0BACAC,2BACAtB,oBAGAuB,YACAC,eACAC,sBACAC,UACAC,oBACAC,mBAGAC,sBACAC,qBAGAC,4BACAC,oBACAC,wBACAC,oBACAC,yBAGAC,kBAEA,4BAEAC,oEAEAA,8DAGAA,uEACAA,sEACAC,gEAEAC,oBAGAC,oBAEAH,uEACAC,+DAGA,6BAGA,+BACAD,sBACA3B,uBACA+B,mBAAA,oCACAC,gBAAA,wCAIAC,SACAC,iBACAP,cACAQ,4BAKAC,+BACAR,8EACA,wBAGA,oFACA,kBAKA,GAJA,gCAIA,oCAKA,WACA,OACA,gBAGA,mBAMA,4BACA,wBAGA,wBAGA,wBAGA,wBAGA,wBAGA,sBAGA,KACA,aAEAS,QAAAC,cAAAC,YAGAF,QAAAC,aAAAC,YAGAF,QAAAC,eAAAC,WAGAF,QAAAC,aAAAC,YAGAF,QAAAC,eAAAC,WACAF,QAAAC,eAAAC,YAGAX,sBACAA,4EACAA,uEACAA,0EACAA,qEACAA,0EACA,YACAS,uBACAT,wFAGAA,iEAGA,SACA,KAEA,SACAY,KACAC,yBACA,SACAD,KACAC,oBACA,SACAD,KACAC,uBACA,SACAD,KACAC,kBACA,SACAD,KACAC,sBACA,CACA,IACA,EADA,IACAJ,GAAA,8CACA,gBACAG,KACAC,SACA,QAEA,+BAGA,KAKA,CACAb,wCACA,kCACA,kCARA,CACAA,oCACA,0BACA,8BASA,qBAEAA,2BACAA,mCACAA,uBACAA,sBAGA,+BAGAc,4BACA,uBAGAC,4BACA,6CAGAC,gCAGA,OAFAhB,gDAEA,GACA,SACA,SACA,gBACA,WACA,OAEA,gBACA,WACA,KACA,aACA,aACA,aACA,gBAEA,oBACA,iBAEA,MACA,KACA,eACA,eACA,kBAGA,8BACA,0BAGA,UACA,IAEA,0BACA,0BAEAA,mHAGA,sEAYA,GAXA,8BAGA,OACAD,aACAkB,iCACAC,eACAC,gBAIA,OACA,kCACApB,aACAkB,2BACAC,eACAC,sBAIAnB,6BAEA,MAEA,SACA,SACA,iBACA,8BACA,0BACA,0BACA,6BACA,6BACAA,kJAEAA,8BAEA,MAEA,SACA,gBACA,8BACAA,iDAEAA,6BAEA,MAEA,SACA,gBACA,kCACA,8BACA,6BACAA,qFAEAA,6BAEA,MAEA,SACA,SACA,gBACA,WACA,kBACA,kBACA,kBACA,kBACAA,kGAEAA,6BAEA,MAEA,SACA,SACA,gBACA,WACA,KACA,UACA,OAAAoB,MAAA,MACA,OAAAA,MAAA,MACA,OAAAA,SAAA,MACA,OAAAA,SAAA,MACA,OAAAA,SAAA,MACA,QAAAA,OAAA,MAEApB,qCAEAA,6BAEA,MAEA,SACAA,wBACA,kCACA,2BACA,8BACAD,aACAkB,eACAC,eACAC,eAEA,MAEA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,gBACA,WACA,8BACA,OACAnB,oCACAD,aAAAkB,6BAAAC,gBAEAlB,oCACAD,aAAAkB,6BAAAC,oBAGAlB,6BAEA,MAEA,SACA,gBACA,8BACA,OACA,KACA,UACA,OAAAqB,QAAA,MACA,OAAAA,OAAA,MACA,OAAAA,OAAA,MACA,QAAAA,OAAA,MAEArB,sEAEAA,8BAEA,MAEA,SACA,gBACA,WACA,KACA,UACA,OAAAsB,SAAA,MACA,OAAAA,SAAA,MACA,OAAAA,SAAA,MACA,OAAAA,SAAA,MACA,OAAAA,SAAA,MACA,QAAAA,OAAA,MAEA,yBACA,qBACA,0BACAtB,4GAEAA,iCAEA,MAEA,SACA,gBACA,WACA,OACA,KACA,UACA,OAAAuB,SAAA,MACA,OAAAA,UAAA,MACA,OAAAA,SAAA,MACA,QAAAA,SAAA,MAEAvB,mHAEAA,2BAEA,MAEA,SACA,uBACAA,4BACAD,aAAAkB,eAAAC,eAEAlB,gCAEA,MAEA,QACAA,uCACA,QAIAwB,2BAAA,gEACA,kFAGA,OAFAzB,aAAAkB,qBAAAC,mBACAlB,kCAGA,eACA,sBAEAyB,SACAA,QACAA,OACAA,OAEA,oBACAA,YAGA,qBACA,wBAmBA,OAlBAA,SAEAzB,yDAAA,oDAEAD,+BACA3B,uBACAC,yBACAqD,6CACAf,eACAR,oBACAH,sBACAD,aAAAkB,eAAAC,eAAAC,gBAEAf,iBACAJ,wBACAD,aAAAkB,eAAAC,aAAAC,kBAGA,GAEAQ,mBAEA,IADA,QACA,aACAC,QACA,oBACA,MACAA,SAEAA,MAGAA,OAGA,cAIAC,sBAEA,IADA,QACA,mBACAC,QAEA,cAIAC,wBAEA,IADA,QACA,mBACAC,QAEA,cAGAC,6BAEA,OADAjC,gCACA,wBAEAkC,2BAEA,OADAlC,iCACA,wBAEAmC,gCAEA,OADAnC,gCACA,wBAEAoC,qCAEA,OADApC,gCACA,wBAEAqC,iCAEA,OADArC,gCACA,wBAEAsC,2BAEA,OADAtC,gCACA,wBAEAuC,yBACAvC,6BACA,6BAQA,OAPA,GACAD,aACAkB,eACAC,eACAC,eAGA,GAGAqB,oBAEA,OADAxC,8CACA,6BAEAyC,0BAEA,OADAzC,+CACA,6BAEA0C,uBAEA,OADA1C,8BACA,6BAEA2C,sBAEA,OADA3C,+BACA,6BAEA4C,kCAEA,OADA5C,gDACA,6BAEA6C,uBAEA,OADA7C,8CACA,6BAEA8C,oCACA9C,6EACA,OACA+C,MACAA,UAEA,GACA5B,MACAA,UAEA,+CAEA6B,6BAEA,OADAhD,kCACA,6BAGAiD,uBACAjD,wBAEA,gCAEA,2EAKAD,+BACA3B,uBACAC,yBACAqD,6CACAf,eACAR,oBACAH,0BACAD,aAAAkB,iBAAAC,kBAEAd,iBACAJ,4BACAD,aAAAkB,iBAAAC,kBAfAnB,aAAAkB,cAAAC,eAoBAgC,6BACA,yBACAnD,aAAAkB,cAAAC,eAAAC,gBAKAgC,wBAAA,WACA,2BACA,QAEAnD,yDAGA,kCACA,IAEAD,aACAkB,wCACAC,eACAC,eAIAiC,uBACA,wBACA,OAKAC,2BAAA,WACArD,gDAGA,kCACA,MAEA,kCACAD,aACAkB,8BACAC,eACAC,eAIAiC,uBACA,wBACA,OAIAE,0CACAtD,oDACA,sCACA,8BAEA,YAOAA,2BALA,4BACA,gBACAD,aAAAkB,2BAAAC,gBAOAqC,mCAAA,WACAC,+BACA,+DACA,sBAKAC,uCACAzD,iDACA,2BACA,cACAD,aAAAkB,wCAAAC,cACA,mBACA,iBACA,kBACA,+BACA,kCAIAwC,2CACA1D,kEACA,8BACA,6FACA,mGACA,8BAGAA,gDACAA,2BAEA,kCAIA2D,gCAAA,WACA5D,wBACAI,oBACAH,8CACA,0BACAD,aAAAkB,mBAAAC,kBAEAd,iBACAJ,6CACA,0BACAD,aAAAkB,6BAAAC,kBAKA0C,qBAAA,WACA,0BAIA7D,kCACA8D,sBACAC,WACA3D,oBACAH,wDACA,gBACA,aACAD,mDACAA,aAAAkB,iBAAAC,kBAEAd,iBACAJ,uDACA,gBACAD,aAAAkB,aAAAC,kBAhBAnB,aAAAkB,iBAAAC,eAqBA6C,oBAAA,WACAhE,iCACAI,oBACAH,uDACA,gBACAD,oDACAA,aAAAkB,aAAAC,kBAEAd,iBACAJ,sDACAD,aAAAkB,eAAAC,mBAKA8C,0BACA,kCAIAC,qCAAA,WACA,IACA,MAEAlE,eAAAkB,sEAGA,iBACA,gBAIA,gCACAjB,0BACAD,sBACA3B,uBACA+B,mBAAA,+BACAC,gBAAA,yCAKAgD,uBACArD,uBACA3B,WACA8F,aACA/D,oBACAH,6CACA,eACA,aACA,2DACA,IACA,6BAEAD,gBACAA,aAAAkB,aAAAC,iBAGA,wBAGA,oCAEAd,iBACAJ,4CACA,eAGA,aACA,kBACAmE,uBACA,kBACAA,mBACA,oBACAA,eAIA,KACApE,gBACAA,aACAkB,+CACAC,YACAC,gBAGAiC,uBACA,kCACA,KAEArD,gBACAA,aACAkB,aACAmD,8FACAC,cACAC,gBACAC,iBACApE,oBACA,WAEA,uCAOA,cAGAqE,4BAAA,WACA,cAKAzE,sBACA3B,uBACA+B,oBACAH,4CACA,yBACAD,aAAAkB,eAAAC,kBAEAd,iBACAJ,2CAEA,yBACAD,aAAAkB,qBAAAC,iBAfAnB,aAAAkB,gBAAAC,eAqBAuD,gCACA,kBACA,mBACA,iBACA,kBACA,+BACA,gCACA,yBAEA,2BACA,0BAEA,6BAEA,yBACA,0BAIAC,iCAuBAC,8BACA5E,aACAkB,eACAmD,qOAqBAC,cACAE,qBAKAK,kCAAA,WACA,6BACAC,2CAGA,oDACA,4BAEA,kBAGA,iBACA,mDACA7E,6BACA,6BAGA,MAIA8E,iCACA,8BACAD,2CACA,mCAKAE,gCAAA,WACA/E,uBACA,kBAEA,6EACA,yBACAA,iGAEAD,aACAkB,6FACAC,eACAC,eAGAiC,uBACA,YACA,yCAEA,OAEArD,aACAkB,cACAC,eAEA,8BAKA8D,+BACA,yBACA,kCACA,+BAIAC,0CAAA,WACAlF,wBACA3B,WACA+B,oBACAH,uDACA,uFACA,GACA,mBACAD,+BACA3B,WACAC,iBACA8B,oBACAH,qEAGAkF,yCACAlF,8EAIA,kHAGA,IACAmF,sCAAA,6BACA,GACAnF,4CAKA,iDACAoF,2CACAA,uBAGA,GACApF,oCAEA,GACA,gCACAA,yDAEAA,sCAEA,GACA,iCACAA,mDAGAoD,uBACA,qDACA,MAEApD,uCAEAD,aAAAkB,mBAAAC,kBAEAd,iBACAJ,oDACAD,aAAAkB,gBAAAC,oBAIAlB,iCACAD,aAAAkB,gBAAAC,gBAGAd,iBACAJ,6CACAD,aAAAkB,eAAAC,mBAMAmE,gDACAtF,sCACAuF,SACAlH,WACAC,YACAqD,mBACAvB,oBACAH,mEACAD,aAAAkB,cAAAC,kBAEAd,iBACAJ,2DACAD,aAAAkB,eAAAC,qBAKA,c,4DCl4CA,wHAAkrC,eAAG,G", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.devices.length\n  var g1 = _vm.devices.length\n  var g2 = _vm.receivedMessages.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<!-- 操作按钮组 -->\n\t\t<view @click=\"ceshi\" style=\"text-align: center;background-color: antiquewhite;margin: 10px;\">ceshi</view>\n\t\t<view class=\"button-group\">\n\t\t\t<button class=\"btn btn-primary\" @click=\"initBluetoothAdapter\" :disabled=\"isBluetoothAvailable\">\n\t\t\t\t🔧 初始化蓝牙\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-success\" @click=\"startScan\" :disabled=\"!isBluetoothAvailable || isScanning || connected\">\n\t\t\t\t🔍 扫描设备\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-warning\" @click=\"stopScan\" :disabled=\"!isScanning\">\n\t\t\t\t⏹️ 停止扫描\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-danger\" @click=\"disconnectDevice\" :disabled=\"!connected\">\n\t\t\t\t🔌 断开连接\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 调试和帮助按钮组 -->\n\t\t<view class=\"debug-button-group\">\n\t\t\t<button class=\"btn btn-debug\" @click=\"showConnectionTips\">\n\t\t\t\t💡 连接帮助\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-debug\" @click=\"clearBluetoothCache\">\n\t\t\t\t🧹 清理缓存\n\t\t\t</button>\n\t\t\t<button class=\"btn btn-debug\" @click=\"resetConnectionState\">\n\t\t\t\t🔄 重置状态\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 连接状态卡片 -->\n\t\t<view class=\"status-card\">\n\t\t\t<view class=\"status-header\">\n\t\t\t\t<text class=\"status-title\">📡 连接状态</text>\n\t\t\t\t<view class=\"status-indicator\" :class=\"{ 'connected': connected, 'disconnected': !connected }\">\n\t\t\t\t\t{{ connected ? '🟢 已连接' : '🔴 未连接' }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"status-details\">\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"status-label\">蓝牙适配器:</text>\n\t\t\t\t\t<text class=\"status-value\" :class=\"{ 'success': isBluetoothAvailable, 'error': !isBluetoothAvailable }\">\n\t\t\t\t\t\t{{ isBluetoothAvailable ? '✅ 可用' : '❌ 不可用' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\">\n\t\t\t\t\t<text class=\"status-label\">扫描状态:</text>\n\t\t\t\t\t<text class=\"status-value\" :class=\"{ 'warning': isScanning }\">\n\t\t\t\t\t\t{{ isScanning ? '🔍 扫描中...' : '⏸️ 未扫描' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\" v-if=\"deviceName\">\n\t\t\t\t\t<text class=\"status-label\">设备名称:</text>\n\t\t\t\t\t<text class=\"status-value success\">{{ deviceName }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"status-item\" v-if=\"serviceId\">\n\t\t\t\t\t<text class=\"status-label\">服务ID:</text>\n\t\t\t\t\t<text class=\"status-value info\">{{ serviceId }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 设备列表卡片 -->\n\t\t<view class=\"device-list-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">📱 发现的设备</text>\n\t\t\t\t<text class=\"device-count\">{{ devices.length }} 个设备</text>\n\t\t\t</view>\n\t\t\t<scroll-view scroll-y class=\"device-scroll\">\n\t\t\t\t<view v-for=\"device in devices\" :key=\"device.deviceId\" class=\"device-item\">\n\t\t\t\t\t<view class=\"device-info\">\n\t\t\t\t\t\t<text class=\"device-name\">{{ device.name || '未知设备' }}</text>\n\t\t\t\t\t\t<text class=\"device-id\">{{ device.deviceId }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<button class=\"btn btn-connect\" @click=\"connectDevice(device.deviceId)\" :disabled=\"connected\">\n\t\t\t\t\t\t{{ connected ? '已连接' : '连接' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"devices.length === 0\" class=\"empty-state\">\n\t\t\t\t\t<text>🔍 暂无发现设备，请先扫描</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<!-- 接收数据卡片 -->\n\t\t<view class=\"data-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">📥 接收到的数据</text>\n\t\t\t\t<button class=\"btn btn-clear\" @click=\"clearReceivedData\">清空</button>\n\t\t\t</view>\n\t\t\t<scroll-view scroll-y class=\"data-scroll\">\n\t\t\t\t<view v-for=\"(msg, index) in receivedMessages\" :key=\"index\" class=\"message-item\">\n\t\t\t\t\t<text class=\"message-text\">{{ msg }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"receivedMessages.length === 0\" class=\"empty-state\">\n\t\t\t\t\t<text>📭 暂无接收数据</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<!-- 命令发送卡片 -->\n\t\t<view class=\"command-card\">\n\t\t\t<view class=\"card-header\">\n\t\t\t\t<text class=\"card-title\">📤 发送命令</text>\n\t\t\t</view>\n\n\t\t\t<!-- 查询命令组 -->\n\t\t\t<view class=\"command-group\">\n\t\t\t\t<text class=\"group-title\">🔍 查询命令</text>\n\t\t\t\t<view class=\"command-buttons\">\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"queryDeviceStatus\" :disabled=\"!connected\">\n\t\t\t\t\t\t📊 设备状态\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"querySensorData\" :disabled=\"!connected\">\n\t\t\t\t\t\t🌡️ 传感器数据\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"queryMaintenanceInfo\" :disabled=\"!connected\">\n\t\t\t\t\t\t🔧 保养信息\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"queryElectricalParameters\" :disabled=\"!connected\">\n\t\t\t\t\t\t⚡ 电气参数\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"queryProtectionStatus\" :disabled=\"!connected\">\n\t\t\t\t\t\t🛡️ 保护状态\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"queryAirQuality\" :disabled=\"!connected\">\n\t\t\t\t\t\t🌬️ 空气质量\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-query\" @click=\"sendHeartbeat\" :disabled=\"!connected\">\n\t\t\t\t\t\t💓 心跳包\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 快捷控制组 -->\n\t\t\t<view class=\"command-group\">\n\t\t\t\t<text class=\"group-title\">⚡ 快捷控制</text>\n\n\t\t\t\t<!-- 蜂鸣器控制 -->\n\t\t\t\t<view class=\"quick-control-section\">\n\t\t\t\t\t<text class=\"control-section-title\">🔊 蜂鸣器控制</text>\n\t\t\t\t\t<view class=\"toggle-buttons\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"btn btn-toggle\"\n\t\t\t\t\t\t\t:class=\"{ 'active': currentBuzzerState }\"\n\t\t\t\t\t\t\t@click=\"toggleBuzzer\"\n\t\t\t\t\t\t\t:disabled=\"!connected\">\n\t\t\t\t\t\t\t{{ currentBuzzerState ? '🔊 开启' : '🔇 关闭' }}\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 电压挡位控制 -->\n\t\t\t\t<view class=\"quick-control-section\">\n\t\t\t\t\t<text class=\"control-section-title\">⚡ 电压挡位控制</text>\n\t\t\t\t\t<view class=\"gear-buttons\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"btn btn-gear\"\n\t\t\t\t\t\t\t:class=\"{ 'active': currentVoltageGear === 0 }\"\n\t\t\t\t\t\t\t@click=\"setVoltageGear(0)\"\n\t\t\t\t\t\t\t:disabled=\"!connected\">\n\t\t\t\t\t\t\t关闭\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"btn btn-gear\"\n\t\t\t\t\t\t\t:class=\"{ 'active': currentVoltageGear === 1 }\"\n\t\t\t\t\t\t\t@click=\"setVoltageGear(1)\"\n\t\t\t\t\t\t\t:disabled=\"!connected\">\n\t\t\t\t\t\t\t1挡\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"btn btn-gear\"\n\t\t\t\t\t\t\t:class=\"{ 'active': currentVoltageGear === 2 }\"\n\t\t\t\t\t\t\t@click=\"setVoltageGear(2)\"\n\t\t\t\t\t\t\t:disabled=\"!connected\">\n\t\t\t\t\t\t\t2挡\n\t\t\t\t\t\t</button>\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"btn btn-gear\"\n\t\t\t\t\t\t\t:class=\"{ 'active': currentVoltageGear === 3 }\"\n\t\t\t\t\t\t\t@click=\"setVoltageGear(3)\"\n\t\t\t\t\t\t\t:disabled=\"!connected\">\n\t\t\t\t\t\t\t3挡\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 控制命令组 -->\n\t\t\t<view class=\"command-group\">\n\t\t\t\t<text class=\"group-title\">🎛️ 控制命令</text>\n\n\t\t\t\t<!-- 简单控制按钮 -->\n\t\t\t\t<view class=\"command-buttons\">\n\t\t\t\t\t<button class=\"btn btn-control\" @click=\"resetDevice\" :disabled=\"!connected\">\n\t\t\t\t\t\t🔄 复位设备\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-control\" @click=\"resetTimer\" :disabled=\"!connected\">\n\t\t\t\t\t\t⏰ 复位计时器\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"btn btn-control\" @click=\"resetAnionCounter\" :disabled=\"!connected\">\n\t\t\t\t\t\t🔋 复位负离子计数器\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 参数控制 -->\n\t\t\t\t<view class=\"param-controls\">\n\t\t\t\t\t<view class=\"control-item\">\n\t\t\t\t\t\t<text class=\"control-label\">⚙️ 设置挡位:</text>\n\t\t\t\t\t\t<view class=\"control-input\">\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"gearValue\" placeholder=\"0-3\"/>\n\t\t\t\t\t\t\t<button class=\"btn btn-send\" @click=\"setGear(gearValue)\" :disabled=\"!connected\">发送</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"control-item\">\n\t\t\t\t\t\t<text class=\"control-label\">🔊 控制蜂鸣器:</text>\n\t\t\t\t\t\t<view class=\"control-input\">\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"buzzerStatus\" placeholder=\"0/1\"/>\n\t\t\t\t\t\t\t<button class=\"btn btn-send\" @click=\"controlBuzzer(buzzerStatus)\" :disabled=\"!connected\">发送</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"control-item\">\n\t\t\t\t\t\t<text class=\"control-label\">🛡️ 复位保护状态:</text>\n\t\t\t\t\t\t<view class=\"control-input\">\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"protectionResetType\" placeholder=\"0x01-0xFF\"/>\n\t\t\t\t\t\t\t<button class=\"btn btn-send\" @click=\"resetProtectionStatus(protectionResetType)\" :disabled=\"!connected\">发送</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"control-item\">\n\t\t\t\t\t\t<text class=\"control-label\">💨 档位控制:</text>\n\t\t\t\t\t\t<view class=\"control-input\">\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"fanGear\" placeholder=\"0-6\"/>\n\t\t\t\t\t\t\t<button class=\"btn btn-send\" @click=\"setFanGear(fanGear)\" :disabled=\"!connected\">发送</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"control-item\">\n\t\t\t\t\t\t<text class=\"control-label\">🎵 蜂鸣器高级控制:</text>\n\t\t\t\t\t\t<view class=\"control-input-group\">\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"buzzerFrequency\" placeholder=\"频率(Hz)\"/>\n\t\t\t\t\t\t\t<input class=\"input-field\" type=\"number\" v-model.number=\"buzzerDuration\" placeholder=\"时间(ms)\"/>\n\t\t\t\t\t\t\t<button class=\"btn btn-send\" @click=\"controlBuzzerAdvanced(buzzerFrequency, buzzerDuration)\" :disabled=\"!connected\">发送</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 测试命令组 -->\n\t\t\t<view class=\"command-group\">\n\t\t\t\t<text class=\"group-title\">🧪 测试命令</text>\n\t\t\t\t<view class=\"command-buttons\">\n\t\t\t\t\t<button class=\"btn btn-test\" @click=\"testRawData\" :disabled=\"!connected\">\n\t\t\t\t\t\t📡 测试原始数据\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisConnect: 0,\n\t\t\tdeviceId: \"\",\n\t\t\tserviceId: \"\",\n\t\t\tnotifyId: \"\",\n\t\t\treceivedTxt: [],\n\t\t\treceiveBuf: [],\n\t\t\tsentWiFiTxt: \"ZHHC\",\n\t\t\tsentPwdTxt: \"666888999\",\n\n\t\t\t// 新增蓝牙相关状态\n\t\t\tisBluetoothAvailable: false, // 蓝牙适配器是否可用\n\t\t\tisScanning: false, // 是否正在扫描\n\t\t\tconnected: false, // 是否已连接\n\t\t\tdevices: [], // 扫描到的设备列表\n\t\t\tdeviceName: '', // 已连接设备名称\n\t\t\tcharacteristicId_write: '', // 写特征值ID\n\t\t\tcharacteristicId_notify: '', // 通知特征值ID\n\t\t\treceivedMessages: [], // 接收到的数据列表\n\n\t\t\t// 命令发送相关数据\n\t\t\tgearValue: 0, // 设置挡位\n\t\t\tbuzzerStatus: 0, // 蜂鸣器状态\n\t\t\tprotectionResetType: 0x01, // 复位保护状态类型\n\t\t\tfanGear: 0, // 风扇档位\n\t\t\tbuzzerFrequency: 1000, // 蜂鸣器频率\n\t\t\tbuzzerDuration: 100, // 蜂鸣器持续时间\n\n\t\t\t// 快捷控制状态\n\t\t\tcurrentBuzzerState: false, // 当前蜂鸣器状态 (false=关闭, true=开启)\n\t\t\tcurrentVoltageGear: 0, // 当前电压挡位 (0=关闭, 1=1挡, 2=2挡, 3=3挡)\n\n\t\t\t// 连接监控\n\t\t\tconnectionMonitorTimer: null, // 连接监控定时器\n\t\t\tlastHeartbeatTime: 0, // 最后心跳时间\n\t\t\tautoReconnectEnabled: true, // 是否启用自动重连\n\t\t\treconnectAttempts: 0, // 重连尝试次数\n\t\t\tmaxReconnectAttempts: 5 // 最大重连次数\n\t\t};\n\t},\n\tonLoad() {\n\t\t// 初始化蓝牙适配器\n\t\tthis.initBluetoothAdapter();\n\t\t// 监听蓝牙适配器状态变化\n\t\tuni.onBluetoothAdapterStateChange(this.onBluetoothAdapterStateChange);\n\t\t// 监听BLE连接状态变化\n\t\tuni.onBLEConnectionStateChange(this.onBLEConnectionStateChange);\n\t\t\n\t\t// 显式地取消并重新注册BLE特征值变化监听器，确保每次加载都是新的监听器\n\t\tuni.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange);\n\t\tuni.onBLECharacteristicValueChange(this.onBLECharacteristicValueChange);\n\t\tconsole.log('onBLECharacteristicValueChange 监听器已在onLoad中重新注册');\n\t},\n\tonShow() {\n\t\t\n\t},\n\tonUnload() {\n\t\t// 在页面卸载时移除BLE特征值变化监听器，防止内存泄漏\n\t\tuni.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange);\n\t\tconsole.log('onBLECharacteristicValueChange 监听器已在onUnload中移除');\n\n\t\t// 停止连接监控\n\t\tthis.stopConnectionMonitor();\n\n\t\t// 断开蓝牙连接\n\t\tif (this.connected && this.deviceId) {\n\t\t\tuni.closeBLEConnection({\n\t\t\t\tdeviceId: this.deviceId,\n\t\t\t\tsuccess: () => console.log('页面卸载时已断开蓝牙连接'),\n\t\t\t\tfail: () => console.log('页面卸载时断开蓝牙连接失败')\n\t\t\t});\n\t\t}\n\t},\n\tmethods: {\n\t\tceshi() {\n\t\t        uni.redirectTo({\n\t\t            url: '/pages/index/ceshi' \n\t\t            // 注意这里如果是对象的最后一个属性，后面不需要分号，\n\t\t            // 另外如果有多个属性，属性之间用逗号分隔\n\t\t        });\n\t\t},\n\t\treceiveDataFromBle(data){\n\t\t\tconsole.log('--- receiveDataFromBle entered with data ---', new Uint8Array(data)); // 新增日志\n\t\t\tvar array = new Uint8Array(data);\n\n\t\t\t// 显示原始数据到界面\n\t\t\tconst hexStr = Array.from(array).map(byte => byte.toString(16).padStart(2, '0')).join(' ');\n\t\t\tconst rawMsg = `📡 ${hexStr}`;\n\t\t\tthis.receivedMessages.push(rawMsg);\n\n\t\t\t// 检查帧头\n\t\t\t// 检查帧头\n\t\t\tif (array.length < 5 || array[0] !== 0xAA || array[1] !== 0x55) {\n\t\t\t\t// 静默忽略无效数据\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconst command = array[2]; // 命令字\n\t\t\tconst dataLength = array[3]; // 数据长度\n\t\t\tconst receivedChecksum = array[array.length - 1]; // 校验和\n\t\t\t\n\t\t\t// 校验数据长度\n\t\t\tif (array.length !== (5 + dataLength)) {\n\t\t\t\t// 静默忽略长度不匹配的数据\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 尝试多种校验和计算方式\n\t\t\tconst checksumData = array.slice(2, array.length - 1); // 从命令字开始到数据区结束\n\t\t\tconst checksumDataWithHeader = array.slice(0, array.length - 1); // 包含帧头\n\n\t\t\t// 方式1: CRC-8 (仅命令和数据)\n\t\t\tconst crc8_cmd_data = this.crc8(checksumData, checksumData.length);\n\n\t\t\t// 方式2: CRC-8 (包含帧头)\n\t\t\tconst crc8_with_header = this.crc8(checksumDataWithHeader, checksumDataWithHeader.length);\n\n\t\t\t// 方式3: 简单累加和 (仅命令和数据)\n\t\t\tconst sum_cmd_data = this.simpleSum(checksumData) & 0xFF;\n\n\t\t\t// 方式4: 简单累加和 (包含帧头)\n\t\t\tconst sum_with_header = this.simpleSum(checksumDataWithHeader) & 0xFF;\n\n\t\t\t// 方式5: XOR校验 (仅命令和数据)\n\t\t\tconst xor_cmd_data = this.xorChecksum(checksumData);\n\n\t\t\t// 方式6-10: 针对心跳包的多种特殊校验\n\t\t\tlet heartbeat_checksums = [];\n\t\t\tif (array[2] === 0x87) { // 心跳响应\n\t\t\t\t// 方式6: 简单累加\n\t\t\t\theartbeat_checksums.push({name: '心跳SUM1', value: (0xAA + 0x55 + 0x87 + 0x00) & 0xFF});\n\n\t\t\t\t// 方式7: 只对命令字校验\n\t\t\t\theartbeat_checksums.push({name: '心跳CMD', value: 0x87});\n\n\t\t\t\t// 方式8: 固定值校验\n\t\t\t\theartbeat_checksums.push({name: '心跳FIXED', value: 0x3C});\n\n\t\t\t\t// 方式9: 命令字取反\n\t\t\t\theartbeat_checksums.push({name: '心跳NOT', value: (~0x87) & 0xFF});\n\n\t\t\t\t// 方式10: 特殊算法 (0x87 + 0x00 + 某个固定值)\n\t\t\t\theartbeat_checksums.push({name: '心跳SPEC1', value: (0x87 + 0x00 + 0xB5) & 0xFF});\n\t\t\t\theartbeat_checksums.push({name: '心跳SPEC2', value: (0x87 ^ 0xAB) & 0xFF});\n\t\t\t}\n\n\t\t\tconsole.log('校验和测试:');\n\t\t\tconsole.log(`  CRC-8(cmd+data): 0x${crc8_cmd_data.toString(16).padStart(2, '0')}`);\n\t\t\tconsole.log(`  CRC-8(all): 0x${crc8_with_header.toString(16).padStart(2, '0')}`);\n\t\t\tconsole.log(`  SUM(cmd+data): 0x${sum_cmd_data.toString(16).padStart(2, '0')}`);\n\t\t\tconsole.log(`  SUM(all): 0x${sum_with_header.toString(16).padStart(2, '0')}`);\n\t\t\tconsole.log(`  XOR(cmd+data): 0x${xor_cmd_data.toString(16).padStart(2, '0')}`);\n\t\t\tif (heartbeat_checksums.length > 0) {\n\t\t\t\theartbeat_checksums.forEach(hb => {\n\t\t\t\t\tconsole.log(`  ${hb.name}: 0x${hb.value.toString(16).padStart(2, '0')}`);\n\t\t\t\t});\n\t\t\t}\n\t\t\tconsole.log(`  接收到的: 0x${receivedChecksum.toString(16).padStart(2, '0')}`);\n\n\t\t\t// 检查哪种方式匹配\n\t\t\tlet checksumMatch = false;\n\t\t\tlet matchMethod = '';\n\n\t\t\tif (crc8_cmd_data === receivedChecksum) {\n\t\t\t\tchecksumMatch = true;\n\t\t\t\tmatchMethod = 'CRC-8(cmd+data)';\n\t\t\t} else if (crc8_with_header === receivedChecksum) {\n\t\t\t\tchecksumMatch = true;\n\t\t\t\tmatchMethod = 'CRC-8(all)';\n\t\t\t} else if (sum_cmd_data === receivedChecksum) {\n\t\t\t\tchecksumMatch = true;\n\t\t\t\tmatchMethod = 'SUM(cmd+data)';\n\t\t\t} else if (sum_with_header === receivedChecksum) {\n\t\t\t\tchecksumMatch = true;\n\t\t\t\tmatchMethod = 'SUM(all)';\n\t\t\t} else if (xor_cmd_data === receivedChecksum) {\n\t\t\t\tchecksumMatch = true;\n\t\t\t\tmatchMethod = 'XOR(cmd+data)';\n\t\t\t} else {\n\t\t\t\t// 检查心跳包的特殊校验方式\n\t\t\t\tfor (let hb of heartbeat_checksums) {\n\t\t\t\t\tif (hb.value === receivedChecksum) {\n\t\t\t\t\t\tchecksumMatch = true;\n\t\t\t\t\t\tmatchMethod = hb.name;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!checksumMatch) {\n\t\t\t\tconsole.warn('所有校验方式都不匹配，跳过校验继续处理');\n\t\t\t\tconst warningMsg = `⚠️ 校验和不匹配，但继续处理数据`;\n\t\t\t\tthis.receivedMessages.push(warningMsg);\n\t\t\t\t// 不再return，继续处理数据\n\t\t\t} else {\n\t\t\t\tconsole.log(`✅ 校验和匹配，使用方式: ${matchMethod}`);\n\t\t\t\tconst successMsg = `✅ 校验和验证成功 (${matchMethod})`;\n\t\t\t\tthis.receivedMessages.push(successMsg);\n\t\t\t}\n\t\t\t\n\t\t\t// 提取数据区\n\t\t\tconst dataArea = array.slice(4, 4 + dataLength);\n\t\t\t\n\t\t\tconsole.log('接收到有效数据包:', array);\n\t\t\tconsole.log('命令字:', command.toString(16));\n\t\t\tconsole.log('数据长度:', dataLength);\n\t\t\tconsole.log('数据区:', dataArea);\n\t\t\t\n\t\t\t// 调用解析函数\n\t\t\tthis.decodeDataFromBle(command, dataArea);\n\t\t},\n\t\t// Helper to convert 2 bytes to a 16-bit unsigned integer (little-endian)\n\t\tbytesToUint16(byteArray, offset) {\n\t\t    return (byteArray[offset + 1] << 8) | byteArray[offset];\n\t\t},\n\t\t// Helper to convert 4 bytes to a 32-bit unsigned integer (little-endian)\n\t\tbytesToUint32(byteArray, offset) {\n\t\t    return (byteArray[offset + 3] << 24) | (byteArray[offset + 2] << 16) | (byteArray[offset + 1] << 8) | byteArray[offset];\n\t\t},\n\t\t// 解析函数\n\t\tdecodeDataFromBle(command, data){\n\t\t\tconsole.log(\"接收到命令: 0x\" + command.toString(16), \"数据:\", data);\n\n\t\t\tswitch (command) {\n\t\t\t\tcase 0x81: // 设备状态响应\n\t\t\t\tcase 0xA1: // 设备状态变化通知\n\t\t\t\t\tif (data.length >= 2) {\n\t\t\t\t\t\tconst workStatusByte = data[0];\n\t\t\t\t\t\tconst deviceModeByte = data[1];\n\n\t\t\t\t\t\tconst switchStatus = (workStatusByte & 0b00000001) ? '打开' : '关闭';\n\t\t\t\t\t\tconst gear = (workStatusByte & 0b00000110) >> 1; // Bit 1-2\n\t\t\t\t\t\tlet gearStatus = '';\n\t\t\t\t\t\tif (gear === 0b00) gearStatus = '关闭';\n\t\t\t\t\t\telse if (gear === 0b01) gearStatus = '低挡';\n\t\t\t\t\t\telse if (gear === 0b10) gearStatus = '中挡';\n\t\t\t\t\t\telse if (gear === 0b11) gearStatus = '高挡';\n\n\t\t\t\t\t\tconst buzzerStatus = (workStatusByte & 0b00001000) ? '打开' : '关闭'; // Bit 3\n\t\t\t\t\t\tconst runException = (workStatusByte & 0b00010000) ? '异常' : '正常'; // Bit 4\n\n\t\t\t\t\t\tconst workMode = (deviceModeByte & 0b00000011); // Bit 0-1\n\t\t\t\t\t\tlet workModeStatus = '';\n\t\t\t\t\t\tif (workMode === 0b00) workModeStatus = '正常模式';\n\t\t\t\t\t\telse if (workMode === 0b01) workModeStatus = '节能模式';\n\t\t\t\t\t\telse if (workMode === 0b10) workModeStatus = '强力模式';\n\n\t\t\t\t\t\t// 检查状态是否发生变化\n\t\t\t\t\t\tconst oldBuzzerState = this.currentBuzzerState;\n\t\t\t\t\t\tconst oldVoltageGear = this.currentVoltageGear;\n\n\t\t\t\t\t\t// 更新快捷控制状态\n\t\t\t\t\t\tconst newBuzzerState = (workStatusByte & 0b00001000) ? true : false;\n\t\t\t\t\t\tconst newVoltageGear = gear; // 0=关闭, 1=低挡, 2=中挡, 3=高挡\n\n\t\t\t\t\t\tthis.currentBuzzerState = newBuzzerState;\n\t\t\t\t\t\tthis.currentVoltageGear = newVoltageGear;\n\n\t\t\t\t\t\tconsole.log(`设备状态: 开关: ${switchStatus}, 挡位: ${gearStatus}, 蜂鸣器: ${buzzerStatus}, 运行: ${runException}, 工作模式: ${workModeStatus}`);\n\n\t\t\t\t\t\t// 在界面显示解析后的状态\n\t\t\t\t\t\tconst statusMsg = `📊 设备状态更新: 挡位=${gearStatus}, 蜂鸣器=${buzzerStatus}, 运行=${runException}`;\n\t\t\t\t\t\tthis.receivedMessages.push(statusMsg);\n\n\t\t\t\t\t\t// 如果状态发生变化，显示确认提示\n\t\t\t\t\t\tif (oldBuzzerState !== newBuzzerState) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `蜂鸣器已${newBuzzerState ? '开启' : '关闭'}`,\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (oldVoltageGear !== newVoltageGear) {\n\t\t\t\t\t\t\tconst gearText = newVoltageGear === 0 ? '关闭' : `${newVoltageGear}挡`;\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `电压挡位已设置为${gearText}`,\n\t\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('设备状态响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x82: // 传感器数据响应\n\t\t\t\tcase 0xA2: // 传感器数据变化通知\n\t\t\t\t\tif (data.length >= 10) {\n\t\t\t\t\t\tconst pm25 = this.bytesToUint16(data, 0);\n\t\t\t\t\t\tconst voc = this.bytesToUint16(data, 2);\n\t\t\t\t\t\tconst anionConcentration = this.bytesToUint16(data, 4);\n\t\t\t\t\t\tconst temperature = this.bytesToUint16(data, 6) / 10;\n\t\t\t\t\t\tconst humidity = this.bytesToUint16(data, 8) / 10;\n\t\t\t\t\t\tconsole.log(`传感器数据: PM2.5: ${pm25}μg/m³, VOC: ${voc}ppm, 负离子浓度: ${anionConcentration}个/cm³, 温度: ${temperature}°C, 湿度: ${humidity}%RH`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('传感器数据响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x83: // 保养信息响应\n\t\t\t\t\tif (data.length >= 4) {\n\t\t\t\t\t\tconst usedTime = this.bytesToUint32(data, 0);\n\t\t\t\t\t\tconsole.log(`保养信息: 已使用时间: ${usedTime}小时`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('保养信息响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x84: // 电气参数响应\n\t\t\t\t\tif (data.length >= 6) {\n\t\t\t\t\t\tconst voltage = this.bytesToUint16(data, 0) / 100;\n\t\t\t\t\t\tconst current = this.bytesToUint16(data, 2) / 1000;\n\t\t\t\t\t\tconst power = this.bytesToUint16(data, 4) / 10;\n\t\t\t\t\t\tconsole.log(`电气参数: 电压: ${voltage}V, 电流: ${current}A, 功率: ${power}W`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('电气参数响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x85: // 保护状态响应\n\t\t\t\tcase 0xA5: // 保护触发通知\n\t\t\t\t\tif (data.length >= 1) {\n\t\t\t\t\t\tconst protectionStatusByte = data[0];\n\t\t\t\t\t\tconst shortCircuit = (protectionStatusByte & 0b00000001) ? '已触发' : '未触发';\n\t\t\t\t\t\tconst overcurrent = (protectionStatusByte & 0b00000010) ? '已触发' : '未触发';\n\t\t\t\t\t\tconst undervoltage = (protectionStatusByte & 0b00000100) ? '已触发' : '未触发';\n\t\t\t\t\t\tconst overtemperature = (protectionStatusByte & 0b00001000) ? '已触发' : '未触发';\n\t\t\t\t\t\tconsole.log(`保护状态: 短路: ${shortCircuit}, 过流: ${overcurrent}, 低压: ${undervoltage}, 过温: ${overtemperature}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('保护状态响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x86: // 空气质量响应\n\t\t\t\tcase 0xA6: // 空气质量变化通知\n\t\t\t\t\tif (data.length >= 1) {\n\t\t\t\t\t\tconst airQualityLevel = data[0];\n\t\t\t\t\t\tlet qualityText = '';\n\t\t\t\t\t\tswitch (airQualityLevel) {\n\t\t\t\t\t\t\tcase 0x01: qualityText = '优'; break;\n\t\t\t\t\t\t\tcase 0x02: qualityText = '良'; break;\n\t\t\t\t\t\t\tcase 0x03: qualityText = '轻度污染'; break;\n\t\t\t\t\t\t\tcase 0x04: qualityText = '中度污染'; break;\n\t\t\t\t\t\t\tcase 0x05: qualityText = '重度污染'; break;\n\t\t\t\t\t\t\tdefault: qualityText = '未知'; break;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(`空气质量: ${qualityText}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('空气质量响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x87: // 心跳响应\n\t\t\t\t\tconsole.log('✅ 心跳响应成功');\n\t\t\t\t\tthis.lastHeartbeatTime = Date.now(); // 更新最后心跳时间\n\t\t\t\t\tconst heartbeatMsg = `💓 心跳响应成功 - 设备连接正常`;\n\t\t\t\t\tthis.receivedMessages.push(heartbeatMsg);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '心跳响应成功',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x91: // 设置挡位响应\n\t\t\t\tcase 0x92: // 控制蜂鸣器响应\n\t\t\t\tcase 0x93: // 复位设备响应\n\t\t\t\tcase 0x94: // 复位计时器响应\n\t\t\t\tcase 0x95: // 复位保护状态响应\n\t\t\t\tcase 0x96: // 档位控制响应\n\t\t\t\tcase 0x97: // 蜂鸣器高级控制响应\n\t\t\t\tcase 0x98: // 负离子计数器复位响应\n\t\t\t\t\tif (data.length >= 1) {\n\t\t\t\t\t\tconst result = data[0];\n\t\t\t\t\t\tconst commandName = `0x${command.toString(16)}`\n\t\t\t\t\t\tif (result === 0x01) {\n\t\t\t\t\t\t\tconsole.log(`${commandName} 命令执行成功`);\n\t\t\t\t\t\t\tuni.showToast({ title: `${commandName} 命令执行成功`, icon: 'none'});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log(`${commandName} 命令执行失败`);\n\t\t\t\t\t\t\tuni.showToast({ title: `${commandName} 命令执行失败`, icon: 'none'});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('控制命令响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t\n\t\t\t\tcase 0x89: // 负离子数据响应\n\t\t\t\t\tif (data.length >= 5) {\n\t\t\t\t\t\tconst anionCount = this.bytesToUint32(data, 0);\n\t\t\t\t\t\tconst sensorStatus = data[4];\n\t\t\t\t\t\tlet statusText = '';\n\t\t\t\t\t\tswitch (sensorStatus) {\n\t\t\t\t\t\t\tcase 0x00: statusText = '不支持'; break;\n\t\t\t\t\t\t\tcase 0x01: statusText = '正常'; break;\n\t\t\t\t\t\t\tcase 0x02: statusText = '故障'; break;\n\t\t\t\t\t\t\tdefault: statusText = '未知'; break;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log(`负离子数据: 计数值: ${anionCount}个/cm³, 传感器状态: ${statusText}`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('负离子数据响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x8A: // 设备工作状态详细响应\n\t\t\t\t\tif (data.length >= 8) {\n\t\t\t\t\t\tconst workStatus = data[0];\n\t\t\t\t\t\tlet workStatusText = '';\n\t\t\t\t\t\tswitch (workStatus) {\n\t\t\t\t\t\t\tcase 0x00: workStatusText = '停机状态'; break;\n\t\t\t\t\t\t\tcase 0x01: workStatusText = '正常工作'; break;\n\t\t\t\t\t\t\tcase 0x02: workStatusText = '待机状态'; break;\n\t\t\t\t\t\t\tcase 0x03: workStatusText = '保护状态'; break;\n\t\t\t\t\t\t\tcase 0x04: workStatusText = '故障状态'; break;\n\t\t\t\t\t\t\tdefault: workStatusText = '未知'; break;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst voltageStatus = (data[1] === 0x01) ? '正常' : '异常';\n\t\t\t\t\t\tconst currentStatus = (data[2] === 0x01) ? '正常' : '异常';\n\t\t\t\t\t\tconst totalWorkTime = this.bytesToUint32(data, 3);\n\t\t\t\t\t\tconsole.log(`设备工作状态: ${workStatusText}, 电压状态: ${voltageStatus}, 电流状态: ${currentStatus}, 累计工作时间: ${totalWorkTime}秒`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('设备工作状态详细响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tcase 0x8F: // 错误响应\n\t\t\t\t\tif (data.length >= 2) {\n\t\t\t\t\t\tconst originalCommand = data[0];\n\t\t\t\t\t\tconst errorCode = data[1];\n\t\t\t\t\t\tlet errorText = '';\n\t\t\t\t\t\tswitch (errorCode) {\n\t\t\t\t\t\t\tcase 0x01: errorText = '参数错误'; break;\n\t\t\t\t\t\t\tcase 0x02: errorText = '命令不支持'; break;\n\t\t\t\t\t\t\tcase 0x03: errorText = '硬件错误'; break;\n\t\t\t\t\t\t\tdefault: errorText = '未知错误'; break;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.warn(`错误响应: 原始命令字: 0x${originalCommand.toString(16)}, 错误代码: 0x${errorCode.toString(16)} (${errorText})`);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('错误响应数据长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t\n\t\t\t\tcase 0xA3: // 保养提醒通知\n\t\t\t\t\tif (data.length >= 1 && data[0] === 0x01) {\n\t\t\t\t\t\tconsole.log('保养提醒: 需要更换滤芯');\n\t\t\t\t\t\tuni.showToast({ title: '需要更换滤芯', icon: 'none'});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('保养提醒通知数据异常或长度不足');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tconsole.log('未知命令: 0x' + command.toString(16));\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t// 构建并发送BLE命令包\n\t\tsendBleCommand(command, dataArea = []) {\n\t\t\tif (!this.connected || !this.deviceId || !this.serviceId || !this.characteristicId_write) {\n\t\t\t\tuni.showToast({ title: '蓝牙未连接或特征值不可用', icon: 'none' });\n\t\t\t\tconsole.warn('发送失败：蓝牙未连接或特征值不可用');\n\t\t\t\treturn;\n\t\t\t}\n\t\t    const dataLength = dataArea.length;\n\t\t    const packet = new Uint8Array(5 + dataLength);\n\t\t\n\t\t    packet[0] = 0xAA;\n\t\t    packet[1] = 0x55;\n\t\t    packet[2] = command;\n\t\t    packet[3] = dataLength;\n\t\t\n\t\t    for (let i = 0; i < dataLength; i++) {\n\t\t        packet[4 + i] = dataArea[i];\n\t\t    }\n\t\t\n\t\t    const checksumData = packet.slice(2, 4 + dataLength);\n\t\t    const checksum = this.crc8(checksumData, checksumData.length);\n\t\t    packet[4 + dataLength] = checksum;\n\t\t\n\t\t    console.log('准备发送BLE命令包:', Array.from(packet).map(byte => byte.toString(16).padStart(2, '0')).join(' '));\n\t\t\n\t\t    uni.writeBLECharacteristicValue({\n\t\t        deviceId: this.deviceId,\n\t\t        serviceId: this.serviceId,\n\t\t        characteristicId: this.characteristicId_write,\n\t\t        value: packet.buffer,\n\t\t        success(res) {\n\t\t            console.log('发送成功', res);\n\t\t            uni.showToast({ title: '命令发送成功', icon: 'success', duration: 1000 });\n\t\t        },\n\t\t        fail(err) {\n\t\t            console.error('发送失败', err);\n\t\t            uni.showToast({ title: '命令发送失败', icon: 'error', duration: 1000 });\n\t\t        }\n\t\t    });\n\t\t    return packet; // 返回以便测试或进一步处理\n\t\t},\n\t\tcrc8(data, length) {\n\t\t    let crc = 0;\n\t\t    for (let i = 0; i < length; i++) {\n\t\t        crc ^= data[i];\n\t\t        for (let j = 0; j < 8; j++) {\n\t\t            if (crc & 0x80) {\n\t\t                crc = (crc << 1) ^ 0x07;\n\t\t            } else {\n\t\t                crc <<= 1;\n\t\t            }\n\t\t            // 确保结果在8位范围内\n\t\t            crc &= 0xFF;\n\t\t        }\n\t\t    }\n\t\t    return crc & 0xFF; // 确保返回值在0-255范围内\n\t\t},\n\n\t\t// 简单累加和校验\n\t\tsimpleSum(data) {\n\t\t\tlet sum = 0;\n\t\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\t\tsum += data[i];\n\t\t\t}\n\t\t\treturn sum & 0xFF;\n\t\t},\n\n\t\t// XOR校验\n\t\txorChecksum(data) {\n\t\t\tlet xor = 0;\n\t\t\tfor (let i = 0; i < data.length; i++) {\n\t\t\t\txor ^= data[i];\n\t\t\t}\n\t\t\treturn xor & 0xFF;\n\t\t},\n\t\t// 查询命令\n\t\tqueryDeviceStatus() {\n\t\t\tconsole.log('发送：查询设备状态 (0x01)');\n\t\t\treturn this.sendBleCommand(0x01);\n\t\t},\n\t\tquerySensorData() {\n\t\t\tconsole.log('发送：查询传感器数据 (0x02)');\n\t\t\treturn this.sendBleCommand(0x02);\n\t\t},\n\t\tqueryMaintenanceInfo() {\n\t\t\tconsole.log('发送：查询保养信息 (0x03)');\n\t\t\treturn this.sendBleCommand(0x03);\n\t\t},\n\t\tqueryElectricalParameters() {\n\t\t\tconsole.log('发送：查询电气参数 (0x04)');\n\t\t\treturn this.sendBleCommand(0x04);\n\t\t},\n\t\tqueryProtectionStatus() {\n\t\t\tconsole.log('发送：查询保护状态 (0x05)');\n\t\t\treturn this.sendBleCommand(0x05);\n\t\t},\n\t\tqueryAirQuality() {\n\t\t\tconsole.log('发送：查询空气质量 (0x06)');\n\t\t\treturn this.sendBleCommand(0x06);\n\t\t},\n\t\tsendHeartbeat() {\n\t\t\tconsole.log('发送：心跳包 (0x07)');\n\t\t\tconst result = this.sendBleCommand(0x07);\n\t\t\tif (result) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '心跳包已发送',\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn result;\n\t\t},\n\t\t// 控制命令\n\t\tsetGear(gearValue) {\n\t\t\tconsole.log(`发送：设置挡位 (0x11), 挡位: ${gearValue}`);\n\t\t\treturn this.sendBleCommand(0x11, [gearValue]);\n\t\t},\n\t\tcontrolBuzzer(status) {\n\t\t\tconsole.log(`发送：控制蜂鸣器 (0x12), 状态: ${status}`);\n\t\t\treturn this.sendBleCommand(0x12, [status]);\n\t\t},\n\t\tresetDevice() {\n\t\t\tconsole.log('发送：复位设备 (0x13)');\n\t\t\treturn this.sendBleCommand(0x13, [0x01]);\n\t\t},\n\t\tresetTimer() {\n\t\t\tconsole.log('发送：复位计时器 (0x14)');\n\t\t\treturn this.sendBleCommand(0x14, [0x01]);\n\t\t},\n\t\tresetProtectionStatus(type) {\n\t\t\tconsole.log(`发送：复位保护状态 (0x15), 类型: ${type}`);\n\t\t\treturn this.sendBleCommand(0x15, [type]);\n\t\t},\n\t\tsetFanGear(gear) {\n\t\t\tconsole.log(`发送：档位控制 (0x16), 档位: ${gear}`);\n\t\t\treturn this.sendBleCommand(0x16, [gear]);\n\t\t},\n\t\tcontrolBuzzerAdvanced(frequency, duration) {\n\t\t\tconsole.log(`发送：蜂鸣器高级控制 (0x17), 频率: ${frequency}Hz, 持续时间: ${duration}ms`);\n\t\t\tconst freqBytes = [\n\t\t\t\tfrequency & 0xFF,\n\t\t\t\t(frequency >> 8) & 0xFF\n\t\t\t];\n\t\t\tconst durationBytes = [\n\t\t\t\tduration & 0xFF,\n\t\t\t\t(duration >> 8) & 0xFF\n\t\t\t];\n\t\t\treturn this.sendBleCommand(0x17, [...freqBytes, ...durationBytes]);\n\t\t},\n\t\tresetAnionCounter() {\n\t\t\tconsole.log(`发送：负离子计数器复位 (0x18)`);\n\t\t\treturn this.sendBleCommand(0x18, [0x01]);\n\t\t},\n\t\t// 测试发送原始数据\n\t\ttestRawData() {\n\t\t\tconsole.log('测试发送简单数据');\n\t\t\t// 发送一个简单的测试数据包\n\t\t\tconst testData = new Uint8Array([0x01, 0x02, 0x03, 0x04]);\n\n\t\t\tif (!this.connected || !this.deviceId || !this.serviceId || !this.characteristicId_write) {\n\t\t\t\tuni.showToast({ title: '蓝牙未连接', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tuni.writeBLECharacteristicValue({\n\t\t\t\tdeviceId: this.deviceId,\n\t\t\t\tserviceId: this.serviceId,\n\t\t\t\tcharacteristicId: this.characteristicId_write,\n\t\t\t\tvalue: testData.buffer,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('测试数据发送成功', res);\n\t\t\t\t\tuni.showToast({ title: '测试数据发送成功', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('测试数据发送失败', err);\n\t\t\t\t\tuni.showToast({ title: '测试数据发送失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 清空接收数据\n\t\tclearReceivedData() {\n\t\t\tthis.receivedMessages = [];\n\t\t\tuni.showToast({ title: '数据已清空', icon: 'success', duration: 1000 });\n\t\t},\n\n\t\t// 快捷控制方法\n\t\t// 切换蜂鸣器状态\n\t\ttoggleBuzzer() {\n\t\t\tconst newState = !this.currentBuzzerState;\n\t\t\tconst status = newState ? 1 : 0;\n\n\t\t\tconsole.log(`快捷控制：切换蜂鸣器 ${newState ? '开启' : '关闭'} (0x12)`);\n\n\t\t\t// 发送命令\n\t\t\tconst result = this.sendBleCommand(0x12, [status]);\n\t\t\tif (result) {\n\t\t\t\t// 不立即更新状态，等待设备响应\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `正在${newState ? '开启' : '关闭'}蜂鸣器...`,\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\n\t\t\t\t// 延迟查询设备状态以获取真实状态\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.queryDeviceStatus();\n\t\t\t\t}, 500);\n\t\t\t}\n\t\t},\n\n\t\t// 设置电压挡位\n\t\tsetVoltageGear(gear) {\n\t\t\tconsole.log(`快捷控制：设置电压挡位 ${gear} (0x11)`);\n\n\t\t\t// 发送命令\n\t\t\tconst result = this.sendBleCommand(0x11, [gear]);\n\t\t\tif (result) {\n\t\t\t\t// 不立即更新状态，等待设备响应\n\t\t\t\tconst gearText = gear === 0 ? '关闭' : `${gear}挡`;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `正在设置为${gearText}...`,\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\n\t\t\t\t// 延迟查询设备状态以获取真实状态\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.queryDeviceStatus();\n\t\t\t\t}, 500);\n\t\t\t}\n\t\t},\n\t\t// 蓝牙适配器状态变化监听\n\t\tonBluetoothAdapterStateChange(res) {\n\t\t\tconsole.log('蓝牙适配器状态变化:', res.available, res.discovering);\n\t\t\tthis.isBluetoothAvailable = res.available;\n\t\t\tthis.isScanning = res.discovering;\n\n\t\t\tif (!res.available) {\n\t\t\t\t// 蓝牙不可用时，重置所有状态\n\t\t\t\tthis.resetConnectionState();\n\t\t\t\tthis.devices = [];\n\t\t\t\tuni.showToast({ title: '蓝牙适配器不可用，请检查蓝牙是否开启', icon: 'none' });\n\t\t\t} else {\n\t\t\t\t// 蓝牙恢复可用\n\t\t\t\tconsole.log('蓝牙适配器已恢复可用');\n\t\t\t}\n\t\t},\n\t\t// 监听发现新设备\n\t\tonBluetoothDeviceFound(res) {\n\t\t\tres.devices.forEach(device => {\n\t\t\t\tif (!this.devices.some(item => item.deviceId === device.deviceId)) {\n\t\t\t\t\tthis.devices.push(device);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// BLE连接状态变化监听\n\t\tonBLEConnectionStateChange(res) {\n\t\t\tconsole.log('BLE连接状态变化:', res.deviceId, res.connected);\n\t\t\tthis.connected = res.connected;\n\t\t\tif (!res.connected) {\n\t\t\t\tuni.showToast({ title: `设备 ${res.deviceId} 已断开连接`, icon: 'none' });\n\t\t\t\tthis.deviceName = '';\n\t\t\t\tthis.deviceId = '';\n\t\t\t\tthis.serviceId = '';\n\t\t\t\tthis.characteristicId_write = '';\n\t\t\t\tthis.characteristicId_notify = '';\n\t\t\t}\n\t\t},\n\t\t// BLE特征值变化监听\n\t\tonBLECharacteristicValueChange(res) {\n\t\t\tconsole.log('--- onBLECharacteristicValueChange triggered ---', res); // 新增日志\n\t\t\tconst buffer = new Uint8Array(res.value);\n\t\t\tconst hexStr = Array.prototype.map.call(buffer, byte => ('00' + byte.toString(16)).slice(-2)).join(' ');\n\t\t\tconst msg = `收到通知: CharacteristicId ${res.characteristicId}, Value: [${buffer}], Hex: ${hexStr}`;\n\t\t\tthis.receivedMessages.push(msg);\n\n\t\t\t// 添加调试信息，显示接收到数据的特征值UUID\n\t\t\tconsole.log('接收数据的特征值UUID:', res.characteristicId);\n\t\t\tconsole.log('接收到的原始数据:', hexStr);\n\n\t\t\tthis.receiveDataFromBle(res.value); // 调用协议解析函数\n\t\t},\n\n\t\t// 初始化蓝牙适配器\n\t\tinitBluetoothAdapter() {\n\t\t\tuni.openBluetoothAdapter({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('initBluetoothAdapter success', res);\n\t\t\t\t\tthis.isBluetoothAvailable = true;\n\t\t\t\t\tuni.showToast({ title: '蓝牙适配器初始化成功', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('initBluetoothAdapter fail', err);\n\t\t\t\t\tthis.isBluetoothAvailable = false;\n\t\t\t\t\tuni.showToast({ title: '蓝牙适配器初始化失败，请检查蓝牙是否打开', icon: 'none' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 开始扫描设备\n\t\tstartScan() {\n\t\t\tif (!this.isBluetoothAvailable) {\n\t\t\t\tuni.showToast({ title: '蓝牙适配器不可用', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tuni.startBluetoothDevicesDiscovery({\n\t\t\t\tallowDuplicatesKey: false,\n\t\t\t\tinterval: 0,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('startBluetoothDevicesDiscovery success', res);\n\t\t\t\t\tthis.isScanning = true;\n\t\t\t\t\tthis.devices = []; // 清空设备列表\n\t\t\t\t\tuni.onBluetoothDeviceFound(this.onBluetoothDeviceFound); // 监听发现新设备\n\t\t\t\t\tuni.showToast({ title: '开始扫描蓝牙设备', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('startBluetoothDevicesDiscovery fail', err);\n\t\t\t\t\tthis.isScanning = false;\n\t\t\t\t\tuni.showToast({ title: '扫描失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 停止扫描设备\n\t\tstopScan() {\n\t\t\tuni.stopBluetoothDevicesDiscovery({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('stopBluetoothDevicesDiscovery success', res);\n\t\t\t\t\tthis.isScanning = false;\n\t\t\t\t\tuni.offBluetoothDeviceFound(this.onBluetoothDeviceFound); // 停止监听发现新设备\n\t\t\t\t\tuni.showToast({ title: '停止扫描', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('stopBluetoothDevicesDiscovery fail', err);\n\t\t\t\t\tuni.showToast({ title: '停止扫描失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 连接设备\n\t\tconnectDevice(deviceId) {\n\t\t\tthis.connectDeviceWithRetry(deviceId, 0);\n\t\t},\n\n\t\t// 带重试机制的连接设备方法\n\t\tconnectDeviceWithRetry(deviceId, retryCount) {\n\t\t\tconst maxRetries = 3;\n\t\t\tconst retryDelay = 2000; // 2秒延迟\n\n\t\t\tuni.showLoading({ title: `连接中${retryCount > 0 ? ` (重试${retryCount}/${maxRetries})` : ''}...` });\n\n\t\t\t// 先停止扫描\n\t\t\tif (this.isScanning) {\n\t\t\t\tthis.stopScan();\n\t\t\t}\n\n\t\t\t// 如果之前有连接，先断开\n\t\t\tif (this.connected && this.deviceId) {\n\t\t\t\tconsole.log('断开之前的连接...');\n\t\t\t\tuni.closeBLEConnection({\n\t\t\t\t\tdeviceId: this.deviceId,\n\t\t\t\t\tsuccess: () => console.log('之前连接已断开'),\n\t\t\t\t\tfail: () => console.log('断开之前连接失败，继续新连接')\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// 延迟一下再连接，给设备一些时间\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.createBLEConnection({\n\t\t\t\t\tdeviceId: deviceId,\n\t\t\t\t\ttimeout: 15000, // 增加超时时间到15秒\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('createBLEConnection success', res);\n\t\t\t\t\t\tthis.connected = true;\n\t\t\t\t\t\tthis.deviceId = deviceId;\n\t\t\t\t\t\tconst connectedDevice = this.devices.find(d => d.deviceId === deviceId);\n\t\t\t\t\t\tif (connectedDevice) {\n\t\t\t\t\t\t\tthis.deviceName = connectedDevice.name || '未知设备';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tuni.showToast({ title: '连接成功', icon: 'success' });\n\n\t\t\t\t\t\t// 连接成功处理\n\t\t\t\t\t\tthis.onConnectionSuccess();\n\n\t\t\t\t\t\t// 获取服务和特征值\n\t\t\t\t\t\tthis.getServicesAndCharacteristics(deviceId);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('createBLEConnection fail', err);\n\t\t\t\t\t\tthis.connected = false;\n\n\t\t\t\t\t\t// 分析错误类型\n\t\t\t\t\t\tlet errorMsg = '连接失败';\n\t\t\t\t\t\tif (err.errCode === 10003) {\n\t\t\t\t\t\t\terrorMsg = '设备连接失败，可能设备忙碌或距离过远';\n\t\t\t\t\t\t} else if (err.errCode === 10012) {\n\t\t\t\t\t\t\terrorMsg = '连接超时，请检查设备是否开启';\n\t\t\t\t\t\t} else if (err.errCode === 10001) {\n\t\t\t\t\t\t\terrorMsg = '蓝牙适配器未初始化';\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 重试逻辑\n\t\t\t\t\t\tif (retryCount < maxRetries) {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: `${errorMsg}，${retryDelay/1000}秒后重试...`,\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.connectDeviceWithRetry(deviceId, retryCount + 1);\n\t\t\t\t\t\t\t}, retryDelay);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '连接失败',\n\t\t\t\t\t\t\t\tcontent: `${errorMsg}\\n\\n建议操作：\\n1. 确认设备已开启且在附近\\n2. 重启设备蓝牙功能\\n3. 清除设备缓存后重试\\n4. 检查设备是否被其他应用占用`,\n\t\t\t\t\t\t\t\tshowCancel: true,\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tconfirmText: '重试',\n\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t// 用户选择重试，重置重试计数\n\t\t\t\t\t\t\t\t\t\tthis.connectDeviceWithRetry(deviceId, 0);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}, retryCount > 0 ? 1000 : 500); // 重试时延迟更长\n\t\t},\n\t\t// 断开连接\n\t\tdisconnectDevice() {\n\t\t\tif (!this.deviceId) {\n\t\t\t\tuni.showToast({ title: '没有连接的设备', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tuni.closeBLEConnection({\n\t\t\t\tdeviceId: this.deviceId,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('closeBLEConnection success', res);\n\t\t\t\t\tthis.resetConnectionState();\n\t\t\t\t\tuni.showToast({ title: '断开连接成功', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('closeBLEConnection fail', err);\n\t\t\t\t\t// 即使断开失败，也重置连接状态\n\t\t\t\t\tthis.resetConnectionState();\n\t\t\t\t\tuni.showToast({ title: '断开连接失败，已重置状态', icon: 'none' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 重置连接状态\n\t\tresetConnectionState() {\n\t\t\tthis.connected = false;\n\t\t\tthis.deviceName = '';\n\t\t\tthis.deviceId = '';\n\t\t\tthis.serviceId = '';\n\t\t\tthis.characteristicId_write = '';\n\t\t\tthis.characteristicId_notify = '';\n\t\t\tthis.receivedMessages = [];\n\t\t\t// 重置快捷控制状态\n\t\t\tthis.currentBuzzerState = false;\n\t\t\tthis.currentVoltageGear = 0;\n\t\t\t// 停止连接监控\n\t\t\tthis.stopConnectionMonitor();\n\t\t\t// 重置重连计数\n\t\t\tthis.reconnectAttempts = 0;\n\t\t\tthis.lastHeartbeatTime = 0;\n\t\t},\n\n\t\t// 清理蓝牙缓存（Android专用）\n\t\tclearBluetoothCache() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\tif (uni.getSystemInfoSync().platform === 'android') {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '清理蓝牙缓存',\n\t\t\t\t\tcontent: '是否清理蓝牙缓存？这可能有助于解决连接问题。',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 这里可以调用原生插件清理蓝牙缓存\n\t\t\t\t\t\t\t// 目前只是提示用户手动操作\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '手动清理提示',\n\t\t\t\t\t\t\t\tcontent: '请前往系统设置 > 应用管理 > 蓝牙 > 存储 > 清除缓存',\n\t\t\t\t\t\t\t\tshowCancel: false\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\n\t\t// 显示连接帮助提示\n\t\tshowConnectionTips() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '蓝牙连接帮助',\n\t\t\t\tcontent: `连接失败的常见原因和解决方法：\n\n1. 设备距离过远\n   → 靠近设备重试\n\n2. 设备被其他应用占用\n   → 关闭其他蓝牙应用\n\n3. 蓝牙缓存问题\n   → 点击\"清理缓存\"按钮\n\n4. 设备蓝牙故障\n   → 重启设备蓝牙功能\n\n5. 系统蓝牙异常\n   → 重启手机蓝牙\n\n错误码说明：\n• 10003: 连接失败，设备忙碌\n• 10012: 连接超时\n• 10001: 蓝牙未初始化`,\n\t\t\t\tshowCancel: false,\n\t\t\t\tconfirmText: '知道了'\n\t\t\t});\n\t\t},\n\n\t\t// 启动连接监控\n\t\tstartConnectionMonitor() {\n\t\t\tif (this.connectionMonitorTimer) {\n\t\t\t\tclearInterval(this.connectionMonitorTimer);\n\t\t\t}\n\n\t\t\tthis.connectionMonitorTimer = setInterval(() => {\n\t\t\t\tif (this.connected && this.deviceId) {\n\t\t\t\t\t// 发送心跳包检测连接状态\n\t\t\t\t\tthis.sendHeartbeat();\n\n\t\t\t\t\t// 检查心跳响应超时\n\t\t\t\t\tconst now = Date.now();\n\t\t\t\t\tif (this.lastHeartbeatTime > 0 && (now - this.lastHeartbeatTime) > 60000) {\n\t\t\t\t\t\tconsole.warn('心跳超时，可能连接已断开');\n\t\t\t\t\t\tthis.handleConnectionLost();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}, 30000); // 每30秒检查一次\n\t\t},\n\n\t\t// 停止连接监控\n\t\tstopConnectionMonitor() {\n\t\t\tif (this.connectionMonitorTimer) {\n\t\t\t\tclearInterval(this.connectionMonitorTimer);\n\t\t\t\tthis.connectionMonitorTimer = null;\n\t\t\t}\n\t\t},\n\n\t\t// 处理连接丢失\n\t\thandleConnectionLost() {\n\t\t\tconsole.log('检测到连接丢失');\n\t\t\tthis.connected = false;\n\n\t\t\tif (this.autoReconnectEnabled && this.reconnectAttempts < this.maxReconnectAttempts) {\n\t\t\t\tthis.reconnectAttempts++;\n\t\t\t\tconsole.log(`尝试自动重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `连接丢失，正在重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,\n\t\t\t\t\ticon: 'loading',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tif (this.deviceId) {\n\t\t\t\t\t\tthis.connectDeviceWithRetry(this.deviceId, 0);\n\t\t\t\t\t}\n\t\t\t\t}, 3000);\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '连接已断开',\n\t\t\t\t\ticon: 'error'\n\t\t\t\t});\n\t\t\t\tthis.resetConnectionState();\n\t\t\t}\n\t\t},\n\n\t\t// 连接成功后的处理\n\t\tonConnectionSuccess() {\n\t\t\tthis.reconnectAttempts = 0;\n\t\t\tthis.lastHeartbeatTime = Date.now();\n\t\t\tthis.startConnectionMonitor();\n\t\t},\n\n\t\t// 获取服务和特征值\n\t\tgetServicesAndCharacteristics(deviceId) {\n\t\t\tuni.getBLEDeviceServices({\n\t\t\t\tdeviceId: deviceId,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('getBLEDeviceServices success', res.services);\n\t\t\t\t\tconst targetService = res.services.find(s => s.uuid.toUpperCase().includes('0000FF00'));\n\t\t\t\t\tif (targetService) {\n\t\t\t\t\t\tthis.serviceId = targetService.uuid;\n\t\t\t\t\t\tuni.getBLEDeviceCharacteristics({\n\t\t\t\t\t\t\tdeviceId: deviceId,\n\t\t\t\t\t\t\tserviceId: targetService.uuid,\n\t\t\t\t\t\t\tsuccess: (charRes) => {\n\t\t\t\t\t\t\t\tconsole.log('getBLEDeviceCharacteristics success', charRes.characteristics);\n\n\t\t\t\t\t\t\t\t// 打印所有特征值的详细信息\n\t\t\t\t\t\t\t\tcharRes.characteristics.forEach((char, index) => {\n\t\t\t\t\t\t\t\t\tconsole.log(`特征值${index}: UUID=${char.uuid}, 属性=`, char.properties);\n\t\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\t\t// 查找写入特征值 - 使用FF02\n\t\t\t\t\t\t\t\tlet writeChar = charRes.characteristics.find(c => c.uuid.toUpperCase().includes('0000FF02') && c.properties.write);\n\n\t\t\t\t\t\t\t\t// 如果找不到0xFF02，尝试查找其他可写特征值\n\t\t\t\t\t\t\t\tif (!writeChar) {\n\t\t\t\t\t\t\t\t\twriteChar = charRes.characteristics.find(c => c.properties.write);\n\t\t\t\t\t\t\t\t\tif (writeChar) {\n\t\t\t\t\t\t\t\t\t\tconsole.log('未找到0xFF02，使用其他可写特征值:', writeChar.uuid);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t// 使用协议定义的通知特征值 FF02\n\t\t\t\t\t\t\t\tconst notifyChar = charRes.characteristics.find(c =>\n\t\t\t\t\t\t\t\t\tc.uuid.toUpperCase().includes('0000FF02') &&\n\t\t\t\t\t\t\t\t\tc.properties.notify\n\t\t\t\t\t\t\t\t);\n\n\t\t\t\t\t\t\t\tif (notifyChar) {\n\t\t\t\t\t\t\t\t\tconsole.log('找到协议定义的通知特征值:', notifyChar.uuid);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (writeChar) {\n\t\t\t\t\t\t\t\t\tthis.characteristicId_write = writeChar.uuid;\n\t\t\t\t\t\t\t\t\tconsole.log('找到可写特征值 (FF02):', this.characteristicId_write); // 新增日志\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.warn('未找到可写特征值 (0x0000FF02)');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (notifyChar) {\n\t\t\t\t\t\t\t\t\tthis.characteristicId_notify = notifyChar.uuid;\n\t\t\t\t\t\t\t\t\tconsole.log('找到可通知特征值:', this.characteristicId_notify); // 新增日志\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 延迟订阅通知，以解决可能的时序问题\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tthis.notifyCharacteristicValueChange(deviceId, targetService.uuid, notifyChar.uuid); // 订阅通知\n\t\t\t\t\t\t\t\t\t}, 200); // 延迟200ms\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tconsole.warn('未找到可通知特征值 (0x0000FF02)');\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tuni.showToast({ title: '服务和特征值发现成功', icon: 'success' });\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (charErr) => {\n\t\t\t\t\t\t\t\tconsole.error('getBLEDeviceCharacteristics fail', charErr);\n\t\t\t\t\t\t\t\tuni.showToast({ title: '获取特征值失败', icon: 'error' });\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('未找到服务 0x0000FF00');\n\t\t\t\t\t\tuni.showToast({ title: '未找到指定服务', icon: 'none' });\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('getBLEDeviceServices fail', err);\n\t\t\t\t\tuni.showToast({ title: '获取服务失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 启用特征值通知\n\t\tnotifyCharacteristicValueChange(deviceId, serviceId, characteristicId) {\n\t\t\tuni.notifyBLECharacteristicValueChange({\n\t\t\t\tstate: true, // 启用notify\n\t\t\t\tdeviceId: deviceId,\n\t\t\t\tserviceId: serviceId,\n\t\t\t\tcharacteristicId: characteristicId,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tconsole.log('notifyBLECharacteristicValueChange success', res.errMsg);\n\t\t\t\t\tuni.showToast({ title: '已订阅通知', icon: 'success' });\n\t\t\t\t},\n\t\t\t\tfail: (err) => {\n\t\t\t\t\tconsole.error('notifyBLECharacteristicValueChange fail', err);\n\t\t\t\t\tuni.showToast({ title: '订阅通知失败', icon: 'error' });\n\t\t\t\t}\n\t\t\t});\n\t\t},\n    }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.content {\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 20rpx;\n\tbackground: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n\tmin-height: 100vh;\n}\n\n/* 按钮样式 */\n.btn {\n\tborder: none;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx 30rpx;\n\tmargin: 10rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\ttransition: all 0.3s ease;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\n\t&:disabled {\n\t\topacity: 0.5;\n\t\ttransform: none !important;\n\t\tbox-shadow: none !important;\n\t}\n\n\t&:not(:disabled):active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n\t}\n}\n\n.btn-primary {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: white;\n}\n\n.btn-success {\n\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n\tcolor: white;\n}\n\n.btn-warning {\n\tbackground: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n\tcolor: white;\n}\n\n.btn-danger {\n\tbackground: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);\n\tcolor: #333;\n}\n\n.btn-query {\n\tbackground: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);\n\tcolor: #333;\n\tfont-size: 24rpx;\n\tpadding: 15rpx 20rpx;\n}\n\n.btn-control {\n\tbackground: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n\tcolor: #333;\n\tfont-size: 24rpx;\n\tpadding: 15rpx 20rpx;\n}\n\n.btn-test {\n\tbackground: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);\n\tcolor: white;\n}\n\n.btn-connect {\n\tbackground: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);\n\tcolor: #333;\n\tfont-size: 24rpx;\n\tpadding: 12rpx 20rpx;\n}\n\n.btn-send {\n\tbackground: linear-gradient(135deg, #fa709a 0%, #fee140 100%);\n\tcolor: #333;\n\tfont-size: 24rpx;\n\tpadding: 12rpx 20rpx;\n\tmin-width: 120rpx;\n}\n\n.btn-clear {\n\tbackground: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);\n\tcolor: #333;\n\tfont-size: 22rpx;\n\tpadding: 8rpx 16rpx;\n}\n\n.btn-toggle {\n\tbackground: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);\n\tcolor: #333;\n\tfont-size: 28rpx;\n\tpadding: 20rpx 40rpx;\n\tmin-width: 200rpx;\n\n\t&.active {\n\t\tbackground: linear-gradient(135deg, #00b894 0%, #00cec9 100%);\n\t\tcolor: white;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.3);\n\t}\n}\n\n.btn-gear {\n\tbackground: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);\n\tcolor: #333;\n\tfont-size: 26rpx;\n\tpadding: 18rpx 30rpx;\n\tmin-width: 120rpx;\n\n\t&.active {\n\t\tbackground: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);\n\t\tcolor: white;\n\t\tbox-shadow: 0 6rpx 20rpx rgba(9, 132, 227, 0.3);\n\t\ttransform: scale(1.05);\n\t}\n}\n\n/* 卡片样式 */\n.status-card, .device-list-card, .data-card, .command-card {\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder-radius: 20rpx;\n\tmargin-bottom: 30rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n\tbackdrop-filter: blur(10rpx);\n\toverflow: hidden;\n}\n\n/* 卡片头部 */\n.card-header, .status-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: white;\n}\n\n.card-title, .status-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.device-count {\n\tbackground: rgba(255, 255, 255, 0.2);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n/* 状态指示器 */\n.status-indicator {\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n\n\t&.connected {\n\t\tbackground: rgba(76, 175, 80, 0.2);\n\t\tcolor: #4CAF50;\n\t}\n\n\t&.disconnected {\n\t\tbackground: rgba(244, 67, 54, 0.2);\n\t\tcolor: #F44336;\n\t}\n}\n\n/* 状态详情 */\n.status-details {\n\tpadding: 30rpx;\n}\n\n.status-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 15rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t&:last-child {\n\t\tborder-bottom: none;\n\t}\n}\n\n.status-label {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.status-value {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\n\t&.success { color: #4CAF50; }\n\t&.error { color: #F44336; }\n\t&.warning { color: #FF9800; }\n\t&.info { color: #2196F3; }\n}\n\n/* 按钮组 */\n.button-group {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: space-between;\n\tmargin-bottom: 30rpx;\n}\n\n/* 调试按钮组 */\n.debug-button-group {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tjustify-content: space-around;\n\tmargin-bottom: 30rpx;\n\tpadding: 20rpx;\n\tbackground: rgba(255, 255, 255, 0.7);\n\tborder-radius: 15rpx;\n\tborder: 2rpx dashed #ccc;\n}\n\n.btn-debug {\n\tbackground: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n\tcolor: white;\n\tfont-size: 24rpx;\n\tpadding: 15rpx 25rpx;\n\tmargin: 5rpx;\n\tmin-width: 140rpx;\n}\n\n/* 设备列表 */\n.device-scroll {\n\theight: 300rpx;\n\tpadding: 20rpx;\n}\n\n.device-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx;\n\tmargin-bottom: 15rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n\tborder-left: 6rpx solid #667eea;\n}\n\n.device-info {\n\tflex: 1;\n}\n\n.device-name {\n\tdisplay: block;\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n}\n\n.device-id {\n\tdisplay: block;\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 数据显示 */\n.data-scroll {\n\theight: 400rpx;\n\tpadding: 20rpx;\n}\n\n.message-item {\n\tpadding: 15rpx;\n\tmargin-bottom: 10rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 8rpx;\n\tborder-left: 4rpx solid #4CAF50;\n}\n\n.message-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tword-break: break-all;\n}\n\n/* 命令组 */\n.command-group {\n\tmargin-bottom: 40rpx;\n\tpadding: 30rpx;\n}\n\n.group-title {\n\tdisplay: block;\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tpadding-bottom: 10rpx;\n\tborder-bottom: 2rpx solid #667eea;\n}\n\n.command-buttons {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 15rpx;\n}\n\n/* 参数控制 */\n.param-controls {\n\tmargin-top: 30rpx;\n}\n\n.control-item {\n\tmargin-bottom: 25rpx;\n\tpadding: 20rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n}\n\n.control-label {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 15rpx;\n}\n\n.control-input, .control-input-group {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n}\n\n.control-input-group {\n\tflex-wrap: wrap;\n}\n\n.input-field {\n\tflex: 1;\n\tmin-width: 200rpx;\n\tpadding: 15rpx;\n\tborder: 2rpx solid #e0e0e0;\n\tborder-radius: 8rpx;\n\tfont-size: 26rpx;\n\tbackground: white;\n\n\t&:focus {\n\t\tborder-color: #667eea;\n\t\toutline: none;\n\t}\n}\n\n/* 快捷控制 */\n.quick-control-section {\n\tmargin-bottom: 30rpx;\n\tpadding: 25rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n\tborder-radius: 15rpx;\n\tborder-left: 6rpx solid #667eea;\n}\n\n.control-section-title {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #495057;\n\tmargin-bottom: 20rpx;\n}\n\n.toggle-buttons {\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.gear-buttons {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 15rpx;\n\tjustify-content: space-between;\n}\n\n/* 空状态 */\n.empty-state {\n\ttext-align: center;\n\tpadding: 60rpx 20rpx;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\""], "sourceRoot": ""}