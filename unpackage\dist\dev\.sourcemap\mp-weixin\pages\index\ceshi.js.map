{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/yingxiao/bleTest/pages/index/ceshi.vue?a6f7", "webpack:///E:/yingxiao/bleTest/pages/index/ceshi.vue?a2c1", "webpack:///E:/yingxiao/bleTest/pages/index/ceshi.vue?562e", "webpack:///E:/yingxiao/bleTest/pages/index/ceshi.vue?111e", "uni-app:///pages/index/ceshi.vue", "webpack:///E:/yingxiao/bleTest/pages/index/ceshi.vue?e6a4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "avatarUrl", "nickname", "defaultAvatar", "onLoad", "methods", "onChooseAvatar", "saveUserInfo", "uni", "title", "loadUserInfo"], "mappings": "2IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,gCCRvB,wHAA+mB,eAAG,G,qHCkClnB,CACAC,gBACA,OACAC,aACAC,YACAC,6CAGAC,kBAEA,qBAEAC,SAEAC,2BACA,kCACA,qBAIAC,wBACAC,6BACAP,yBACAC,yBAEAM,aAAAC,gBAIAC,wBACA,mCACA,IACA,2BACA,6BAIA,c,4DCvEA,wHAAy5B,eAAG,G", "file": "pages/index/ceshi.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/ceshi.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ceshi.vue?vue&type=template&id=5ca119a8&scoped=true&\"\nvar renderjs\nimport script from \"./ceshi.vue?vue&type=script&lang=js&\"\nexport * from \"./ceshi.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ceshi.vue?vue&type=style&index=0&id=5ca119a8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5ca119a8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/ceshi.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ceshi.vue?vue&type=template&id=5ca119a8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ceshi.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ceshi.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 使用官方头像选择按钮 -->\n    <button \n      open-type=\"chooseAvatar\" \n      @chooseavatar=\"onChooseAvatar\"\n      class=\"avatar-btn\"\n    >\n      <image \n        :src=\"avatarUrl || defaultAvatar\" \n        class=\"avatar\" \n        mode=\"aspectFill\"\n      ></image>\n    </button>\n    \n    <!-- 表单区域 -->\n    <view class=\"form\">\n      <view class=\"form-item\">\n        <view class=\"form-label\">昵称</view>\n        <input \n          class=\"form-input\" \n          type=\"text\" \n          placeholder=\"请输入昵称\" \n          v-model=\"nickname\"\n        />\n      </view>\n    </view>\n    \n    <!-- 保存按钮 -->\n    <button class=\"save-btn\" @click=\"saveUserInfo\">保存</button>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      avatarUrl: '',           // 用户头像URL\n      nickname: '',            // 用户昵称\n      defaultAvatar: '/static/default-avatar.png' // 默认头像\n    }\n  },\n  onLoad() {\n    // 加载本地缓存的用户信息\n    this.loadUserInfo();\n  },\n  methods: {\n    // 处理头像选择事件\n    onChooseAvatar(e) {\n      this.avatarUrl = e.detail.avatarUrl;\n      this.saveUserInfo(); // 自动保存\n    },\n    \n    // 保存用户信息到本地\n    saveUserInfo() {\n      uni.setStorageSync('userInfo', {\n        avatarUrl: this.avatarUrl,\n        nickname: this.nickname\n      });\n      uni.showToast({ title: '保存成功' });\n    },\n    \n    // 从本地加载用户信息\n    loadUserInfo() {\n      const userInfo = uni.getStorageSync('userInfo');\n      if (userInfo) {\n        this.avatarUrl = userInfo.avatarUrl;\n        this.nickname = userInfo.nickname;\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.container {\n  padding: 30rpx;\n}\n\n.avatar-btn {\n  display: block;\n  width: 200rpx;\n  height: 200rpx;\n  padding: 0;\n  margin: 40rpx auto;\n  border: none;\n  background-color: transparent;\n}\n\n.avatar {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background-color: #f5f5f5;\n}\n\n.form {\n  background-color: #fff;\n  border-radius: 12rpx;\n  margin-top: 30rpx;\n}\n\n.form-item {\n  display: flex;\n  align-items: center;\n  height: 100rpx;\n  padding: 0 30rpx;\n  border-bottom: 1rpx solid #eee;\n}\n\n.form-label {\n  width: 180rpx;\n  font-size: 28rpx;\n  color: #333;\n}\n\n.form-input {\n  flex: 1;\n  font-size: 28rpx;\n  color: #666;\n  text-align: right;\n}\n\n.save-btn {\n  width: 100%;\n  height: 90rpx;\n  line-height: 90rpx;\n  background-color: #07c160;\n  color: #fff;\n  border-radius: 45rpx;\n  font-size: 32rpx;\n  margin-top: 60rpx;\n}\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ceshi.vue?vue&type=style&index=0&id=5ca119a8&scoped=true&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ceshi.vue?vue&type=style&index=0&id=5ca119a8&scoped=true&lang=css&\""], "sourceRoot": ""}