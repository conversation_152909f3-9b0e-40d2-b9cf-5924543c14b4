<view class="content data-v-57280228"><view data-event-opts="{{[['tap',[['ceshi',['$event']]]]]}}" style="text-align:center;background-color:antiquewhite;margin:10px;" bindtap="__e" class="data-v-57280228">ceshi</view><view class="button-group data-v-57280228"><button class="btn btn-primary data-v-57280228" disabled="{{isBluetoothAvailable}}" data-event-opts="{{[['tap',[['initBluetoothAdapter',['$event']]]]]}}" bindtap="__e">🔧 初始化蓝牙</button><button class="btn btn-success data-v-57280228" disabled="{{!isBluetoothAvailable||isScanning||connected}}" data-event-opts="{{[['tap',[['startScan',['$event']]]]]}}" bindtap="__e">🔍 扫描设备</button><button class="btn btn-warning data-v-57280228" disabled="{{!isScanning}}" data-event-opts="{{[['tap',[['stopScan',['$event']]]]]}}" bindtap="__e">⏹️ 停止扫描</button><button class="btn btn-danger data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['disconnectDevice',['$event']]]]]}}" bindtap="__e">🔌 断开连接</button></view><view class="debug-button-group data-v-57280228"><button data-event-opts="{{[['tap',[['showConnectionTips',['$event']]]]]}}" class="btn btn-debug data-v-57280228" bindtap="__e">💡 连接帮助</button><button data-event-opts="{{[['tap',[['clearBluetoothCache',['$event']]]]]}}" class="btn btn-debug data-v-57280228" bindtap="__e">🧹 清理缓存</button><button data-event-opts="{{[['tap',[['resetConnectionState',['$event']]]]]}}" class="btn btn-debug data-v-57280228" bindtap="__e">🔄 重置状态</button><button class="btn btn-debug data-v-57280228" disabled="{{!$root.g0}}" data-event-opts="{{[['tap',[['debugModeConnect',['$event']]]]]}}" bindtap="__e">🔧 调试模式连接</button></view><view class="status-card data-v-57280228"><view class="status-header data-v-57280228"><text class="status-title data-v-57280228">📡 连接状态</text><view class="{{['status-indicator','data-v-57280228',(connected)?'connected':'',(!connected)?'disconnected':'']}}">{{''+(connected?'🟢 已连接':'🔴 未连接')+''}}</view></view><view class="status-details data-v-57280228"><view class="status-item data-v-57280228"><text class="status-label data-v-57280228">蓝牙适配器:</text><text class="{{['status-value','data-v-57280228',(isBluetoothAvailable)?'success':'',(!isBluetoothAvailable)?'error':'']}}">{{''+(isBluetoothAvailable?'✅ 可用':'❌ 不可用')+''}}</text></view><view class="status-item data-v-57280228"><text class="status-label data-v-57280228">扫描状态:</text><text class="{{['status-value','data-v-57280228',(isScanning)?'warning':'']}}">{{''+(isScanning?'🔍 扫描中...':'⏸️ 未扫描')+''}}</text></view><block wx:if="{{deviceName}}"><view class="status-item data-v-57280228"><text class="status-label data-v-57280228">设备名称:</text><text class="status-value success data-v-57280228">{{deviceName}}</text></view></block><block wx:if="{{serviceId}}"><view class="status-item data-v-57280228"><text class="status-label data-v-57280228">服务ID:</text><text class="status-value info data-v-57280228">{{serviceId}}</text></view></block></view></view><block wx:if="{{connected}}"><view class="air-quality-card data-v-57280228"><view class="card-header data-v-57280228"><text class="card-title data-v-57280228">🌬️ 空气质量监测</text><button data-event-opts="{{[['tap',[['queryAirQuality',['$event']]]]]}}" class="btn btn-refresh data-v-57280228" bindtap="__e">刷新</button></view><view class="air-quality-content data-v-57280228"><view class="air-quality-main data-v-57280228"><view class="air-quality-icon data-v-57280228">{{currentAirQuality.icon}}</view><view class="air-quality-info data-v-57280228"><text class="air-quality-level data-v-57280228" style="{{'color:'+(currentAirQuality.color)+';'}}">{{''+currentAirQuality.text+''}}</text><text class="air-quality-desc data-v-57280228">当前空气质量</text></view></view><block wx:if="{{sensorData.lastUpdate>0}}"><view class="sensor-data-grid data-v-57280228"><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">PM2.5</text><text class="sensor-value data-v-57280228">{{sensorData.pm25+" μg/m³"}}</text></view><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">VOC</text><text class="sensor-value data-v-57280228">{{sensorData.voc+" ppm"}}</text></view><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">负离子</text><text class="sensor-value data-v-57280228">{{sensorData.anionConcentration+" 个/cm³"}}</text></view><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">温度</text><text class="sensor-value data-v-57280228">{{sensorData.temperature+"°C"}}</text></view><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">湿度</text><text class="sensor-value data-v-57280228">{{sensorData.humidity+"%RH"}}</text></view><view class="sensor-item data-v-57280228"><text class="sensor-label data-v-57280228">更新时间</text><text class="sensor-value data-v-57280228">{{$root.m0}}</text></view></view></block><view class="sensor-actions data-v-57280228"><button class="btn btn-sensor data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['querySensorData',['$event']]]]]}}" bindtap="__e">🌡️ 获取传感器数据</button></view></view></view></block><view class="device-list-card data-v-57280228"><view class="card-header data-v-57280228"><text class="card-title data-v-57280228">📱 发现的设备</text><text class="device-count data-v-57280228">{{$root.g1+" 个设备"}}</text></view><scroll-view class="device-scroll data-v-57280228" scroll-y="{{true}}"><block wx:for="{{devices}}" wx:for-item="device" wx:for-index="__i0__" wx:key="deviceId"><view class="device-item data-v-57280228"><view class="device-info data-v-57280228"><text class="device-name data-v-57280228">{{device.name||'未知设备'}}</text><text class="device-id data-v-57280228">{{device.deviceId}}</text></view><button class="btn btn-connect data-v-57280228" disabled="{{connected}}" data-event-opts="{{[['tap',[['connectDevice',['$0'],[[['devices','deviceId',device.deviceId,'deviceId']]]]]]]}}" bindtap="__e">{{''+(connected?'已连接':'连接')+''}}</button></view></block><block wx:if="{{$root.g2===0}}"><view class="empty-state data-v-57280228"><text class="data-v-57280228">🔍 暂无发现设备，请先扫描</text></view></block></scroll-view></view><view class="data-card data-v-57280228"><view class="card-header data-v-57280228"><text class="card-title data-v-57280228">📥 接收到的数据</text><button data-event-opts="{{[['tap',[['clearReceivedData',['$event']]]]]}}" class="btn btn-clear data-v-57280228" bindtap="__e">清空</button></view><scroll-view class="data-scroll data-v-57280228" scroll-y="{{true}}"><block wx:for="{{receivedMessages}}" wx:for-item="msg" wx:for-index="index" wx:key="index"><view class="message-item data-v-57280228"><text class="message-text data-v-57280228">{{msg}}</text></view></block><block wx:if="{{$root.g3===0}}"><view class="empty-state data-v-57280228"><text class="data-v-57280228">📭 暂无接收数据</text></view></block></scroll-view></view><view class="command-card data-v-57280228"><view class="card-header data-v-57280228"><text class="card-title data-v-57280228">📤 发送命令</text></view><view class="command-group data-v-57280228"><text class="group-title data-v-57280228">🔍 查询命令</text><view class="command-buttons data-v-57280228"><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['queryDeviceStatus',['$event']]]]]}}" bindtap="__e">📊 设备状态</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['querySensorData',['$event']]]]]}}" bindtap="__e">🌡️ 传感器数据</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['queryMaintenanceInfo',['$event']]]]]}}" bindtap="__e">🔧 保养信息</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['queryElectricalParameters',['$event']]]]]}}" bindtap="__e">⚡ 电气参数</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['queryProtectionStatus',['$event']]]]]}}" bindtap="__e">🛡️ 保护状态</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['queryAirQuality',['$event']]]]]}}" bindtap="__e">🌬️ 空气质量</button><button class="btn btn-query data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['sendHeartbeat',['$event']]]]]}}" bindtap="__e">💓 心跳包</button></view></view><view class="command-group data-v-57280228"><text class="group-title data-v-57280228">⚡ 快捷控制</text><view class="quick-control-section data-v-57280228"><text class="control-section-title data-v-57280228">🔊 蜂鸣器控制</text><view class="toggle-buttons data-v-57280228"><button class="{{['btn','btn-toggle','data-v-57280228',(currentBuzzerState)?'active':'']}}" disabled="{{!connected}}" data-event-opts="{{[['tap',[['toggleBuzzer',['$event']]]]]}}" bindtap="__e">{{''+(currentBuzzerState?'🔊 开启':'🔇 关闭')+''}}</button></view></view><view class="quick-control-section data-v-57280228"><text class="control-section-title data-v-57280228">⚡ 电压挡位控制</text><view class="gear-buttons data-v-57280228"><button class="{{['btn','btn-gear','data-v-57280228',(currentVoltageGear===0)?'active':'']}}" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setVoltageGear',[0]]]]]}}" bindtap="__e">关闭</button><button class="{{['btn','btn-gear','data-v-57280228',(currentVoltageGear===1)?'active':'']}}" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setVoltageGear',[1]]]]]}}" bindtap="__e">1挡</button><button class="{{['btn','btn-gear','data-v-57280228',(currentVoltageGear===2)?'active':'']}}" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setVoltageGear',[2]]]]]}}" bindtap="__e">2挡</button><button class="{{['btn','btn-gear','data-v-57280228',(currentVoltageGear===3)?'active':'']}}" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setVoltageGear',[3]]]]]}}" bindtap="__e">3挡</button></view></view></view><view class="command-group data-v-57280228"><text class="group-title data-v-57280228">🎛️ 控制命令</text><view class="command-buttons data-v-57280228"><button class="btn btn-control data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['resetDevice',['$event']]]]]}}" bindtap="__e">🔄 复位设备</button><button class="btn btn-control data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['resetTimer',['$event']]]]]}}" bindtap="__e">⏰ 复位计时器</button><button class="btn btn-control data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['resetAnionCounter',['$event']]]]]}}" bindtap="__e">🔋 复位负离子计数器</button></view><view class="param-controls data-v-57280228"><view class="control-item data-v-57280228"><text class="control-label data-v-57280228">⚙️ 设置挡位:</text><view class="control-input data-v-57280228"><input class="input-field data-v-57280228" type="number" placeholder="0-3" data-event-opts="{{[['input',[['__set_model',['','gearValue','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{gearValue}}" bindinput="__e" bindblur="__e"/><button class="btn btn-send data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setGear',['$0'],['gearValue']]]]]}}" bindtap="__e">发送</button></view></view><view class="control-item data-v-57280228"><text class="control-label data-v-57280228">🔊 控制蜂鸣器:</text><view class="control-input data-v-57280228"><input class="input-field data-v-57280228" type="number" placeholder="0/1" data-event-opts="{{[['input',[['__set_model',['','buzzerStatus','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{buzzerStatus}}" bindinput="__e" bindblur="__e"/><button class="btn btn-send data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['controlBuzzer',['$0'],['buzzerStatus']]]]]}}" bindtap="__e">发送</button></view></view><view class="control-item data-v-57280228"><text class="control-label data-v-57280228">🛡️ 复位保护状态:</text><view class="control-input data-v-57280228"><input class="input-field data-v-57280228" type="number" placeholder="0x01-0xFF" data-event-opts="{{[['input',[['__set_model',['','protectionResetType','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{protectionResetType}}" bindinput="__e" bindblur="__e"/><button class="btn btn-send data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['resetProtectionStatus',['$0'],['protectionResetType']]]]]}}" bindtap="__e">发送</button></view></view><view class="control-item data-v-57280228"><text class="control-label data-v-57280228">💨 档位控制:</text><view class="control-input data-v-57280228"><input class="input-field data-v-57280228" type="number" placeholder="0-6" data-event-opts="{{[['input',[['__set_model',['','fanGear','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{fanGear}}" bindinput="__e" bindblur="__e"/><button class="btn btn-send data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['setFanGear',['$0'],['fanGear']]]]]}}" bindtap="__e">发送</button></view></view><view class="control-item data-v-57280228"><text class="control-label data-v-57280228">🎵 蜂鸣器高级控制:</text><view class="control-input-group data-v-57280228"><input class="input-field data-v-57280228" type="number" placeholder="频率(Hz)" data-event-opts="{{[['input',[['__set_model',['','buzzerFrequency','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{buzzerFrequency}}" bindinput="__e" bindblur="__e"/><input class="input-field data-v-57280228" type="number" placeholder="时间(ms)" data-event-opts="{{[['input',[['__set_model',['','buzzerDuration','$event',['number']]]]],['blur',[['$forceUpdate']]]]}}" value="{{buzzerDuration}}" bindinput="__e" bindblur="__e"/><button class="btn btn-send data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['controlBuzzerAdvanced',['$0','$1'],['buzzerFrequency','buzzerDuration']]]]]}}" bindtap="__e">发送</button></view></view></view></view><view class="command-group data-v-57280228"><text class="group-title data-v-57280228">🧪 测试命令</text><view class="command-buttons data-v-57280228"><button class="btn btn-test data-v-57280228" disabled="{{!connected}}" data-event-opts="{{[['tap',[['testRawData',['$event']]]]]}}" bindtap="__e">📡 测试原始数据</button></view></view></view></view>