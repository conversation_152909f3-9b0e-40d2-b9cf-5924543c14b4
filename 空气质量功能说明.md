# 空气质量获取功能说明

## 🌬️ 功能概述

现在您的应用**完全支持**空气质量获取功能，包括主动查询和实时监测。

## ✅ 已实现的功能

### 1. **空气质量查询**
- **按钮位置**: "查询命令"组中的"🌬️ 空气质量"按钮
- **命令代码**: `0x06`
- **响应代码**: `0x86`
- **功能**: 主动查询当前空气质量等级

### 2. **空气质量实时监测**
- **通知代码**: `0xA6`
- **功能**: 设备主动推送空气质量变化
- **提示**: 空气质量变化时会显示Toast通知

### 3. **传感器数据获取**
- **按钮位置**: 空气质量卡片中的"🌡️ 获取传感器数据"按钮
- **命令代码**: `0x02`
- **响应代码**: `0x82`
- **通知代码**: `0xA2`
- **数据包含**:
  - PM2.5 浓度 (μg/m³)
  - VOC 浓度 (ppm)
  - 负离子浓度 (个/cm³)
  - 温度 (°C)
  - 湿度 (%RH)

### 4. **可视化界面**
- **空气质量卡片**: 显示当前空气质量等级和图标
- **传感器数据网格**: 显示详细的传感器数据
- **实时更新**: 数据变化时自动更新界面
- **时间戳**: 显示最后更新时间

## 📊 空气质量等级

| 等级 | 显示 | 颜色 | 图标 | 说明 |
|------|------|------|------|------|
| 1 | 优 | 绿色 | 😊 | 空气质量令人满意 |
| 2 | 良 | 黄色 | 🙂 | 空气质量可以接受 |
| 3 | 轻度污染 | 橙色 | 😐 | 易感人群症状轻度加剧 |
| 4 | 中度污染 | 红色 | 😷 | 易感人群症状进一步加剧 |
| 5 | 重度污染 | 深红色 | 😵 | 心脏病和肺病患者症状显著加剧 |

## 🎯 使用方法

### 方法一：主动查询
1. 确保设备已连接
2. 点击"🌬️ 空气质量"按钮
3. 查看空气质量卡片中的结果

### 方法二：获取详细数据
1. 确保设备已连接
2. 点击"🌡️ 获取传感器数据"按钮
3. 查看传感器数据网格中的详细信息

### 方法三：实时监测
- 设备会自动推送空气质量和传感器数据变化
- 无需手动操作，数据会自动更新
- 变化时会显示Toast通知

## 🔄 数据更新机制

### 主动查询
```javascript
// 查询空气质量
this.queryAirQuality();

// 查询传感器数据
this.querySensorData();
```

### 自动通知
- **空气质量变化**: 设备检测到空气质量等级变化时自动推送
- **传感器数据变化**: 设备定期推送传感器数据更新
- **界面自动更新**: 收到数据后界面立即更新

## 📱 界面功能

### 空气质量卡片
- **主要显示**: 当前空气质量等级、图标、颜色
- **刷新按钮**: 手动刷新空气质量
- **传感器数据网格**: 显示PM2.5、VOC、负离子、温度、湿度
- **更新时间**: 显示最后数据更新时间

### 数据格式
- **PM2.5**: 显示为 "XX μg/m³"
- **VOC**: 显示为 "XX ppm"
- **负离子**: 显示为 "XX 个/cm³"
- **温度**: 显示为 "XX.X°C"
- **湿度**: 显示为 "XX.X%RH"
- **时间**: 显示为 "HH:MM:SS"

## 🛠️ 技术实现

### 数据解析
```javascript
// 空气质量解析
case 0x86: // 查询响应
case 0xA6: // 变化通知
  const airQualityLevel = data[0];
  this.updateAirQualityDisplay(airQualityLevel);

// 传感器数据解析
case 0x82: // 查询响应
case 0xA2: // 变化通知
  const pm25 = this.bytesToUint16(data, 0);
  const voc = this.bytesToUint16(data, 2);
  // ... 其他数据解析
```

### 状态管理
```javascript
// 空气质量状态
currentAirQuality: {
  level: 0,
  text: '未知',
  color: '#999',
  icon: '❓'
}

// 传感器数据状态
sensorData: {
  pm25: 0,
  voc: 0,
  anionConcentration: 0,
  temperature: 0,
  humidity: 0,
  lastUpdate: 0
}
```

## 🎨 视觉效果

- **渐变背景**: 空气质量卡片使用渐变背景
- **颜色编码**: 不同空气质量等级使用不同颜色
- **图标表情**: 使用表情符号直观表示空气质量
- **网格布局**: 传感器数据使用网格布局整齐显示
- **实时动画**: 数据更新时有平滑的过渡效果

## 📈 数据监控

### 实时性
- 空气质量变化立即显示
- 传感器数据定期更新
- 时间戳显示最后更新时间

### 准确性
- 直接从设备获取原始数据
- 按照协议规范解析数据
- 支持多种数据格式和精度

## 🔧 故障排除

### 数据不更新
1. 检查设备连接状态
2. 点击"刷新"按钮手动更新
3. 重新连接设备

### 显示异常
1. 检查设备是否支持该功能
2. 查看控制台日志
3. 重置连接状态

现在您可以完整地获取和监测空气质量数据了！
