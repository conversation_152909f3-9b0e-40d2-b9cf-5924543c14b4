{"version": 3, "sources": ["uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/uni-mp-weixin/dist/wx.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/uni-mp-weixin/dist/index.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/webpack/buildin/global.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/interopRequireDefault.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/slicedToArray.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/arrayWithHoles.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/iterableToArrayLimit.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/arrayLikeToArray.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/nonIterableRest.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/defineProperty.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/toPropertyKey.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/typeof.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/toPrimitive.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/construct.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/setPrototypeOf.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/isNativeReflectConstruct.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/toConsumableArray.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/arrayWithoutHoles.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/iterableToArray.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/nonIterableSpread.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/uni-i18n/dist/uni-i18n.es.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/classCallCheck.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@babel/runtime/helpers/createClass.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/mp-vue/dist/mp.runtime.esm.js", "uni-app:///D:/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js"], "names": ["objectKeys", "singlePageDisableKey", "target", "globalThis", "this", "key", "join", "oldWx", "launchOption", "getLaunchOptionsSync", "isWxKey", "scene", "includes", "indexOf", "initWx", "newWx", "canIUse", "getAppBaseInfo", "getSystemInfoSync", "getWindowInfo", "getDeviceInfo", "realAtob", "b64", "b64re", "b64DecodeUnicode", "str", "decodeURIComponent", "split", "map", "c", "charCodeAt", "toString", "slice", "getCurrentUserInfo", "userInfo", "token", "wx", "getStorageSync", "tokenArr", "length", "uid", "role", "permission", "tokenExpired", "JSON", "parse", "error", "Error", "message", "exp", "iat", "uniIdMixin", "<PERSON><PERSON>", "prototype", "uniIDHasRole", "roleId", "uniIDHasPermission", "permissionId", "uniIDTokenValid", "Date", "now", "atob", "String", "replace", "test", "bitmap", "r1", "r2", "result", "i", "char<PERSON>t", "fromCharCode", "_toString", "Object", "hasOwnProperty", "isFn", "fn", "isStr", "isObject", "obj", "isPlainObject", "call", "hasOwn", "noop", "cached", "cache", "create", "hit", "camelizeRE", "camelize", "_", "toUpperCase", "sortObject", "sortObj", "keys", "sort", "for<PERSON>ach", "HOOKS", "globalInterceptors", "scopedInterceptors", "mergeHook", "parentVal", "childVal", "res", "concat", "Array", "isArray", "dedupe<PERSON><PERSON>s", "hooks", "push", "removeH<PERSON>", "hook", "index", "splice", "mergeInterceptorHook", "interceptor", "option", "removeInterceptorHook", "addInterceptor", "method", "removeInterceptor", "wrapperHook", "params", "data", "isPromise", "then", "queue", "promise", "Promise", "resolve", "callback", "wrapperOptions", "options", "name", "oldCallback", "wrapperReturnValue", "returnValue", "returnValueHooks", "getApiInterceptorHooks", "scopedInterceptor", "invokeApi", "api", "invoke", "promiseInterceptor", "reject", "SYNC_API_RE", "CONTEXT_API_RE", "CONTEXT_API_RE_EXC", "ASYNC_API", "CALLBACK_API_RE", "isContextApi", "isSyncApi", "isCallbackApi", "handlePromise", "catch", "err", "shouldPromise", "promisify", "success", "fail", "complete", "assign", "finally", "constructor", "value", "reason", "EPS", "BASE_DEVICE_WIDTH", "isIOS", "deviceWidth", "deviceDPR", "checkDeviceWidth", "windowWidth", "pixelRatio", "platform", "windowInfo", "deviceInfo", "upx2px", "number", "newDeviceWidth", "Number", "Math", "floor", "locale", "LOCALE_ZH_HANS", "LOCALE_ZH_HANT", "LOCALE_EN", "LOCALE_FR", "LOCALE_ES", "messages", "getLocaleLanguage", "localeLanguage", "appBaseInfo", "language", "normalizeLocale", "initI18nMessages", "isEnableLocale", "localeKeys", "__uniConfig", "locales", "curMessages", "userMessages", "i18n", "initVueI18n", "t", "mixin", "beforeCreate", "unwatch", "watchLocale", "$forceUpdate", "$once", "methods", "$$t", "values", "setLocale", "getLocale", "initAppLocale", "appVm", "state", "observable", "localeWatchers", "$watchLocale", "defineProperty", "get", "set", "v", "watch", "include", "parts", "find", "part", "startsWith", "trim", "toLowerCase", "lang", "getLocale$1", "getApp", "app", "allowDefault", "$vm", "$locale", "setLocale$1", "oldLocale", "onLocaleChangeCallbacks", "onLocaleChange", "global", "interceptors", "baseApi", "freeze", "__proto__", "rpx2px", "findExistsPageIndex", "url", "pages", "getCurrentPages", "len", "page", "$page", "fullPath", "deviceId", "redirectTo", "fromArgs", "exists", "delta", "args", "existsPageIndex", "previewImage", "currentIndex", "parseInt", "current", "isNaN", "urls", "filter", "item", "indicator", "loop", "UUID_KEY", "useDeviceId", "random", "setStorage", "addSafeAreaInsets", "safeArea", "safeAreaInsets", "top", "left", "right", "bottom", "screenHeight", "getOSInfo", "system", "osName", "osVersion", "toLocaleLowerCase", "populateParameters", "brand", "model", "theme", "version", "fontSizeSetting", "SDKVersion", "deviceOrientation", "extraParam", "hostVersion", "deviceType", "getGetDeviceType", "deviceBrand", "getDevice<PERSON>rand", "_hostName", "getHostName", "_deviceOrientation", "_devicePixelRatio", "_SDKVersion", "hostLanguage", "parameters", "appId", "process", "appName", "appVersion", "appVersionCode", "appLanguage", "getAppLanguage", "uniCompileVersion", "uniCompilerVersion", "uniRuntimeVersion", "uniPlatform", "deviceModel", "devicePixelRatio", "hostTheme", "hostName", "hostSDKVersion", "hostFontSizeSetting", "windowTop", "windowBottom", "osLanguage", "undefined", "osTheme", "ua", "hostPackageName", "browserName", "browserVersion", "isUniAppX", "deviceTypeMaps", "ipad", "windows", "mac", "deviceTypeMapsKeys", "_model", "_m", "defaultLanguage", "_platform", "environment", "host", "env", "getSystemInfo", "showActionSheet", "alertText", "title", "getAppAuthorizeSetting", "locationReducedAccuracy", "locationAccuracy", "compressImage", "compressedHeight", "compressHeight", "compressedWidth", "compressWidth", "protocols", "todos", "canIUses", "CALLBACKS", "processCallback", "methodName", "processReturnValue", "processArgs", "argsOption", "keepFromArgs", "<PERSON><PERSON><PERSON><PERSON>", "keyOption", "console", "warn", "keepReturnValue", "wrapper", "protocol", "arg1", "arg2", "apply", "todo<PERSON><PERSON>", "TODOS", "createTodoApi", "errMsg", "providers", "o<PERSON>h", "share", "payment", "get<PERSON><PERSON><PERSON>", "service", "provider", "extraApi", "getEmitter", "Emitter", "ctx", "$on", "arguments", "$off", "$emit", "cid", "cidErrMsg", "enabled", "eventApi", "tryCatch", "e", "getApiCallbacks", "apiCallbacks", "param", "normalizePushMessage", "invoke<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "invokeGetPushCidCallbacks", "onPushMessageCallbacks", "stopped", "getPushCidCallbacks", "getPushClientId", "hasSuccess", "hasFail", "hasComplete", "onPushMessage", "offPushMessage", "__f__", "baseInfo", "shareVideoMessage", "miniapp", "mocks", "findVmByVueId", "vm", "vuePid", "parentVm", "$children", "childVm", "$scope", "_$vueId", "init<PERSON>eh<PERSON>or", "Behavior", "isPage", "route", "initRelation", "detail", "triggerEvent", "selectAllComponents", "mpInstance", "selector", "$refs", "components", "component", "ref", "dataset", "toSkip", "vueGeneric", "scopedComponent", "syncRefs", "refs", "newRefs", "oldKeys", "Set", "newKeys", "oldValue", "newValue", "every", "delete", "initRefs", "forComponents", "handleLink", "event", "vueOptions", "parent", "markMPComponent", "IS_MP", "configurable", "enumerable", "OB", "SKIP", "isExtensible", "WORKLET_RE", "initWorkletMethods", "mpMethods", "vueMethods", "matches", "match", "workletName", "MPPage", "Page", "MPComponent", "Component", "customizeRE", "customize", "initTriggerEvent", "oldTriggerEvent", "newTriggerEvent", "comType", "newEvent", "_triggerEvent", "initHook", "isComponent", "oldHook", "__$wrappered", "after", "PAGE_EVENT_HOOKS", "initMocks", "$mp", "mpType", "mock", "hasH<PERSON>", "default", "extendOptions", "super", "mixins", "initHooks", "mpOptions", "__call_hook", "initUnknownHooks", "excludes", "find<PERSON>ooks", "initHook$1", "initVueComponent", "VueComponent", "extend", "initSlots", "vueSlots", "$slots", "slotName", "$scopedSlots", "initVueIds", "vueIds", "_$vuePid", "initData", "context", "VUE_APP_DEBUG", "stringify", "__lifecycle_hooks__", "PROP_TYPES", "Boolean", "createObserver", "newVal", "oldVal", "initBehaviors", "vueBehaviors", "behaviors", "vueExtends", "extends", "vueMixins", "vueProps", "props", "behavior", "properties", "initProperties", "vueMixin", "parsePropType", "defaultValue", "file", "is<PERSON>eh<PERSON>or", "vueId", "virtualHost", "virtualHostStyle", "virtualHostClass", "scopedSlotsCompiler", "observer", "setData", "opts", "wrapper$1", "mp", "stopPropagation", "preventDefault", "markerId", "getExtraValue", "dataPathsArray", "dataPathArray", "dataPath", "vFor", "prop<PERSON>ath", "valuePath", "isInteger", "substr", "__get_value", "vForItem", "vForKey", "processEventExtra", "extra", "__args__", "extraObj", "getObjByArray", "arr", "element", "processEventArgs", "isCustom", "isCustomMPEvent", "currentTarget", "ret", "arg", "ONCE", "CUSTOM", "isMatchEventType", "eventType", "optType", "getContextVm", "$parent", "$options", "generic", "handleEvent", "eventOpts", "eventOpt", "eventsArray", "isOnce", "eventArray", "handlerCtx", "handler", "path", "is", "once", "eventChannels", "getEventChannel", "id", "eventChannel", "initEventChannel", "getOpenerEventChannel", "callHook", "__id__", "__eventChannel__", "initScopedSlotsParams", "center", "parents", "currentId", "propsData", "$hasSSP", "slot", "$getSSP", "needAll", "$setSSP", "$initSSP", "$callSSP", "destroyed", "parseBaseApp", "store", "$store", "mpHost", "$i18n", "_i18n", "appOptions", "onLaunch", "globalData", "_isMounted", "getLocaleLanguage$1", "parseApp", "createApp", "App", "encodeReserveRE", "encodeReserveReplacer", "commaRE", "encode", "encodeURIComponent", "stringifyQuery", "encodeStr", "val", "val2", "x", "parseBaseComponent", "vueComponentOptions", "needVueOptions", "multipleSlots", "addGlobalClass", "componentOptions", "__file", "lifetimes", "attached", "$mount", "ready", "detached", "$destroy", "pageLifetimes", "show", "hide", "resize", "size", "__l", "__e", "externalClasses", "wxsCallMethods", "callMethod", "parseComponent", "hooks$1", "parseBasePage", "vuePageOptions", "pageOptions", "onLoad", "query", "copyQuery", "parsePage", "createPage", "createComponent", "createSubpackageApp", "onShow", "onAppShow", "onHide", "onAppHide", "createPlugin", "todoApi", "canIUseApi", "apiName", "uni", "Proxy", "uni$1", "g", "Function", "window", "module", "exports", "_interopRequireDefault", "__esModule", "arrayWithHoles", "require", "iterableToArrayLimit", "unsupportedIterableToArray", "nonIterableRest", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "r", "l", "Symbol", "iterator", "n", "u", "a", "f", "o", "next", "done", "arrayLikeToArray", "_unsupportedIterableToArray", "minLen", "from", "_arrayLikeToArray", "arr2", "_nonIterableRest", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_defineProperty", "writable", "_typeof", "toPrimitive", "setPrototypeOf", "isNativeReflectConstruct", "_construct", "Reflect", "construct", "p", "bind", "_setPrototypeOf", "_isNativeReflectConstruct", "valueOf", "arrayWithoutHoles", "iterableToArray", "nonIterableSpread", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "iter", "_nonIterableSpread", "defaultDelimiters", "BaseFormatter", "_caches", "delimiters", "tokens", "compile", "RE_TOKEN_LIST_VALUE", "RE_TOKEN_NAMED_VALUE", "format", "startDelimiter", "endDelimiter", "position", "text", "char", "sub", "isClosed", "compiled", "mode", "defaultFormatter", "I18n", "fallback<PERSON><PERSON><PERSON>", "watcher", "formater", "watchers", "override", "interpolate", "watchAppLocale", "newLocale", "$watch", "getDefaultLocale", "isWatchedAppLocale", "add", "isString", "hasI18nJson", "jsonObj", "walkJsonObj", "isI18nStr", "parseI18nJson", "compileStr", "compileI18nJsonStr", "jsonStr", "localeValues", "unshift", "compileJsonObj", "compileValue", "valueLocales", "localValue", "walk", "resolveLocale", "resolveLocaleChain", "chain", "pop", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "descriptor", "_createClass", "protoProps", "staticProps", "emptyObject", "isUndef", "isDef", "isTrue", "isFalse", "isPrimitive", "toRawType", "isRegExp", "isValidArrayIndex", "parseFloat", "isFinite", "toNumber", "makeMap", "expectsLowerCase", "list", "isBuiltInTag", "isReservedAttribute", "remove", "capitalize", "hyphenateRE", "hyphenate", "polyfillBind", "boundFn", "_length", "nativeBind", "toArray", "start", "to", "_from", "toObject", "b", "no", "identity", "looseEqual", "isObjectA", "isObjectB", "isArrayA", "isArrayB", "getTime", "keysA", "keysB", "looseIndexOf", "called", "ASSET_TYPES", "LIFECYCLE_HOOKS", "config", "optionMergeStrategies", "silent", "productionTip", "devtools", "performance", "<PERSON><PERSON><PERSON><PERSON>", "warn<PERSON><PERSON>ler", "ignoredElements", "keyCodes", "isReservedTag", "isReservedAttr", "isUnknownElement", "getTagNamespace", "parsePlatformTagName", "mustUseProp", "async", "_lifecycleHooks", "unicodeRegExp", "isReserved", "def", "bailRE", "RegExp", "parsePath", "segments", "_isServer", "hasProto", "inBrowser", "inWeex", "WXEnvironment", "weexPlatform", "UA", "navigator", "userAgent", "isIE", "isEdge", "nativeWatch", "addEventListener", "isServerRendering", "VUE_ENV", "__VUE_DEVTOOLS_GLOBAL_HOOK__", "isNative", "Ctor", "_Set", "hasSymbol", "ownKeys", "has", "clear", "tip", "generateComponentTrace", "formatComponentName", "hasConsole", "classifyRE", "classify", "msg", "trace", "includeFile", "$root", "_isVue", "_componentTag", "repeat", "tree", "currentRecursiveSequence", "last", "Dep", "subs", "pushTarget", "SharedObject", "targetStack", "pop<PERSON>arget", "addSub", "removeSub", "depend", "addDep", "notify", "update", "VNode", "tag", "children", "elm", "asyncFactory", "ns", "fnContext", "fnOptions", "fnScopeId", "componentInstance", "raw", "isStatic", "isRootInsert", "isComment", "isCloned", "asyncMeta", "isAsyncPlaceholder", "prototypeAccessors", "child", "defineProperties", "createEmptyVNode", "node", "createTextVNode", "cloneVNode", "vnode", "cloned", "arrayProto", "arrayMethods", "methodsToPatch", "original", "inserted", "ob", "__ob__", "observeArray", "dep", "arrayKeys", "getOwnPropertyNames", "shouldObserve", "toggleObserving", "Observer", "vmCount", "copyAugment", "protoAugment", "src", "observe", "asRootData", "__v_isMPComponent", "defineReactive$$1", "customSetter", "shallow", "property", "getOwnPropertyDescriptor", "getter", "setter", "childOb", "dependArray", "max", "del", "items", "strats", "mergeData", "toVal", "fromVal", "mergeDataOrFn", "instanceData", "defaultData", "mergeAssets", "assertObjectType", "el", "defaultStrat", "key$1", "inject", "computed", "provide", "checkComponents", "validateComponentName", "normalizeProps", "normalizeInject", "normalized", "normalizeDirectives", "dirs", "directives", "def$$1", "mergeOptions", "_base", "mergeField", "strat", "resolveAsset", "warnMissing", "assets", "camelizedId", "PascalCaseId", "validateProp", "propOptions", "prop", "absent", "booleanIndex", "getTypeIndex", "stringIndex", "getPropDefaultValue", "prevShouldObserve", "assertProp", "_props", "getType", "required", "valid", "expectedTypes", "assertedType", "assertType", "expectedType", "validator", "getInvalidTypeMessage", "simpleCheckRE", "isSameType", "receivedType", "expectedValue", "styleValue", "receivedValue", "isExplicable", "isBoolean", "explicitTypes", "some", "elem", "handleError", "info", "cur", "errorCaptured", "capture", "globalHandleError", "invokeWithErrorHandling", "_handled", "logError", "timer<PERSON>un<PERSON>", "initProxy", "callbacks", "pending", "flushCallbacks", "copies", "setTimeout", "MutationObserver", "setImmediate", "counter", "textNode", "document", "createTextNode", "characterData", "nextTick", "cb", "_resolve", "allowedGlobals", "warnNonPresent", "warnReservedPrefix", "hasProxy", "isBuiltInModifier", "<PERSON><PERSON><PERSON><PERSON>", "isAllowed", "$data", "<PERSON><PERSON><PERSON><PERSON>", "handlers", "render", "_withStripped", "_renderProxy", "mark", "measure", "seenObjects", "traverse", "_traverse", "seen", "isA", "isFrozen", "depId", "perf", "clearMarks", "clearMeasures", "startTag", "endTag", "normalizeEvent", "passive", "once$$1", "createFnInvoker", "fns", "invoker", "arguments$1", "updateListeners", "on", "oldOn", "remove$$1", "createOnceHandler", "old", "extractPropertiesFromVNodeData", "attrs", "altKey", "checkProp", "extractPropsFromVNodeData", "keyInLowerCase", "hash", "preserve", "simpleNormalizeChildren", "normalizeChildren", "normalizeArrayChildren", "isTextNode", "nestedIndex", "lastIndex", "shift", "_isVList", "initProvide", "_provided", "initInjections", "resolveInject", "<PERSON><PERSON><PERSON>", "source", "provideDefault", "resolveSlots", "slots", "name$1", "isWhitespace", "normalizeScopedSlots", "normalSlots", "prevSlots", "hasNormalSlots", "isStable", "$stable", "$key", "_normalized", "$hasNormal", "normalizeScopedSlot", "key$2", "proxyNormalSlot", "proxy", "renderList", "renderSlot", "fallback", "bindObject", "nodes", "scopedSlotFn", "_i", "$createElement", "resolveFilter", "isKeyNotMatch", "expect", "actual", "checkKeyCodes", "eventKeyCode", "builtInKeyCode", "eventKeyName", "builtInKeyName", "mappedKeyCode", "bindObjectProps", "asProp", "isSync", "domProps", "cameli<PERSON><PERSON><PERSON>", "hyphenatedKey", "$event", "renderStatic", "isInFor", "_staticTrees", "staticRenderFns", "markStatic", "markOnce", "markStaticNode", "bindObjectListeners", "existing", "ours", "resolveScopedSlots", "hasDynamicKeys", "contentHashKey", "bindDynamicKeys", "baseObj", "prependModifier", "symbol", "installRenderHelpers", "_o", "_n", "_s", "_l", "_t", "_q", "_f", "_k", "_b", "_v", "_e", "_u", "_g", "_d", "_p", "FunctionalRenderContext", "contextVm", "this$1", "_original", "isCompiled", "_compiled", "needNormalization", "listeners", "injections", "scopedSlots", "_scopeId", "_c", "d", "createElement", "createFunctionalComponent", "mergeProps", "renderContext", "cloneAndMarkFunctionalResult", "vnodes", "clone", "devtoolsMeta", "componentVNodeHooks", "init", "hydrating", "_isDestroyed", "keepAlive", "mountedNode", "prepatch", "createComponentInstanceForVnode", "activeInstance", "oldVnode", "updateChildComponent", "insert", "queueActivatedComponent", "activateChildComponent", "destroy", "deactivateChildComponent", "hooksToMerge", "baseCtor", "resolveAsyncComponent", "createAsyncPlaceholder", "resolveConstructorOptions", "transformModel", "functional", "nativeOn", "abstract", "installComponentHooks", "_isComponent", "_parentVnode", "inlineTemplate", "toMerge", "_merged", "mergeHook$1", "f1", "f2", "merged", "SIMPLE_NORMALIZE", "ALWAYS_NORMALIZE", "normalizationType", "alwaysNormalize", "_createElement", "$vnode", "pre", "applyNS", "registerDeepBindings", "force", "style", "class", "init<PERSON><PERSON>", "_vnode", "parentVnode", "_<PERSON><PERSON><PERSON><PERSON>n", "parentData", "isUpdatingChildComponent", "_parentListeners", "currentRenderingInstance", "renderMixin", "$nextTick", "_render", "renderError", "ensureCtor", "comp", "base", "toStringTag", "factory", "errorComp", "resolved", "owner", "owners", "loading", "loadingComp", "sync", "timerLoading", "timerTimeout", "forceRender", "renderCompleted", "clearTimeout", "delay", "timeout", "getFirstComponentChild", "initEvents", "_events", "_hasHookEvent", "updateComponentListeners", "remove$1", "_target", "once<PERSON><PERSON><PERSON>", "oldListeners", "eventsMixin", "hookRE", "i$1", "cbs", "lowerCaseEvent", "setActiveInstance", "prevActiveInstance", "initLifecycle", "_watcher", "_inactive", "_directInactive", "_isBeingDestroyed", "lifecycleMixin", "_update", "prevEl", "$el", "prevVnode", "restoreActiveInstance", "__patch__", "__vue__", "teardown", "_watchers", "_data", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newScopedSlots", "oldScopedSlots", "hasDynamicScopedSlot", "needsForceUpdate", "$attrs", "$listeners", "propKeys", "_propKeys", "_$updateProperties", "isInInactiveTree", "direct", "j", "MAX_UPDATE_COUNT", "activatedChildren", "circular", "waiting", "flushing", "resetSchedulerState", "getNow", "createEvent", "timeStamp", "flushSchedulerQueue", "before", "run", "user", "activatedQueue", "updatedQueue", "callActivatedHooks", "callUpdatedHooks", "emit", "queueWatcher", "uid$2", "Watcher", "expOrFn", "isRenderWatcher", "deep", "lazy", "active", "dirty", "deps", "newDeps", "depIds", "newDepIds", "expression", "cleanupDeps", "tmp", "evaluate", "sharedPropertyDefinition", "sourceKey", "initState", "initProps", "initMethods", "initComputed", "initWatch", "propsOptions", "isRoot", "_getFormData", "__next_tick_pending", "getData", "computedWatcherOptions", "_computedWatchers", "isSSR", "userDef", "defineComputed", "shouldCache", "createComputedGetter", "createGetterInvoker", "createWatcher", "stateMixin", "dataDef", "propsDef", "$set", "$delete", "immediate", "uid$3", "initMixin", "_init", "_uid", "initInternalComponent", "_self", "_$fallback", "_name", "vnodeComponentOptions", "superOptions", "cachedSuperOptions", "modifiedOptions", "resolveModifiedOptions", "modified", "latest", "sealed", "sealedOptions", "initUse", "use", "plugin", "installedPlugins", "_installedPlugins", "install", "initMixin$1", "initExtend", "Super", "SuperId", "cachedCtors", "_Ctor", "Sub", "initProps$1", "initComputed$1", "Comp", "initAssetRegisters", "definition", "getComponentName", "pattern", "prun<PERSON><PERSON><PERSON>", "keepAliveInstance", "cachedNode", "pruneCacheEntry", "cached$$1", "patternTypes", "KeepAlive", "exclude", "created", "mounted", "ref$1", "builtInComponents", "initGlobalAPI", "configDef", "util", "defineReactive", "ssrContext", "ARRAYTYPE", "OBJECTTYPE", "NULLTYPE", "UNDEFINEDTYPE", "diff", "syncKeys", "_diff", "rootCurrentType", "rootPreType", "currentValue", "nullOrUndefined", "currentType", "preType", "setResult", "preValue", "subKey", "k", "flushCallbacks$1", "__next_tick_callbacks", "log", "hasRenderWatcher", "nextTick$1", "mpInstance$1", "clearInstance", "cloneWithData", "dataKeys", "reduce", "compositionApiState", "__composition_api_state__", "__secret_vfa_state__", "rawBindings", "patch", "__webviewId__", "mpData", "diffData", "$shouldDiffData", "createEmptyRender", "mountComponent$1", "template", "updateComponent", "renderClass", "staticClass", "dynamicClass", "stringifyClass", "stringifyArray", "stringifyObject", "stringified", "parseStyleText", "cssText", "listDelimiter", "propertyDelimiter", "normalizeStyleBinding", "bindingStyle", "MP_METHODS", "get<PERSON><PERSON><PERSON>", "internalMixin", "onError", "oldEmit", "my", "createSelectorQuery", "createIntersectionObserver", "__init_provide", "__init_injections", "__set_model", "modifiers", "__set_sync", "__get_orig", "__get_class", "__get_style", "dynamicStyle", "staticStyle", "dynamicStyleObj", "styleObj", "__map", "iteratee", "LIFECYCLE_HOOKS$1", "lifecycleMixin$1", "oldExtend", "strategies", "normalizeComponent", "scriptExports", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "renderjs", "__module", "__VUE_SSR_CONTEXT__", "_registeredComponents", "_ssrRegister", "shadowRoot", "_injectStyles", "originalRender", "h"], "mappings": "6KAAA,IAAMA,EAAa,CACjB,KACA,MACA,QACA,UACA,WACA,QACA,gBACA,SACA,UACA,qCAEIC,EAAuB,CAC3B,WACA,SACA,WAEIC,EAA+B,qBAAfC,WAA6BA,WAAc,WAC/D,OAAOC,KADwD,GAI3DC,EAAM,CAAC,IAAK,KAAKC,KAAK,IACtBC,EAAQL,EAAOG,GACfG,EAAeD,EAAME,qBAAuBF,EAAME,uBAAyB,KAEjF,SAASC,EAASL,GAChB,QAAIG,GAAuC,OAAvBA,EAAaG,QAAkBV,EAAqBW,SAASP,MAG1EL,EAAWa,QAAQR,IAAQ,GAA2B,oBAAfE,EAAMF,IAGtD,SAASS,IACP,IAAMC,EAAQ,GACd,IAAK,IAAMV,KAAOE,EACZG,EAAQL,KAEVU,EAAMV,GAAOE,EAAMF,IAGvB,OAAOU,EAETb,EAAOG,GAAOS,IACTZ,EAAOG,GAAKW,QAAQ,oBACvBd,EAAOG,GAAKY,eAAiBf,EAAOG,GAAKa,mBAGtChB,EAAOG,GAAKW,QAAQ,mBACvBd,EAAOG,GAAKc,cAAgBjB,EAAOG,GAAKa,mBAGrChB,EAAOG,GAAKW,QAAQ,mBACvBd,EAAOG,GAAKe,cAAgBlB,EAAOG,GAAKa,mBACzC,MACchB,EAAOG,GAAI,a,4NCnDtBgB,E,sDAHJ,QACA,WAAsB,2kBAItB,IAAMC,EAAM,oEACNC,EAAQ,uEAyBd,SAASC,EAAkBC,GACzB,OAAOC,mBAAmBL,EAASI,GAAKE,MAAM,IAAIC,KAAI,SAAUC,GAC9D,MAAO,KAAO,KAAOA,EAAEC,WAAW,GAAGC,SAAS,KAAKC,OAAO,MACzD1B,KAAK,KAGV,SAAS2B,IACP,IAUIC,EAVEC,EAAUC,EAAIC,eAAe,iBAAmB,GAChDC,EAAWH,EAAMR,MAAM,KAC7B,IAAKQ,GAA6B,IAApBG,EAASC,OACrB,MAAO,CACLC,IAAK,KACLC,KAAM,GACNC,WAAY,GACZC,aAAc,GAIlB,IACET,EAAWU,KAAKC,MAAMrB,EAAiBc,EAAS,KAChD,MAAOQ,GACP,MAAM,IAAIC,MAAM,sBAAwBD,EAAME,SAKhD,OAHAd,EAASS,aAA8B,IAAfT,EAASe,WAC1Bf,EAASe,WACTf,EAASgB,IACThB,EAGT,SAASiB,EAAYC,GACnBA,EAAIC,UAAUC,aAAe,SAAUC,GACrC,MAEItB,IADFQ,EAAI,EAAJA,KAEF,OAAOA,EAAK5B,QAAQ0C,IAAW,GAEjCH,EAAIC,UAAUG,mBAAqB,SAAUC,GAC3C,MAEIxB,IADFS,EAAU,EAAVA,WAEF,OAAOtC,KAAKkD,aAAa,UAAYZ,EAAW7B,QAAQ4C,IAAiB,GAE3EL,EAAIC,UAAUK,gBAAkB,WAC9B,MAEIzB,IADFU,EAAY,EAAZA,aAEF,OAAOA,EAAegB,KAAKC,OApE7BvC,EADkB,oBAATwC,KACE,SAAUpC,GAEnB,GADAA,EAAMqC,OAAOrC,GAAKsC,QAAQ,gBAAiB,KACtCxC,EAAMyC,KAAKvC,GAAQ,MAAM,IAAIsB,MAAM,4FAIxC,IAAIkB,EADJxC,GAAO,KAAKO,MAAM,GAAkB,EAAbP,EAAIc,SAE3B,IADY,IAAqB2B,EAAQC,EAAzBC,EAAS,GAAwBC,EAAI,EAC9CA,EAAI5C,EAAIc,QACb0B,EAAS3C,EAAIT,QAAQY,EAAI6C,OAAOD,OAAS,GAAK/C,EAAIT,QAAQY,EAAI6C,OAAOD,OAAS,IAC/DH,EAAK5C,EAAIT,QAAQY,EAAI6C,OAAOD,QAAU,GAAKF,EAAK7C,EAAIT,QAAQY,EAAI6C,OAAOD,OAEtFD,GAAiB,KAAPF,EAAYJ,OAAOS,aAAaN,GAAU,GAAK,KAC9C,KAAPE,EAAYL,OAAOS,aAAaN,GAAU,GAAK,IAAKA,GAAU,EAAI,KAChEH,OAAOS,aAAaN,GAAU,GAAK,IAAKA,GAAU,EAAI,IAAc,IAATA,GAEnE,OAAOG,GAIEP,KAqDb,IAAMW,EAAYC,OAAOpB,UAAUtB,SAC7B2C,EAAiBD,OAAOpB,UAAUqB,eAExC,SAASC,EAAMC,GACb,MAAqB,oBAAPA,EAGhB,SAASC,EAAOpD,GACd,MAAsB,kBAARA,EAGhB,SAASqD,EAAUC,GACjB,OAAe,OAARA,GAA+B,YAAf,aAAOA,GAGhC,SAASC,EAAeD,GACtB,MAA+B,oBAAxBP,EAAUS,KAAKF,GAGxB,SAASG,EAAQH,EAAK1E,GACpB,OAAOqE,EAAeO,KAAKF,EAAK1E,GAGlC,SAAS8E,KAKT,SAASC,EAAQR,GACf,IAAMS,EAAQZ,OAAOa,OAAO,MAC5B,OAAO,SAAmB7D,GACxB,IAAM8D,EAAMF,EAAM5D,GAClB,OAAO8D,IAAQF,EAAM5D,GAAOmD,EAAGnD,KAOnC,IAAM+D,EAAa,SACbC,EAAWL,GAAO,SAAC3D,GACvB,OAAOA,EAAIsC,QAAQyB,GAAY,SAACE,EAAG7D,GAAC,OAAKA,EAAIA,EAAE8D,cAAgB,SAGjE,SAASC,EAAYb,GACnB,IAAMc,EAAU,GAMhB,OALIb,EAAcD,IAChBN,OAAOqB,KAAKf,GAAKgB,OAAOC,SAAQ,SAAA3F,GAC9BwF,EAAQxF,GAAO0E,EAAI1E,MAGfoE,OAAOqB,KAAKD,GAAiBA,EAANd,EAGjC,IAAMkB,EAAQ,CACZ,SACA,UACA,OACA,WACA,eAGIC,EAAqB,GACrBC,EAAqB,GAE3B,SAASC,EAAWC,EAAWC,GAC7B,IAAMC,EAAMD,EACRD,EACEA,EAAUG,OAAOF,GACjBG,MAAMC,QAAQJ,GACZA,EAAW,CAACA,GAChBD,EACJ,OAAOE,EACHI,EAAYJ,GACZA,EAGN,SAASI,EAAaC,GAEpB,IADA,IAAML,EAAM,GACHlC,EAAI,EAAGA,EAAIuC,EAAMrE,OAAQ8B,KACD,IAA3BkC,EAAI1F,QAAQ+F,EAAMvC,KACpBkC,EAAIM,KAAKD,EAAMvC,IAGnB,OAAOkC,EAGT,SAASO,EAAYF,EAAOG,GAC1B,IAAMC,EAAQJ,EAAM/F,QAAQkG,IACb,IAAXC,GACFJ,EAAMK,OAAOD,EAAO,GAIxB,SAASE,EAAsBC,EAAaC,GAC1C3C,OAAOqB,KAAKsB,GAAQpB,SAAQ,SAAAe,IACG,IAAzBd,EAAMpF,QAAQkG,IAAgBpC,EAAKyC,EAAOL,MAC5CI,EAAYJ,GAAQX,EAAUe,EAAYJ,GAAOK,EAAOL,QAK9D,SAASM,EAAuBF,EAAaC,GACtCD,GAAgBC,GAGrB3C,OAAOqB,KAAKsB,GAAQpB,SAAQ,SAAAe,IACG,IAAzBd,EAAMpF,QAAQkG,IAAgBpC,EAAKyC,EAAOL,KAC5CD,EAAWK,EAAYJ,GAAOK,EAAOL,OAK3C,SAASO,EAAgBC,EAAQH,GACT,kBAAXG,GAAuBvC,EAAcoC,GAC9CF,EAAqBf,EAAmBoB,KAAYpB,EAAmBoB,GAAU,IAAKH,GAC7EpC,EAAcuC,IACvBL,EAAqBhB,EAAoBqB,GAI7C,SAASC,EAAmBD,EAAQH,GACZ,kBAAXG,EACLvC,EAAcoC,GAChBC,EAAsBlB,EAAmBoB,GAASH,UAE3CjB,EAAmBoB,GAEnBvC,EAAcuC,IACvBF,EAAsBnB,EAAoBqB,GAI9C,SAASE,EAAaV,EAAMW,GAC1B,OAAO,SAAUC,GACf,OAAOZ,EAAKY,EAAMD,IAAWC,GAIjC,SAASC,EAAW7C,GAClB,QAASA,IAAuB,YAAf,aAAOA,IAAmC,oBAARA,IAA2C,oBAAbA,EAAI8C,KAGvF,SAASC,EAAOlB,EAAOe,EAAMD,GAE3B,IADA,IAAIK,GAAU,EACL1D,EAAI,EAAGA,EAAIuC,EAAMrE,OAAQ8B,IAAK,CACrC,IAAM0C,EAAOH,EAAMvC,GACnB,GAAI0D,EACFA,EAAUC,QAAQC,QAAQR,EAAYV,EAAMW,QACvC,CACL,IAAMnB,EAAMQ,EAAKY,EAAMD,GAIvB,GAHIE,EAAUrB,KACZwB,EAAUC,QAAQC,QAAQ1B,KAEhB,IAARA,EACF,MAAO,CACLsB,KAAI,eAKZ,OAAOE,GAAW,CAChBF,KAAI,SAAEK,GACJ,OAAOA,EAASP,KAKtB,SAASQ,EAAgBhB,GAA2B,IAAdiB,EAAU,UAAH,6CAAG,GAY9C,MAXA,CAAC,UAAW,OAAQ,YAAYpC,SAAQ,SAAAqC,GACtC,GAAI5B,MAAMC,QAAQS,EAAYkB,IAAQ,CACpC,IAAMC,EAAcF,EAAQC,GAC5BD,EAAQC,GAAQ,SAA8B9B,GAC5CuB,EAAMX,EAAYkB,GAAO9B,EAAK6B,GAASP,MAAK,SAACtB,GAE3C,OAAO5B,EAAK2D,IAAgBA,EAAY/B,IAAQA,UAKjD6B,EAGT,SAASG,EAAoBhB,EAAQiB,GACnC,IAAMC,EAAmB,GACrBhC,MAAMC,QAAQR,EAAmBsC,cACnCC,EAAiB5B,KAAI,MAArB4B,GAAgB,aAASvC,EAAmBsC,cAE9C,IAAMrB,EAAchB,EAAmBoB,GAOvC,OANIJ,GAAeV,MAAMC,QAAQS,EAAYqB,cAC3CC,EAAiB5B,KAAI,MAArB4B,GAAgB,aAAStB,EAAYqB,cAEvCC,EAAiBzC,SAAQ,SAAAe,GACvByB,EAAczB,EAAKyB,IAAgBA,KAE9BA,EAGT,SAASE,EAAwBnB,GAC/B,IAAMJ,EAAc1C,OAAOa,OAAO,MAClCb,OAAOqB,KAAKI,GAAoBF,SAAQ,SAAAe,GACzB,gBAATA,IACFI,EAAYJ,GAAQb,EAAmBa,GAAM/E,YAGjD,IAAM2G,EAAoBxC,EAAmBoB,GAQ7C,OAPIoB,GACFlE,OAAOqB,KAAK6C,GAAmB3C,SAAQ,SAAAe,GACxB,gBAATA,IACFI,EAAYJ,IAASI,EAAYJ,IAAS,IAAIP,OAAOmC,EAAkB5B,QAItEI,EAGT,SAASyB,EAAWrB,EAAQsB,EAAKT,GAAoB,2BAARV,EAAM,iCAANA,EAAM,kBACjD,IAAMP,EAAcuB,EAAuBnB,GAC3C,GAAIJ,GAAe1C,OAAOqB,KAAKqB,GAAa5E,OAAQ,CAClD,GAAIkE,MAAMC,QAAQS,EAAY2B,QAAS,CACrC,IAAMvC,EAAMuB,EAAMX,EAAY2B,OAAQV,GACtC,OAAO7B,EAAIsB,MAAK,SAACO,GAEf,OAAOS,EAAG,cACRV,EAAeO,EAAuBnB,GAASa,IAAQ,OACpDV,OAIP,OAAOmB,EAAG,cAACV,EAAehB,EAAaiB,IAAQ,OAAKV,IAGxD,OAAOmB,EAAG,cAACT,GAAO,OAAKV,IAGzB,IAAMqB,EAAqB,CACzBP,YAAW,SAAEjC,GACX,OAAKqB,EAAUrB,GAGR,IAAIyB,SAAQ,SAACC,EAASe,GAC3BzC,EAAIsB,MAAK,SAAAtB,GACFA,EAIDA,EAAI,GACNyC,EAAOzC,EAAI,IAEX0B,EAAQ1B,EAAI,IANZ0B,EAAQ1B,SALLA,IAkBP0C,EACJ,6cAEIC,EAAiB,mBAGjBC,EAAqB,CAAC,uBAGtBC,EAAY,CAAC,sBAAuB,qBAEpCC,GAAkB,WAExB,SAASC,GAAcjB,GACrB,OAAOa,EAAelF,KAAKqE,KAA+C,IAAtCc,EAAmBtI,QAAQwH,GAEjE,SAASkB,GAAWlB,GAClB,OAAOY,EAAYjF,KAAKqE,KAAsC,IAA7Be,EAAUvI,QAAQwH,GAGrD,SAASmB,GAAenB,GACtB,OAAOgB,GAAgBrF,KAAKqE,IAAkB,WAATA,EAGvC,SAASoB,GAAe1B,GACtB,OAAOA,EAAQF,MAAK,SAAAF,GAClB,MAAO,CAAC,KAAMA,MAEb+B,OAAM,SAAAC,GAAG,MAAI,CAACA,MAGnB,SAASC,GAAevB,GACtB,QACEiB,GAAajB,IACbkB,GAAUlB,IACVmB,GAAcnB,IAoBlB,SAASwB,GAAWxB,EAAMQ,GACxB,OAAKe,GAAcvB,IAAU1D,EAAKkE,GAG3B,WAAiC,IAAa,IAAzBT,EAAU,UAAH,6CAAG,GAAE,mBAAKV,EAAM,iCAANA,EAAM,kBACjD,OAAI/C,EAAKyD,EAAQ0B,UAAYnF,EAAKyD,EAAQ2B,OAASpF,EAAKyD,EAAQ4B,UACvDzB,EAAmBF,EAAMO,EAAS,cAACP,EAAMQ,EAAKT,GAAO,OAAKV,KAE5Da,EAAmBF,EAAMoB,GAAc,IAAIzB,SAAQ,SAACC,EAASe,GAClEJ,EAAS,cAACP,EAAMQ,EAAKpE,OAAOwF,OAAO,GAAI7B,EAAS,CAC9C0B,QAAS7B,EACT8B,KAAMf,KACN,OAAKtB,UAVFmB,EAdNb,QAAQ3E,UAAU6G,UACrBlC,QAAQ3E,UAAU6G,QAAU,SAAUhC,GACpC,IAAMH,EAAU3H,KAAK+J,YACrB,OAAO/J,KAAKyH,MACV,SAAAuC,GAAK,OAAIrC,EAAQE,QAAQC,KAAYL,MAAK,kBAAMuC,QAChD,SAAAC,GAAM,OAAItC,EAAQE,QAAQC,KAAYL,MAAK,WACzC,MAAMwC,UAuBd,IAAMC,GAAM,KACNC,GAAoB,IACtBC,IAAQ,EACRC,GAAc,EACdC,GAAY,EAEhB,SAASC,KACP,IAAIC,EAAaC,EAAYC,EAGrBC,EAAyC,oBAArB3I,EAAGjB,eAAgCiB,EAAGjB,gBAAkBiB,EAAGjB,gBAAkBiB,EAAGlB,oBACpG8J,EAAyC,oBAArB5I,EAAGhB,eAAgCgB,EAAGhB,gBAAkBgB,EAAGhB,gBAAkBgB,EAAGlB,oBAE1G0J,EAAcG,EAAWH,YACzBC,EAAaE,EAAWF,WACxBC,EAAWE,EAAWF,SAGxBL,GAAcG,EACdF,GAAYG,EACZL,GAAqB,QAAbM,EAGV,SAASG,GAAOC,EAAQC,GAMtB,GALoB,IAAhBV,IACFE,KAGFO,EAASE,OAAOF,GACD,IAAXA,EACF,OAAO,EAET,IAAI9G,EAAU8G,EAASX,IAAsBY,GAAkBV,IAY/D,OAXIrG,EAAS,IACXA,GAAUA,GAEZA,EAASiH,KAAKC,MAAMlH,EAASkG,IACd,IAAXlG,IAIAA,EAHgB,IAAdsG,IAAoBF,GAGb,GAFA,GAKNU,EAAS,GAAK9G,EAASA,EAGhC,IAmBImH,GAnBEC,GAAiB,UACjBC,GAAiB,UACjBC,GAAY,KACZC,GAAY,KACZC,GAAY,KAEZC,GAAW,GAEjB,SAASC,KACP,IAAIC,EAAiB,GAEbC,EAA2C,oBAAtB5J,EAAGnB,gBAAiCmB,EAAGnB,iBAAmBmB,EAAGnB,iBAAmBmB,EAAGlB,oBACxG+K,EACJD,GAAeA,EAAYC,SAAWD,EAAYC,SAAWP,GAGjE,OAFEK,EAAiBG,GAAgBD,IAAaP,GAEzCK,EAST,SAASI,KACP,GAAKC,KAAL,CAGA,IAAMC,EAAa5H,OAAOqB,KAAKwG,YAAYC,SACvCF,EAAW9J,QACb8J,EAAWrG,SAAQ,SAACuF,GAClB,IAAMiB,EAAcX,GAASN,GACvBkB,EAAeH,YAAYC,QAAQhB,GACrCiB,EACF/H,OAAOwF,OAAOuC,EAAaC,GAE3BZ,GAASN,GAAUkB,MAfzBlB,GAASO,KAqBXK,KAEA,IAAMO,IAAO,EAAAC,eACXpB,GACC,IAEGqB,GAAIF,GAAKE,EACIF,GAAKG,MAAQ,CAC9BC,aAAY,WAAI,WACRC,EAAUL,GAAKA,KAAKM,aAAY,WACpC,EAAKC,kBAEP7M,KAAK8M,MAAM,sBAAsB,WAC/BH,QAGJI,QAAS,CACPC,IAAG,SAAE/M,EAAKgN,GACR,OAAOT,GAAEvM,EAAKgN,MAIFX,GAAKY,UACLZ,GAAKa,UAEvB,SAASC,GAAepK,EAAKqK,EAAOlC,GAClC,IAAMmC,EAAQtK,EAAIuK,WAAW,CAC3BpC,OAAQA,GAAUmB,GAAKa,cAEnBK,EAAiB,GACvBH,EAAMI,aAAe,SAAAjJ,GACnBgJ,EAAe/G,KAAKjC,IAEtBH,OAAOqJ,eAAeL,EAAO,UAAW,CACtCM,IAAG,WACD,OAAOL,EAAMnC,QAEfyC,IAAG,SAAEC,GACHP,EAAMnC,OAAS0C,EACfL,EAAe5H,SAAQ,SAAAkI,GAAK,OAAIA,EAAMD,SAK5C,SAAS7B,KACP,MAA8B,qBAAhBE,aAA+BA,YAAYC,WAAa9H,OAAOqB,KAAKwG,YAAYC,SAAShK,OAGzG,SAAS4L,GAAS1M,EAAK2M,GACrB,QAASA,EAAMC,MAAK,SAACC,GAAI,OAA4B,IAAvB7M,EAAIZ,QAAQyN,MAG5C,SAASC,GAAY9M,EAAK2M,GACxB,OAAOA,EAAMC,MAAK,SAACC,GAAI,OAA2B,IAAtB7M,EAAIZ,QAAQyN,MAG1C,SAASpC,GAAiBX,EAAQM,GAChC,GAAKN,EAAL,CAIA,GADAA,EAASA,EAAOiD,OAAOzK,QAAQ,KAAM,KACjC8H,GAAYA,EAASN,GACvB,OAAOA,EAGT,GADAA,EAASA,EAAOkD,cACD,YAAXlD,EAEF,OAAOC,GAET,GAA6B,IAAzBD,EAAO1K,QAAQ,MACjB,OAAI0K,EAAO1K,QAAQ,UAAY,EACtB2K,GAELD,EAAO1K,QAAQ,UAAY,GAG3BsN,GAAQ5C,EAAQ,CAAC,MAAO,MAAO,MAAO,SAFjCE,GAKFD,GAET,IAAMkD,EAAOH,GAAWhD,EAAQ,CAACG,GAAWC,GAAWC,KACvD,OAAI8C,QAAJ,GAaF,SAASC,KAEP,GAAIhK,EAAKiK,QAAS,CAChB,IAAMC,EAAMD,OAAO,CACjBE,cAAc,IAEhB,GAAID,GAAOA,EAAIE,IACb,OAAOF,EAAIE,IAAIC,QAGnB,OAAOlD,KAGT,SAASmD,GAAa1D,GACpB,IAAMsD,IAAMlK,EAAKiK,SAAUA,SAC3B,IAAKC,EACH,OAAO,EAET,IAAMK,EAAYL,EAAIE,IAAIC,QAC1B,OAAIE,IAAc3D,IAChBsD,EAAIE,IAAIC,QAAUzD,EAClB4D,GAAwBnJ,SAAQ,SAACpB,GAAE,OAAKA,EAAG,CACzC2G,eAEK,GAKX,IAAM4D,GAA0B,GAChC,SAASC,GAAgBxK,IACsB,IAAzCuK,GAAwBtO,QAAQ+D,IAClCuK,GAAwBtI,KAAKjC,GAIX,qBAAXyK,IACTA,EAAO9B,UAAYoB,IAGrB,IAAMW,GAAe,CACnBvG,sBAGEwG,GAAuB9K,OAAO+K,OAAO,CACvCC,UAAW,KACXxE,OAAQA,GACRyE,OAAQzE,GACRsC,UAAWoB,GACXrB,UAAW2B,GACXG,eAAgBA,GAChB9H,eAAgBA,EAChBE,kBAAmBA,EACnB8H,aAAcA,KAGhB,SAASK,GAAqBC,GAC5B,IAAMC,EAAQC,kBACVC,EAAMF,EAAMtN,OAChB,MAAOwN,IAAO,CACZ,IAAMC,EAAOH,EAAME,GACnB,GAAIC,EAAKC,OAASD,EAAKC,MAAMC,WAAaN,EACxC,OAAOG,EAGX,OAAQ,EAGV,IAuDII,GAvDAC,GAAa,CACf/H,KAAI,SAAEgI,GACJ,MAAwB,SAApBA,EAASC,QAAqBD,EAASE,MAClC,eAEF,cAETC,KAAI,SAAEH,GACJ,GAAwB,SAApBA,EAASC,QAAqBD,EAAST,IAAK,CAC9C,IAAMa,EAAkBd,GAAoBU,EAAST,KACrD,IAAyB,IAArBa,EAAwB,CAC1B,IAAMF,EAAQT,kBAAkBvN,OAAS,EAAIkO,EACzCF,EAAQ,IACVF,EAASE,MAAQA,OAOvBG,GAAe,CACjBF,KAAI,SAAEH,GACJ,IAAIM,EAAeC,SAASP,EAASQ,SACrC,IAAIC,MAAMH,GAAV,CAGA,IAAMI,EAAOV,EAASU,KACtB,GAAKtK,MAAMC,QAAQqK,GAAnB,CAGA,IAAMhB,EAAMgB,EAAKxO,OACjB,GAAKwN,EAgBL,OAbIY,EAAe,EACjBA,EAAe,EACNA,GAAgBZ,IACzBY,EAAeZ,EAAM,GAEnBY,EAAe,GACjBN,EAASQ,QAAUE,EAAKJ,GACxBN,EAASU,KAAOA,EAAKC,QACnB,SAACC,EAAMjK,GAAK,QAAKA,EAAQ2J,IAAeM,IAASF,EAAKJ,OAGxDN,EAASQ,QAAUE,EAAK,GAEnB,CACLG,WAAW,EACXC,MAAM,OAKNC,GAAW,iBAEjB,SAASC,GAAajN,GACpB+L,GAAWA,IAAY/N,EAAGC,eAAe+O,IACpCjB,KACHA,GAAWxM,KAAKC,MAAQ,GAAKyH,KAAKC,MAAsB,IAAhBD,KAAKiG,UAC7ClP,EAAGmP,WAAW,CACZlR,IAAK+Q,GACLzJ,KAAMwI,MAGV/L,EAAO+L,SAAWA,GAGpB,SAASqB,GAAmBpN,GAC1B,GAAIA,EAAOqN,SAAU,CACnB,IAAMA,EAAWrN,EAAOqN,SACxBrN,EAAOsN,eAAiB,CACtBC,IAAKF,EAASE,IACdC,KAAMH,EAASG,KACfC,MAAOzN,EAAOwG,YAAc6G,EAASI,MACrCC,OAAQ1N,EAAO2N,aAAeN,EAASK,SAK7C,SAASE,GAAWC,EAAQnH,GAC1B,IAAIoH,EAAS,GACTC,EAAY,GAchB,OALED,EAASD,EAAOtQ,MAAM,KAAK,IAAMmJ,EACjCqH,EAAYF,EAAOtQ,MAAM,KAAK,IAAM,GAGtCuQ,EAASA,EAAOE,oBACRF,GACN,IAAK,UACL,IAAK,OACL,IAAK,cACHA,EAAS,YACT,MACF,IAAK,YACHA,EAAS,MACT,MACF,IAAK,MACL,IAAK,SACHA,EAAS,QACT,MACF,IAAK,aACHA,EAAS,UACT,MAGJ,MAAO,CACLA,SACAC,aAIJ,SAASE,GAAoBjO,GAC3B,MAKIA,EAJFkO,aAAK,IAAG,KAAE,IAIRlO,EAJUmO,aAAK,IAAG,KAAE,IAIpBnO,EAJsB6N,cAAM,IAAG,KAAE,IAIjC7N,EAHF6H,gBAAQ,IAAG,KAAE,EAAEuG,EAGbpO,EAHaoO,MAAOC,EAGpBrO,EAHoBqO,QACtB3H,EAEE1G,EAFF0G,SAAU4H,EAERtO,EAFQsO,gBACVC,EACEvO,EADFuO,WAAY9H,EACVzG,EADUyG,WAAY+H,EACtBxO,EADsBwO,kBAIpBC,EAAa,GAGnB,EAA8Bb,GAAUC,EAAQnH,GAAxCoH,EAAM,EAANA,OAAQC,EAAS,EAATA,UACZW,EAAcL,EAGZM,EAAaC,GAAiB5O,EAAQmO,GAGtCU,EAAcC,GAAeZ,GAG7Ba,EAAYC,GAAYhP,GAG1BiP,EAAqBT,EAGrBU,EAAoBzI,EAGpB0I,EAAcZ,EAGZa,GAAgBvH,GAAY,IAAIlI,QAAQ,KAAM,KAI9C0P,EAAa,CACjBC,MAAOC,iBACPC,QAASD,MACTE,WAAYF,QACZG,eAAgBH,MAChBI,YAAaC,GAAeR,GAC5BS,kBAAmBN,OACnBO,mBAAoBP,OACpBQ,kBAAmBR,OACnBS,YAA6CT,YAC7CV,cACAoB,YAAa9B,EACbQ,aACAuB,iBAAkBhB,EAClBV,kBAAmBS,EACnBnB,OAAQA,EAAOE,oBACfD,YACAoC,UAAW/B,EACXM,cACAU,eACAgB,SAAUrB,EACVsB,eAAgBlB,EAChBmB,oBAAqBhC,EACrBiC,UAAW,EACXC,aAAc,EAEdC,gBAAYC,EACZC,aAASD,EACTE,QAAIF,EACJG,qBAAiBH,EACjBI,iBAAaJ,EACbK,oBAAgBL,EAChBM,WAAW,GAGb3Q,OAAOwF,OAAO7F,EAAQqP,EAAYZ,GAGpC,SAASG,GAAkB5O,EAAQmO,GAU/B,IATF,IAAIQ,EAAa3O,EAAO2O,YAAc,QAE9BsC,EAAiB,CACrBC,KAAM,MACNC,QAAS,KACTC,IAAK,MAEDC,EAAqBhR,OAAOqB,KAAKuP,GACjCK,EAASnD,EAAMH,oBACZpL,EAAQ,EAAGA,EAAQyO,EAAmBlT,OAAQyE,IAAS,CAC9D,IAAM2O,EAAKF,EAAmBzO,GAC9B,IAA4B,IAAxB0O,EAAO7U,QAAQ8U,GAAY,CAC7B5C,EAAasC,EAAeM,GAC5B,OAIN,OAAO5C,EAGT,SAASG,GAAgBZ,GACvB,IAAIW,EAAcX,EAIlB,OAHIW,IACFA,EAAcX,EAAMF,qBAEfa,EAGT,SAASe,GAAgB4B,GACvB,OAAOjH,GACHA,KACAiH,EAGN,SAASxC,GAAahP,GACpB,IAAMyR,EACH,SAEC1C,EAAY/O,EAAOoQ,UAAYqB,EASnC,OAPMzR,EAAO0R,YACT3C,EAAY/O,EAAO0R,YACV1R,EAAO2R,MAAQ3R,EAAO2R,KAAKC,MACpC7C,EAAY/O,EAAO2R,KAAKC,KAIrB7C,EAGT,IAAI8C,GAAgB,CAClBzN,YAAa,SAAUpE,GACrBiN,GAAYjN,GACZoN,GAAkBpN,GAClBiO,GAAmBjO,KAInB8R,GAAkB,CACpB1F,KAAI,SAAEH,GACoB,YAApB,aAAOA,KACTA,EAAS8F,UAAY9F,EAAS+F,SAKhCnV,GAAiB,CACnBuH,YAAa,SAAUpE,GACrB,MAAiDA,EAAzCqO,EAAO,EAAPA,QAASxG,EAAQ,EAARA,SAAU0G,EAAU,EAAVA,WAAYH,EAAK,EAALA,MAEjCW,EAAYC,GAAYhP,GAExBoP,GAAgBvH,GAAY,IAAIlI,QAAQ,IAAK,KAEnDK,EAASwB,EAAWnB,OAAOwF,OAAO7F,EAAQ,CACxCsP,MAAOC,iBACPC,QAASD,MACTE,WAAYF,QACZG,eAAgBH,MAChBI,YAAaC,GAAeR,GAC5BV,YAAaL,EACbe,eACAgB,SAAUrB,EACVsB,eAAgB9B,EAChB4B,UAAW/B,EACX4C,WAAW,EACXhB,YAA6CT,YAC7CM,kBAAmBN,OACnBO,mBAAoBP,OACpBQ,kBAAmBR,YAKrBvS,GAAgB,CAClBoH,YAAa,SAAUpE,GACrB,MAAqDA,EAA7CkO,EAAK,EAALA,MAAOC,EAAK,EAALA,MAAK,IAAEN,cAAM,IAAG,KAAE,MAAEnH,gBAAQ,IAAG,KAAE,EAC1CiI,EAAaC,GAAiB5O,EAAQmO,GACtCU,EAAcC,GAAeZ,GACnCjB,GAAYjN,GAEZ,MAA8B4N,GAAUC,EAAQnH,GAAxCoH,EAAM,EAANA,OAAQC,EAAS,EAATA,UAEhB/N,EAASwB,EAAWnB,OAAOwF,OAAO7F,EAAQ,CACxC2O,aACAE,cACAoB,YAAa9B,EACbL,SACAC,iBAKFhR,GAAgB,CAClBqH,YAAa,SAAUpE,GACrBoN,GAAkBpN,GAElBA,EAASwB,EAAWnB,OAAOwF,OAAO7F,EAAQ,CACxCuQ,UAAW,EACXC,aAAc,OAKhByB,GAAyB,CAC3B7N,YAAa,SAAUpE,GACrB,IAAQkS,EAA4BlS,EAA5BkS,wBAERlS,EAAOmS,iBAAmB,eACM,IAA5BD,EACFlS,EAAOmS,iBAAmB,WACW,IAA5BD,IACTlS,EAAOmS,iBAAmB,UAO1BC,GAAgB,CACpBhG,KAAI,SAAEH,GAEAA,EAASoG,mBAAqBpG,EAASqG,iBACzCrG,EAASqG,eAAiBrG,EAASoG,kBAEjCpG,EAASsG,kBAAoBtG,EAASuG,gBACxCvG,EAASuG,cAAgBvG,EAASsG,mBAKlCE,GAAY,CAChBzG,cAEAM,gBACAuF,iBACA/U,kBAAmB+U,GACnBC,mBACAjV,kBACAG,iBACAD,iBACAkV,0BACAG,kBAEIM,GAAQ,CACZ,UACA,cACA,gBACA,kBAEIC,GAAW,GAEXC,GAAY,CAAC,UAAW,OAAQ,SAAU,YAEhD,SAASC,GAAiBC,EAAY3P,EAAQiB,GAC5C,OAAO,SAAUjC,GACf,OAAOgB,EAAO4P,GAAmBD,EAAY3Q,EAAKiC,KAItD,SAAS4O,GAAaF,EAAY7G,GAAmE,IAAzDgH,EAAa,UAAH,6CAAG,GAAI7O,EAAc,UAAH,6CAAG,GAAI8O,EAAe,UAAH,8CACzF,GAAItS,EAAcqL,GAAW,CAC3B,IAAMkH,GAA0B,IAAjBD,EAAwBjH,EAAW,GAIlD,IAAK,IAAMhQ,KAHPsE,EAAK0S,KACPA,EAAaA,EAAWhH,EAAUkH,IAAW,IAE7BlH,EAChB,GAAInL,EAAOmS,EAAYhX,GAAM,CAC3B,IAAImX,EAAYH,EAAWhX,GACvBsE,EAAK6S,KACPA,EAAYA,EAAUnH,EAAShQ,GAAMgQ,EAAUkH,IAE5CC,EAEM3S,EAAM2S,GACfD,EAAOC,GAAanH,EAAShQ,GACpB2E,EAAcwS,KACvBD,EAAOC,EAAUnP,KAAOmP,EAAUnP,KAAOhI,GAAOmX,EAAUpN,OAJ1DqN,QAAQC,KAAK,QAAD,OAASR,EAAU,iEAAyD7W,EAAG,WAMxD,IAA5B2W,GAAUnW,QAAQR,GACvBsE,EAAK0L,EAAShQ,MAChBkX,EAAOlX,GAAO4W,GAAgBC,EAAY7G,EAAShQ,GAAMmI,IAGtD8O,IACHC,EAAOlX,GAAOgQ,EAAShQ,IAI7B,OAAOkX,EAIT,OAHW5S,EAAK0L,KACdA,EAAW4G,GAAgBC,EAAY7G,EAAU7H,IAE5C6H,EAGT,SAAS8G,GAAoBD,EAAY3Q,EAAKiC,GAAsC,IAAzBmP,EAAkB,UAAH,8CAIxE,OAHIhT,EAAKkS,GAAUrO,eACjBjC,EAAMsQ,GAAUrO,YAAY0O,EAAY3Q,IAEnC6Q,GAAYF,EAAY3Q,EAAKiC,EAAa,GAAImP,GAGvD,SAASC,GAASV,EAAY3P,GAC5B,GAAIrC,EAAO2R,GAAWK,GAAa,CACjC,IAAMW,EAAWhB,GAAUK,GAC3B,OAAKW,EAKE,SAAUC,EAAMC,GACrB,IAAI3P,EAAUyP,EACVlT,EAAKkT,KACPzP,EAAUyP,EAASC,IAGrBA,EAAOV,GAAYF,EAAYY,EAAM1P,EAAQoI,KAAMpI,EAAQI,aAE3D,IAAMgI,EAAO,CAACsH,GACM,qBAATC,GACTvH,EAAK3J,KAAKkR,GAERpT,EAAKyD,EAAQC,MACf6O,EAAa9O,EAAQC,KAAKyP,GACjBjT,EAAMuD,EAAQC,QACvB6O,EAAa9O,EAAQC,MAEvB,IAAMG,EAAcpG,EAAG8U,GAAYc,MAAM5V,EAAIoO,GAC7C,OAAIjH,GAAU2N,GACLC,GAAmBD,EAAY1O,EAAaJ,EAAQI,YAAac,GAAa4N,IAEhF1O,GAzBA,WACLiP,QAAQ3U,MAAM,sCAAD,OAAuCoU,EAAU,QA2BpE,OAAO3P,EAGT,IAAM0Q,GAAWxT,OAAOa,OAAO,MAEzB4S,GAAQ,CACZ,uBACA,gBACA,kBACA,SACA,UACA,SAGF,SAASC,GAAe9P,GACtB,OAAO,SAAgB,GAGpB,IAFD0B,EAAI,EAAJA,KACAC,EAAQ,EAARA,SAEMzD,EAAM,CACV6R,OAAQ,GAAF,OAAK/P,EAAI,yBAAiBA,EAAI,oBAEtC1D,EAAKoF,IAASA,EAAKxD,GACnB5B,EAAKqF,IAAaA,EAASzD,IAI/B2R,GAAMlS,SAAQ,SAAUqC,GACtB4P,GAAS5P,GAAQ8P,GAAc9P,MAGjC,IAAIgQ,GAAY,CACdC,MAAO,CAAC,UACRC,MAAO,CAAC,UACRC,QAAS,CAAC,SACV3R,KAAM,CAAC,WAGT,SAAS4R,GAAW,GAKjB,IAJDC,EAAO,EAAPA,QACA5O,EAAO,EAAPA,QACAC,EAAI,EAAJA,KACAC,EAAQ,EAARA,SAEIzD,GAAM,EACN8R,GAAUK,IACZnS,EAAM,CACJ6R,OAAQ,iBACRM,UACAC,SAAUN,GAAUK,IAEtB/T,EAAKmF,IAAYA,EAAQvD,KAEzBA,EAAM,CACJ6R,OAAQ,sCAEVzT,EAAKoF,IAASA,EAAKxD,IAErB5B,EAAKqF,IAAaA,EAASzD,GAG7B,IAAIqS,GAAwBnU,OAAO+K,OAAO,CACxCC,UAAW,KACXgJ,YAAaA,KAGTI,GAAc,WAClB,IAAIC,EACJ,OAAO,WAIL,OAHKA,IACHA,EAAU,IAAI1V,WAET0V,GANS,GAUpB,SAASd,GAAOe,EAAKxR,EAAQiJ,GAC3B,OAAOuI,EAAIxR,GAAQyQ,MAAMe,EAAKvI,GAGhC,SAASwI,KACP,OAAOhB,GAAMa,KAAc,MAAO,MAAF,qBAAMI,YAExC,SAASC,KACP,OAAOlB,GAAMa,KAAc,OAAQ,MAAF,qBAAMI,YAEzC,SAAS/L,KACP,OAAO8K,GAAMa,KAAc,QAAS,MAAF,qBAAMI,YAE1C,SAASE,KACP,OAAOnB,GAAMa,KAAc,QAAS,MAAF,qBAAMI,YAG1C,IAqCIG,GACAC,GACAC,GAvCAC,GAAwB9U,OAAO+K,OAAO,CACxCC,UAAW,KACXuJ,IAAKA,GACLE,KAAMA,GACNhM,MAAOA,GACPiM,MAAOA,KAST,SAASK,GAAU5U,GACjB,OAAO,WACL,IACE,OAAOA,EAAGoT,MAAMpT,EAAIqU,WACpB,MAAOQ,GAEPhC,QAAQ3U,MAAM2W,KAKpB,SAASC,GAAiBhS,GACxB,IAAMiS,EAAe,GACrB,IAAK,IAAMtR,KAAQX,EAAQ,CACzB,IAAMkS,EAAQlS,EAAOW,GACjB1D,EAAKiV,KACPD,EAAatR,GAAQmR,GAASI,UACvBlS,EAAOW,IAGlB,OAAOsR,EAOT,SAASE,GAAsB7W,GAC7B,IACE,OAAOJ,KAAKC,MAAMG,GAClB,MAAOyW,IACT,OAAOzW,EAGT,SAAS8W,GACPtJ,GAEA,GAAkB,YAAdA,EAAKuJ,KACPT,IAAU,OACL,GAAkB,aAAd9I,EAAKuJ,KACdX,GAAM5I,EAAK4I,IACXC,GAAY7I,EAAK4H,OACjB4B,GAA0BZ,GAAK5I,EAAK4H,aAC/B,GAAkB,YAAd5H,EAAKuJ,KAKd,IAJA,IAAM/W,EAAU,CACd+W,KAAM,UACNpS,KAAMkS,GAAqBrJ,EAAKxN,UAEzBqB,EAAI,EAAGA,EAAI4V,GAAuB1X,OAAQ8B,IAAK,CACtD,IAAM6D,EAAW+R,GAAuB5V,GAGxC,GAFA6D,EAASlF,GAELA,EAAQkX,QACV,UAGmB,UAAd1J,EAAKuJ,MACdE,GAAuBjU,SAAQ,SAACkC,GAC9BA,EAAS,CACP6R,KAAM,QACNpS,KAAMkS,GAAqBrJ,EAAKxN,cAMxC,IAAMmX,GAAsB,GAE5B,SAASH,GAA2BZ,EAAKhB,GACvC+B,GAAoBnU,SAAQ,SAACkC,GAC3BA,EAASkR,EAAKhB,MAEhB+B,GAAoB5X,OAAS,EAG/B,SAAS6X,GAAiB5J,GACnBxL,EAAcwL,KACjBA,EAAO,IAET,MAIIkJ,GAAgBlJ,GAHlB1G,EAAO,EAAPA,QACAC,EAAI,EAAJA,KACAC,EAAQ,EAARA,SAEIqQ,EAAa1V,EAAKmF,GAClBwQ,EAAU3V,EAAKoF,GACfwQ,EAAc5V,EAAKqF,GAEzBhC,QAAQC,UAAUJ,MAAK,WACE,qBAAZyR,KACTA,IAAU,EACVF,GAAM,GACNC,GAAY,0BAEdc,GAAoBtT,MAAK,SAACuS,EAAKhB,GAC7B,IAAI7R,EACA6S,GACF7S,EAAM,CACJ6R,OAAQ,qBACRgB,OAEFiB,GAAcvQ,EAAQvD,KAEtBA,EAAM,CACJ6R,OAAQ,wBAA0BA,EAAS,IAAMA,EAAS,KAE5DkC,GAAWvQ,EAAKxD,IAElBgU,GAAevQ,EAASzD,MAEP,qBAAR6S,IACTY,GAA0BZ,GAAKC,OAKrC,IAAMY,GAAyB,GAEzBO,GAAgB,SAAC5V,IACuB,IAAxCqV,GAAuBpZ,QAAQ+D,IACjCqV,GAAuBpT,KAAKjC,IAI1B6V,GAAiB,SAAC7V,GACtB,GAAKA,EAEE,CACL,IAAMoC,EAAQiT,GAAuBpZ,QAAQ+D,GACzCoC,GAAS,GACXiT,GAAuBhT,OAAOD,EAAO,QAJvCiT,GAAuB1X,OAAS,GASpC,SAASmY,GACPX,GAEA,2BADGvJ,EAAI,iCAAJA,EAAI,kBAEPiH,QAAQsC,GAAM/B,MAAMP,QAASjH,GAG/B,IAAImK,GAAWvY,EAAGnB,gBAAkBmB,EAAGnB,iBAClC0Z,KACHA,GAAWvY,EAAGlB,qBAEhB,IAAM6U,GAAO4E,GAAWA,GAAS5E,KAAO,KAClC6E,GACJ7E,IAAqB,YAAbA,GAAKC,IAAoB5T,EAAGyY,QAAQD,kBAAoBxY,EAAGwY,kBAEjE/R,GAAmBpE,OAAO+K,OAAO,CACnCC,UAAW,KACXmL,kBAAmBA,GACnBR,gBAAiBA,GACjBI,cAAeA,GACfC,eAAgBA,GAChBX,mBAAoBA,GACpBY,MAAOA,KAGHI,GAAQ,CAAC,YAAa,uBAAwB,mBAEpD,SAASC,GAAeC,EAAIC,GAG1B,IAFA,IASIC,EATEC,EAAYH,EAAGG,UAEZ9W,EAAI8W,EAAU5Y,OAAS,EAAG8B,GAAK,EAAGA,IAAK,CAC9C,IAAM+W,EAAUD,EAAU9W,GAC1B,GAAI+W,EAAQC,OAAOC,UAAYL,EAC7B,OAAOG,EAKX,IAAK,IAAI/W,EAAI8W,EAAU5Y,OAAS,EAAG8B,GAAK,EAAGA,IAEzC,GADA6W,EAAWH,GAAcI,EAAU9W,GAAI4W,GACnCC,EACF,OAAOA,EAKb,SAASK,GAAcnT,GACrB,OAAOoT,SAASpT,GAGlB,SAASqT,KACP,QAASrb,KAAKsb,MAGhB,SAASC,GAAcC,GACrBxb,KAAKyb,aAAa,MAAOD,GAG3B,SAASE,GAAqBC,EAAYC,EAAUC,GAClD,IAAMC,EAAaH,EAAWD,oBAAoBE,IAAa,GAC/DE,EAAWlW,SAAQ,SAAAmW,GACjB,IAAMC,EAAMD,EAAUE,QAAQD,IAC9BH,EAAMG,GAAOD,EAAUpN,KAAOuN,GAAOH,GAEE,WAAjCA,EAAUE,QAAQE,YACpBJ,EAAUL,oBAAoB,eAAe9V,SAAQ,SAAAwW,GACnDV,GAAoBU,EAAiBR,EAAUC,SAOzD,SAASQ,GAAUC,EAAMC,GACvB,IAAMC,GAAU,EAAH,WAAOC,KAAG,aAAIpY,OAAOqB,KAAK4W,KACjCI,EAAUrY,OAAOqB,KAAK6W,GAa5B,OAZAG,EAAQ9W,SAAQ,SAAA3F,GACd,IAAM0c,EAAWL,EAAKrc,GAChB2c,EAAWL,EAAQtc,GACrBoG,MAAMC,QAAQqW,IAAatW,MAAMC,QAAQsW,IAAaD,EAASxa,SAAWya,EAASza,QAAUya,EAASC,OAAM,SAAA7S,GAAK,OAAI2S,EAASnc,SAASwJ,QAG3IsS,EAAKrc,GAAO2c,EACZJ,EAAQM,OAAO7c,OAEjBuc,EAAQ5W,SAAQ,SAAA3F,UACPqc,EAAKrc,MAEPqc,EAGT,SAASS,GAAUnC,GACjB,IAAMe,EAAaf,EAAGK,OAChBqB,EAAO,GACbjY,OAAOqJ,eAAekN,EAAI,QAAS,CACjCjN,IAAG,WACD,IAAMkO,EAAQ,GACdH,GAAoBC,EAAY,WAAYE,GAE5C,IAAMmB,EAAgBrB,EAAWD,oBAAoB,oBAAsB,GAQ3E,OAPAsB,EAAcpX,SAAQ,SAAAmW,GACpB,IAAMC,EAAMD,EAAUE,QAAQD,IACzBH,EAAMG,KACTH,EAAMG,GAAO,IAEfH,EAAMG,GAAKvV,KAAKsV,EAAUpN,KAAOuN,GAAOH,OAEnCM,GAASC,EAAMT,MAK5B,SAASoB,GAAYC,GACnB,IAKIpC,EALJ,EAGIoC,EAAM1B,QAAU0B,EAAMlT,MAFxB6Q,EAAM,EAANA,OACAsC,EAAU,EAAVA,WAKEtC,IACFC,EAAWH,GAAc3a,KAAK2O,IAAKkM,IAGhCC,IACHA,EAAW9a,KAAK2O,KAGlBwO,EAAWC,OAAStC,EAGtB,SAASuC,GAAiBtB,GAExB,IAAMuB,EAAQ,oBAMd,OALAjZ,OAAOqJ,eAAeqO,EAAWuB,EAAO,CACtCC,cAAc,EACdC,YAAY,EACZxT,OAAO,IAEF+R,EAGT,SAASG,GAAQvX,GACf,IAAM8Y,EAAK,SACLC,EAAO,WAWb,OAVIhZ,EAASC,IAAQN,OAAOsZ,aAAahZ,IAEvCN,OAAOqJ,eAAe/I,EAAK8Y,EAAI,CAC7BF,cAAc,EACdC,YAAY,EACZxT,OAAO,EAAF,cACF0T,GAAO,KAIP/Y,EAGT,IAAMiZ,GAAa,yBACnB,SAASC,GAAoBC,EAAWC,GAClCA,GACF1Z,OAAOqB,KAAKqY,GAAYnY,SAAQ,SAACqC,GAC/B,IAAM+V,EAAU/V,EAAKgW,MAAML,IAC3B,GAAII,EAAS,CACX,IAAME,EAAcF,EAAQ,GAC5BF,EAAU7V,GAAQ8V,EAAW9V,GAC7B6V,EAAUI,GAAeH,EAAWG,OAM5C,IAAMC,GAASC,KACTC,GAAcC,UAEdC,GAAc,KAEdC,GAAYxZ,GAAO,SAAC3D,GACxB,OAAOgE,EAAShE,EAAIsC,QAAQ4a,GAAa,SAG3C,SAASE,GAAkB9C,GACzB,IAAM+C,EAAkB/C,EAAWF,aAC7BkD,EAAkB,SAAUzB,GAAgB,2BAAN9M,EAAI,iCAAJA,EAAI,kBAE9C,GAAIpQ,KAAK2O,KAAQ3O,KAAKic,SAAWjc,KAAKic,QAAQ2C,QAC5C1B,EAAQsB,GAAUtB,OACb,CAEL,IAAM2B,EAAWL,GAAUtB,GACvB2B,IAAa3B,GACfwB,EAAgB9G,MAAM5X,KAAM,CAAC6e,GAAQ,OAAKzO,IAG9C,OAAOsO,EAAgB9G,MAAM5X,KAAM,CAACkd,GAAK,OAAK9M,KAEhD,IAEEuL,EAAWF,aAAekD,EAC1B,MAAOjc,GACPiZ,EAAWmD,cAAgBH,GAI/B,SAASI,GAAU9W,EAAMD,EAASgX,GAChC,IAAMC,EAAUjX,EAAQC,GACxBD,EAAQC,GAAQ,WAGd,GAFAoV,GAAgBrd,MAChBye,GAAiBze,MACbif,EAAS,4BAHc7O,EAAI,yBAAJA,EAAI,gBAI7B,OAAO6O,EAAQrH,MAAM5X,KAAMoQ,KAI5B+N,GAAOe,eACVf,GAAOe,cAAe,EACtBd,KAAO,WAAwB,IAAdpW,EAAU,UAAH,6CAAG,GAEzB,OADA+W,GAAS,SAAU/W,GACZmW,GAAOnW,IAEhBoW,KAAKe,MAAQhB,GAAOgB,MAEpBb,UAAY,WAAwB,IAAdtW,EAAU,UAAH,6CAAG,GAE9B,OADA+W,GAAS,UAAW/W,GACbqW,GAAYrW,KAIvB,IAAMoX,GAAmB,CACvB,oBACA,gBACA,mBACA,kBACA,oBACA,eACA,WACA,gBAGF,SAASC,GAAWzE,EAAIF,GACtB,IAAMiB,EAAaf,EAAG0E,IAAI1E,EAAG2E,QAC7B7E,EAAM9U,SAAQ,SAAA4Z,GACR1a,EAAO6W,EAAY6D,KACrB5E,EAAG4E,GAAQ7D,EAAW6D,OAK5B,SAASC,GAAS9Y,EAAMwW,GACtB,IAAKA,EACH,OAAO,EAGT,GAAIna,UAAIgF,SAAW3B,MAAMC,QAAQtD,UAAIgF,QAAQrB,IAC3C,OAAO,EAKT,GAFAwW,EAAaA,EAAWuC,SAAWvC,EAE/B5Y,EAAK4Y,GACP,QAAI5Y,EAAK4Y,EAAWwC,cAAchZ,QAG9BwW,EAAWyC,OACbzC,EAAWyC,MAAM5X,SACjB3B,MAAMC,QAAQ6W,EAAWyC,MAAM5X,QAAQrB,KAM3C,GAAIpC,EAAK4Y,EAAWxW,KAAUN,MAAMC,QAAQ6W,EAAWxW,IACrD,OAAO,EAET,IAAMkZ,EAAS1C,EAAW0C,OAC1B,OAAIxZ,MAAMC,QAAQuZ,KACPA,EAAO5R,MAAK,SAAAxB,GAAK,OAAIgT,GAAQ9Y,EAAM8F,WAD9C,EAKF,SAASqT,GAAWC,EAAWvZ,EAAO2W,GACpC3W,EAAMZ,SAAQ,SAAAe,GACR8Y,GAAQ9Y,EAAMwW,KAChB4C,EAAUpZ,GAAQ,SAAUyJ,GAC1B,OAAOpQ,KAAK2O,KAAO3O,KAAK2O,IAAIqR,YAAYrZ,EAAMyJ,QAMtD,SAAS6P,GAAkBF,EAAW5C,GAA2B,IAAf+C,EAAW,UAAH,6CAAG,GAC3DC,GAAUhD,GAAYvX,SAAQ,SAACe,GAAI,OAAKyZ,GAAWL,EAAWpZ,EAAMuZ,MAGtE,SAASC,GAAWhD,GAAwB,IAAZ3W,EAAQ,UAAH,6CAAG,GAQtC,OAPI2W,GACF9Y,OAAOqB,KAAKyX,GAAYvX,SAAQ,SAACqC,GACJ,IAAvBA,EAAKxH,QAAQ,OAAe8D,EAAK4Y,EAAWlV,KAC9CzB,EAAMC,KAAKwB,MAIVzB,EAGT,SAAS4Z,GAAYL,EAAWpZ,EAAMuZ,IACJ,IAA5BA,EAASzf,QAAQkG,IAAiB7B,EAAOib,EAAWpZ,KACtDoZ,EAAUpZ,GAAQ,SAAUyJ,GAC1B,OAAOpQ,KAAK2O,KAAO3O,KAAK2O,IAAIqR,YAAYrZ,EAAMyJ,KAKpD,SAASiQ,GAAkBrd,EAAKma,GAE9B,IAAImD,EAOJ,OARAnD,EAAaA,EAAWuC,SAAWvC,EAGjCmD,EADE/b,EAAK4Y,GACQA,EAEAna,EAAIud,OAAOpD,GAE5BA,EAAamD,EAAatY,QACnB,CAACsY,EAAcnD,GAGxB,SAASqD,GAAW5F,EAAI6F,GACtB,GAAIpa,MAAMC,QAAQma,IAAaA,EAASte,OAAQ,CAC9C,IAAMue,EAASrc,OAAOa,OAAO,MAC7Bub,EAAS7a,SAAQ,SAAA+a,GACfD,EAAOC,IAAY,KAErB/F,EAAGgG,aAAehG,EAAG8F,OAASA,GAIlC,SAASG,GAAYC,EAAQnF,GAC3BmF,GAAUA,GAAU,IAAIvf,MAAM,KAC9B,IAAMoO,EAAMmR,EAAO3e,OAEP,IAARwN,EACFgM,EAAWT,QAAU4F,EAAO,GACX,IAARnR,IACTgM,EAAWT,QAAU4F,EAAO,GAC5BnF,EAAWoF,SAAWD,EAAO,IAIjC,SAASE,GAAU7D,EAAY8D,GAC7B,IAAI1Z,EAAO4V,EAAW5V,MAAQ,GACxBwF,EAAUoQ,EAAWpQ,SAAW,GAEtC,GAAoB,oBAATxF,EACT,IACEA,EAAOA,EAAK1C,KAAKoc,GACjB,MAAO5H,GACH9F,wHAAY2N,eACd7J,QAAQC,KAAK,yEAA0E/P,QAI3F,IAEEA,EAAO/E,KAAKC,MAAMD,KAAK2e,UAAU5Z,IACjC,MAAO8R,IAaX,OAVKzU,EAAc2C,KACjBA,EAAO,IAGTlD,OAAOqB,KAAKqH,GAASnH,SAAQ,SAAAkR,IAC8B,IAArDmK,EAAQG,oBAAoB3gB,QAAQqW,IAAuBhS,EAAOyC,EAAMuP,KAC1EvP,EAAKuP,GAAc/J,EAAQ+J,OAIxBvP,EAGT,IAAM8Z,GAAa,CAAC3d,OAAQsH,OAAQsW,QAASjd,OAAQgC,MAAO,MAE5D,SAASkb,GAAgBtZ,GACvB,OAAO,SAAmBuZ,EAAQC,GAC5BzhB,KAAK2O,MACP3O,KAAK2O,IAAI1G,GAAQuZ,IAKvB,SAASE,GAAevE,EAAYhC,GAClC,IAAMwG,EAAexE,EAAWyE,UAC1BC,EAAa1E,EAAW2E,QACxBC,EAAY5E,EAAW0C,OAEzBmC,EAAW7E,EAAW8E,MAErBD,IACH7E,EAAW8E,MAAQD,EAAW,IAGhC,IAAMJ,EAAY,GAuClB,OAtCIvb,MAAMC,QAAQqb,IAChBA,EAAa/b,SAAQ,SAAAsc,GACnBN,EAAUnb,KAAKyb,EAASve,QAAQ,SAAa,KAAI,gBAChC,qBAAbue,IACE7b,MAAMC,QAAQ0b,IAChBA,EAASvb,KAAK,QACdub,EAASvb,KAAK,WAEdub,EAAS/Z,KAAO,CACd0R,KAAMjW,OACNgc,QAAS,IAEXsC,EAAShY,MAAQ,CACf2P,KAAM,CAACjW,OAAQsH,OAAQsW,QAASjb,MAAOhC,OAAQd,MAC/Cmc,QAAS,SAMf9a,EAAcid,IAAeA,EAAWI,OAC1CL,EAAUnb,KACR0U,EAAa,CACXgH,WAAYC,GAAeP,EAAWI,OAAO,MAI/C5b,MAAMC,QAAQyb,IAChBA,EAAUnc,SAAQ,SAAAyc,GACZzd,EAAcyd,IAAaA,EAASJ,OACtCL,EAAUnb,KACR0U,EAAa,CACXgH,WAAYC,GAAeC,EAASJ,OAAO,SAM9CL,EAGT,SAASU,GAAeriB,EAAK0Z,EAAM4I,EAAcC,GAE/C,OAAInc,MAAMC,QAAQqT,IAAyB,IAAhBA,EAAKxX,OACvBwX,EAAK,GAEPA,EAGT,SAASyI,GAAgBH,GAA+C,IAAxCQ,EAAa,UAAH,8CAAqBza,EAAO,uCAC9Dma,EAAa,GAqEnB,OApEKM,IACHN,EAAWO,MAAQ,CACjB/I,KAAMjW,OACNsG,MAAO,IAGFhC,EAAQ2a,cACXR,EAAWS,iBAAmB,CAC5BjJ,KAAM,KACN3P,MAAO,IAETmY,EAAWU,iBAAmB,CAC5BlJ,KAAM,KACN3P,MAAO,KAKbmY,EAAWW,oBAAsB,CAC/BnJ,KAAMjW,OACNsG,MAAO,IAETmY,EAAW1B,SAAW,CACpB9G,KAAM,KACN3P,MAAO,GACP+Y,SAAU,SAAUvB,EAAQC,GAC1B,IAAMf,EAASrc,OAAOa,OAAO,MAC7Bsc,EAAO5b,SAAQ,SAAA+a,GACbD,EAAOC,IAAY,KAErB3gB,KAAKgjB,QAAQ,CACXtC,cAKJra,MAAMC,QAAQ2b,GAChBA,EAAMrc,SAAQ,SAAA3F,GACZkiB,EAAWliB,GAAO,CAChB0Z,KAAM,KACNoJ,SAAUxB,GAAethB,OAGpB2E,EAAcqd,IACvB5d,OAAOqB,KAAKuc,GAAOrc,SAAQ,SAAA3F,GACzB,IAAMgjB,EAAOhB,EAAMhiB,GACnB,GAAI2E,EAAcqe,GAAO,CACvB,IAAIjZ,EAAQiZ,EAAKvD,QACbnb,EAAKyF,KACPA,EAAQA,KAGViZ,EAAKtJ,KAAO2I,GAAcriB,EAAKgjB,EAAKtJ,MAEpCwI,EAAWliB,GAAO,CAChB0Z,MAAyC,IAAnC0H,GAAW5gB,QAAQwiB,EAAKtJ,MAAesJ,EAAKtJ,KAAO,KACzD3P,QACA+Y,SAAUxB,GAAethB,QAEtB,CACL,IAAM0Z,EAAO2I,GAAcriB,EAAKgjB,GAChCd,EAAWliB,GAAO,CAChB0Z,MAAoC,IAA9B0H,GAAW5gB,QAAQkZ,GAAeA,EAAO,KAC/CoJ,SAAUxB,GAAethB,QAK1BkiB,EAGT,SAASe,GAAWhG,GAElB,IACEA,EAAMiG,GAAK3gB,KAAKC,MAAMD,KAAK2e,UAAUjE,IACrC,MAAO7D,IAoBT,OAlBA6D,EAAMkG,gBAAkBre,EACxBmY,EAAMmG,eAAiBte,EAEvBmY,EAAMpd,OAASod,EAAMpd,QAAU,GAE1BgF,EAAOoY,EAAO,YACjBA,EAAM1B,OAAS,IAGb1W,EAAOoY,EAAO,cAChBA,EAAM1B,OAAiC,YAAxB,aAAO0B,EAAM1B,QAAsB0B,EAAM1B,OAAS,GACjE0B,EAAM1B,OAAO8H,SAAWpG,EAAMoG,UAG5B1e,EAAcsY,EAAM1B,UACtB0B,EAAMpd,OAASuE,OAAOwF,OAAO,GAAIqT,EAAMpd,OAAQod,EAAM1B,SAGhD0B,EAGT,SAASqG,GAAe3I,EAAI4I,GAC1B,IAAIvC,EAAUrG,EA4Cd,OA3CA4I,EAAe5d,SAAQ,SAAA6d,GACrB,IAAMC,EAAWD,EAAc,GACzBzZ,EAAQyZ,EAAc,GAC5B,GAAIC,GAA6B,qBAAV1Z,EAAuB,CAC5C,IAGI2Z,EAHEC,EAAWH,EAAc,GACzBI,EAAYJ,EAAc,GAG5BzY,OAAO8Y,UAAUJ,GACnBC,EAAOD,EACGA,EAEmB,kBAAbA,GAAyBA,IAEvCC,EAD8B,IAA5BD,EAASjjB,QAAQ,OACZijB,EAASK,OAAO,GAEhBnJ,EAAGoJ,YAAYN,EAAUzC,IALlC0C,EAAO1C,EASLjW,OAAO8Y,UAAUH,GACnB1C,EAAUjX,EACA4Z,EAGNvd,MAAMC,QAAQqd,GAChB1C,EAAU0C,EAAK1V,MAAK,SAAAgW,GAClB,OAAOrJ,EAAGoJ,YAAYJ,EAAUK,KAAcja,KAEvCpF,EAAc+e,GACvB1C,EAAU5c,OAAOqB,KAAKie,GAAM1V,MAAK,SAAAiW,GAC/B,OAAOtJ,EAAGoJ,YAAYJ,EAAUD,EAAKO,MAAcla,KAGrDqN,QAAQ3U,MAAM,kBAAmBihB,GAXnC1C,EAAU0C,EAAK3Z,GAeb6Z,IACF5C,EAAUrG,EAAGoJ,YAAYH,EAAW5C,QAInCA,EAGT,SAASkD,GAAmBvJ,EAAIwJ,EAAOlH,EAAOmH,GAC5C,IAAMC,EAAW,GAmCjB,OAjCIje,MAAMC,QAAQ8d,IAAUA,EAAMjiB,QAYhCiiB,EAAMxe,SAAQ,SAAC8d,EAAU9c,GACC,kBAAb8c,EACJA,EAGc,WAAbA,EACFY,EAAS,IAAM1d,GAASsW,EACF,cAAbwG,EACTY,EAAS,IAAM1d,GAASsW,EAAM1B,QAAS0B,EAAM1B,OAAO6I,UAAuBA,EAClC,IAAhCX,EAASjjB,QAAQ,WAC1B6jB,EAAS,IAAM1d,GAASgU,EAAGoJ,YAAYN,EAAS/f,QAAQ,UAAW,IAAKuZ,GAExEoH,EAAS,IAAM1d,GAASgU,EAAGoJ,YAAYN,GATzCY,EAAS,IAAM1d,GAASgU,EAa1B0J,EAAS,IAAM1d,GAAS2c,GAAc3I,EAAI8I,MAKzCY,EAGT,SAASC,GAAeC,GAEtB,IADA,IAAM7f,EAAM,GACHV,EAAI,EAAGA,EAAIugB,EAAIriB,OAAQ8B,IAAK,CACnC,IAAMwgB,EAAUD,EAAIvgB,GACpBU,EAAI8f,EAAQ,IAAMA,EAAQ,GAE5B,OAAO9f,EAGT,SAAS+f,GAAkB9J,EAAIsC,GAAoD,IAA7C9M,EAAO,UAAH,6CAAG,GAAIgU,EAAQ,UAAH,6CAAG,GAAIO,EAAQ,uCAAE7N,EAAU,uCAC3E8N,GAAkB,EAGhBP,EAAWzf,EAAcsY,EAAM1B,SACjC0B,EAAM1B,OAAO6I,UACb,CAACnH,EAAM1B,QAEX,GAAImJ,IACFC,EAAkB1H,EAAM2H,eACtB3H,EAAM2H,cAAc5I,SACoB,OAAxCiB,EAAM2H,cAAc5I,QAAQ2C,SACzBxO,EAAKjO,QACR,OAAIyiB,EACK,CAAC1H,GAEHmH,EAIX,IAAMC,EAAWH,GAAkBvJ,EAAIwJ,EAAOlH,EAAOmH,GAE/CS,EAAM,GAuBZ,OAtBA1U,EAAKxK,SAAQ,SAAAmf,GACC,WAARA,EACiB,gBAAfjO,GAAiC6N,EAG/BA,IAAaC,EACfE,EAAIre,KAAK4d,EAAS,IAElBS,EAAIre,KAAKyW,GALX4H,EAAIre,KAAKyW,EAAMpd,OAAOkK,OASpB3D,MAAMC,QAAQye,IAAmB,MAAXA,EAAI,GAC5BD,EAAIre,KAAK8d,GAAcQ,IACC,kBAARA,GAAoBjgB,EAAOwf,EAAUS,GACrDD,EAAIre,KAAK6d,EAASS,IAElBD,EAAIre,KAAKse,MAKRD,EAGT,IAAME,GAAO,IACPC,GAAS,IAEf,SAASC,GAAkBC,EAAWC,GACpC,OAAQD,IAAcC,GAEN,iBAAZA,IAEgB,UAAdD,GACc,QAAdA,GAKR,SAASE,GAAczK,GACrB,IAAI0K,EAAU1K,EAAG0K,QAEjB,MAAOA,GAAWA,EAAQA,UAAYA,EAAQC,SAASC,SAAWF,EAAQA,QAAQC,SAASC,SAAWF,EAAQrK,OAAO8F,UACnHuE,EAAUA,EAAQA,QAEpB,OAAOA,GAAWA,EAAQA,QAG5B,SAASG,GAAavI,GAAO,WAC3BA,EAAQgG,GAAUhG,GAGlB,IAAMjB,GAAWiB,EAAM2H,eAAiB3H,EAAMpd,QAAQmc,QACtD,IAAKA,EACH,OAAO5E,QAAQC,KAAK,WAEtB,IAAMoO,EAAYzJ,EAAQyJ,WAAazJ,EAAQ,cAC/C,IAAKyJ,EACH,OAAOrO,QAAQC,KAAK,WAItB,IAAM6N,EAAYjI,EAAMvD,KAElBmL,EAAM,GA+DZ,OA7DAY,EAAU9f,SAAQ,SAAA+f,GAChB,IAAIhM,EAAOgM,EAAS,GACdC,EAAcD,EAAS,GAEvBhB,EAAWhL,EAAKzV,OAAO,KAAO+gB,GACpCtL,EAAOgL,EAAWhL,EAAK/X,MAAM,GAAK+X,EAClC,IAAMkM,EAASlM,EAAKzV,OAAO,KAAO8gB,GAClCrL,EAAOkM,EAASlM,EAAK/X,MAAM,GAAK+X,EAE5BiM,GAAeV,GAAiBC,EAAWxL,IAC7CiM,EAAYhgB,SAAQ,SAAAkgB,GAClB,IAAMhP,EAAagP,EAAW,GAC9B,GAAIhP,EAAY,CACd,IAAIiP,EAAa,EAAKpX,IAItB,GAHIoX,EAAWR,SAASC,UACtBO,EAAaV,GAAaU,IAAeA,GAExB,UAAfjP,EAUF,YATAiP,EAAWhN,MAAMnB,MAAMmO,EACrBrB,GACE,EAAK/V,IACLuO,EACA4I,EAAW,GACXA,EAAW,GACXnB,EACA7N,IAIN,IAAMkP,EAAUD,EAAWjP,GAC3B,IAAKvS,EAAKyhB,GAAU,CAClB,IAAMrM,EAA2B,SAApB,EAAKhL,IAAI4Q,OAAoB,OAAS,YAC7C0G,EAAO,EAAK3K,OAAS,EAAK4K,GAChC,MAAM,IAAIvjB,MAAM,GAAD,OAAIgX,EAAI,aAAKsM,EAAI,qCAA6BnP,EAAU,MAEzE,GAAI+O,EAAQ,CACV,GAAIG,EAAQG,KACV,OAEFH,EAAQG,MAAO,EAEjB,IAAI7e,EAASod,GACX,EAAK/V,IACLuO,EACA4I,EAAW,GACXA,EAAW,GACXnB,EACA7N,GAEFxP,EAASjB,MAAMC,QAAQgB,GAAUA,EAAS,GAEtC,4DAA4D1D,KAAKoiB,EAAQrkB,cAE3E2F,EAASA,EAAOlB,OAAO,CAAC,CAAC,CAAF,QAAqB8W,KAE9C4H,EAAIre,KAAKuf,EAAQpO,MAAMmO,EAAYze,WAO3B,UAAd6d,GACe,IAAfL,EAAI3iB,QACc,qBAAX2iB,EAAI,GAEJA,EAAI,QALb,EASF,IAAMsB,GAAgB,GAEtB,SAASC,GAAiBC,GACxB,IAAMC,EAAeH,GAAcE,GAEnC,cADOF,GAAcE,GACdC,EAGT,IAAM/f,GAAQ,CACZ,SACA,SACA,UACA,iBACA,gBACA,wBAGF,SAASggB,KACPxjB,UAAIC,UAAUwjB,sBAAwB,WAGlC,OAAOzmB,KAAKib,OAAOwL,yBAGvB,IAAMC,EAAW1jB,UAAIC,UAAU+c,YAC/Bhd,UAAIC,UAAU+c,YAAc,SAAUrZ,EAAMyJ,GAK1C,MAJa,WAATzJ,GAAqByJ,GAAQA,EAAKuW,SACpC3mB,KAAK4mB,iBAAmBP,GAAgBjW,EAAKuW,eACtCvW,EAAKuW,QAEPD,EAAS7hB,KAAK7E,KAAM2G,EAAMyJ,IAIrC,SAASyW,KACP,IAAMC,EAAS,GACTC,EAAU,GAEhB,SAASC,EAAWxiB,GAClB,IAAMsc,EAAS9gB,KAAKulB,SAAS0B,UAAUvE,MACvC,GAAI5B,EAAQ,CACV,IAAM4B,EAAQ5B,EAAOvf,MAAM,KAAK,GAChCiD,EAAGke,IAIP1f,UAAIC,UAAUikB,QAAU,SAAUxE,GAChC,IAAMyE,EAAOL,EAAOpE,GAOpB,OANKyE,IACHJ,EAAQrE,GAAS1iB,KACjBA,KAAK4Y,IAAI,kBAAkB,kBAClBmO,EAAQrE,OAGZyE,GAGTnkB,UAAIC,UAAUmkB,QAAU,SAAU1E,EAAOza,EAAMof,GAC7C,IAAMF,EAAOL,EAAOpE,GACpB,GAAIyE,EAAM,CACR,IAAM7f,EAAS6f,EAAKlf,IAAS,GAC7B,OAAIof,EACK/f,EAEFA,EAAO,KAIlBtE,UAAIC,UAAUqkB,QAAU,SAAUrf,EAAM+B,GACtC,IAAIpD,EAAQ,EAOZ,OANAogB,EAAUniB,KAAK7E,MAAM,SAAA0iB,GACnB,IAAMyE,EAAOL,EAAOpE,GACdpb,EAAS6f,EAAKlf,GAAQkf,EAAKlf,IAAS,GAC1CX,EAAOb,KAAKuD,GACZpD,EAAQU,EAAOnF,OAAS,KAEnByE,GAGT5D,UAAIC,UAAUskB,SAAW,WACvBP,EAAUniB,KAAK7E,MAAM,SAAA0iB,GACnBoE,EAAOpE,GAAS,OAIpB1f,UAAIC,UAAUukB,SAAW,WACvBR,EAAUniB,KAAK7E,MAAM,SAAA0iB,GACfqE,EAAQrE,IACVqE,EAAQrE,GAAO7V,mBAKrB7J,UAAIyJ,MAAM,CACRgb,UAAS,WACP,IAAMR,EAAYjnB,KAAKulB,SAAS0B,UAC1BvE,EAAQuE,GAAaA,EAAUvE,MACjCA,WACKoE,EAAOpE,UACPqE,EAAQrE,OAMvB,SAASgF,GAAc9M,EAAI,GAGxB,IAFDF,EAAK,EAALA,MACAqC,EAAQ,EAARA,SAEAyJ,KAEEK,KAEEjM,EAAG2K,SAASoC,QACd3kB,UAAIC,UAAU2kB,OAAShN,EAAG2K,SAASoC,OAErC5kB,EAAWC,WAEXA,UAAIC,UAAU4kB,OAAS,YAEvB7kB,UAAIyJ,MAAM,CACRC,aAAY,WACV,GAAK1M,KAAKulB,SAAShG,OAAnB,CAeA,GAXAvf,KAAKuf,OAASvf,KAAKulB,SAAShG,OAE5Bvf,KAAKsf,KAAM,EAAH,YACN/X,KAAM,IACLvH,KAAKuf,OAASvf,KAAKulB,SAAS5J,YAG/B3b,KAAKib,OAASjb,KAAKulB,SAAS5J,kBAErB3b,KAAKulB,SAAShG,cACdvf,KAAKulB,SAAS5J,WAED,SAAhB3b,KAAKuf,QACW,oBAAX/Q,OACP,CACA,IAAMC,EAAMD,SACRC,EAAIE,KAAOF,EAAIE,IAAImZ,QACrB9nB,KAAK+nB,MAAQtZ,EAAIE,IAAImZ,OAGL,QAAhB9nB,KAAKuf,SACPxC,EAAS/c,MACTqf,GAAUrf,KAAM0a,QAKtB,IAAMsN,EAAa,CACjBC,SAAQ,SAAE7X,GACJpQ,KAAK2O,MAIH3M,EAAGpB,UAAYoB,EAAGpB,QAAQ,aAC5ByW,QAAQ3U,MAAM,uDAIlB1C,KAAK2O,IAAMiM,EAEX5a,KAAK2O,IAAI2Q,IAAM,CACb7Q,IAAKzO,MAGPA,KAAK2O,IAAIsM,OAASjb,KAElBA,KAAK2O,IAAIuZ,WAAaloB,KAAKkoB,WAE3BloB,KAAK2O,IAAIwZ,YAAa,EACtBnoB,KAAK2O,IAAIqR,YAAY,UAAW5P,GAEhCpQ,KAAK2O,IAAIqR,YAAY,WAAY5P,MAKrC4X,EAAWE,WAAatN,EAAG2K,SAAS2C,YAAc,GAElD,IAAMnb,EAAU6N,EAAG2K,SAASxY,QAY5B,OAXIA,GACF1I,OAAOqB,KAAKqH,GAASnH,SAAQ,SAAAqC,GAC3B+f,EAAW/f,GAAQ8E,EAAQ9E,MAI/BmF,GAAcpK,UAAK4X,EAAIwN,MAEvBtI,GAAUkI,EAAYxhB,IACtByZ,GAAiB+H,EAAYpN,EAAG2K,UAEzByC,EAGT,SAASI,KACP,IAAIzc,EAAiB,GAEbC,EAAc5J,EAAGnB,iBACjBgL,EACJD,GAAeA,EAAYC,SAAWD,EAAYC,SAAWP,GAGjE,OAFEK,EAAiBG,GAAgBD,IAAaP,GAEzCK,EAGT,SAAS0c,GAAUzN,GACjB,OAAO8M,GAAa9M,EAAI,CACtBF,SACAqC,cAIJ,SAASuL,GAAW1N,GAElB,OADA2N,IAAIF,GAASzN,IACNA,EAGT,IAAM4N,GAAkB,WAClBC,GAAwB,SAAAhnB,GAAC,MAAI,IAAMA,EAAEC,WAAW,GAAGC,SAAS,KAC5D+mB,GAAU,OAKVC,GAAS,SAAAtnB,GAAG,OAAIunB,mBAAmBvnB,GACtCsC,QAAQ6kB,GAAiBC,IACzB9kB,QAAQ+kB,GAAS,MAEpB,SAASG,GAAgBlkB,GAAyB,IAApBmkB,EAAY,UAAH,6CAAGH,GAClCxiB,EAAMxB,EAAMN,OAAOqB,KAAKf,GAAKnD,KAAI,SAAAvB,GACrC,IAAM8oB,EAAMpkB,EAAI1E,GAEhB,QAAYyU,IAARqU,EACF,MAAO,GAGT,GAAY,OAARA,EACF,OAAOD,EAAU7oB,GAGnB,GAAIoG,MAAMC,QAAQyiB,GAAM,CACtB,IAAM/kB,EAAS,GAWf,OAVA+kB,EAAInjB,SAAQ,SAAAojB,QACGtU,IAATsU,IAGS,OAATA,EACFhlB,EAAOyC,KAAKqiB,EAAU7oB,IAEtB+D,EAAOyC,KAAKqiB,EAAU7oB,GAAO,IAAM6oB,EAAUE,QAG1ChlB,EAAO9D,KAAK,KAGrB,OAAO4oB,EAAU7oB,GAAO,IAAM6oB,EAAUC,MACvCnY,QAAO,SAAAqY,GAAC,OAAIA,EAAE9mB,OAAS,KAAGjC,KAAK,KAAO,KACzC,OAAOiG,EAAM,IAAH,OAAOA,GAAQ,GAG3B,SAAS+iB,GAAoBC,GAGL,6DAApB,GAFF9N,EAAM,EAANA,OACAE,EAAY,EAAZA,aACM6N,EAAc,uCACpB,EAAmC/I,GAAiBrd,UAAKmmB,GAAoB,qBAAtE7I,EAAY,KAAEnD,EAAU,KAEzBnV,EAAU,EAAH,CACXqhB,eAAe,EAEfC,gBAAgB,GACZnM,EAAWnV,SAAW,IAKtBmV,EAAW,cAAgBA,EAAW,aAAanV,SACrD3D,OAAOwF,OAAO7B,EAASmV,EAAW,aAAanV,SAInD,IAAMuhB,EAAmB,CACvBvhB,UACAT,KAAMyZ,GAAS7D,EAAYna,UAAIC,WAC/B2e,UAAWF,GAAcvE,EAAYhC,IACrCgH,WAAYC,GAAejF,EAAW8E,OAAO,EAAO9E,EAAWqM,OAAQxhB,GACvEyhB,UAAW,CACTC,SAAQ,WACN,IAAMvH,EAAaniB,KAAKmiB,WAElBna,EAAU,CACduX,OAAQlE,EAAOxW,KAAK7E,MAAQ,OAAS,YACrC2b,WAAY3b,KACZinB,UAAW9E,GAGbtB,GAAWsB,EAAWO,MAAO1iB,MAG7Bub,EAAa1W,KAAK7E,KAAM,CACtB6a,OAAQ7a,KAAK+gB,SACb5D,WAAYnV,IAIdhI,KAAK2O,IAAM,IAAI2R,EAAatY,GAG5BwY,GAAUxgB,KAAK2O,IAAKwT,EAAW1B,UAG/BzgB,KAAK2O,IAAIgb,UAEXC,MAAK,WAGC5pB,KAAK2O,MACP3O,KAAK2O,IAAIwZ,YAAa,EACtBnoB,KAAK2O,IAAIqR,YAAY,WACrBhgB,KAAK2O,IAAIqR,YAAY,aAGzB6J,SAAQ,WACN7pB,KAAK2O,KAAO3O,KAAK2O,IAAImb,aAGzBC,cAAe,CACbC,KAAI,SAAE5Z,GACJpQ,KAAK2O,KAAO3O,KAAK2O,IAAIqR,YAAY,aAAc5P,IAEjD6Z,KAAI,WACFjqB,KAAK2O,KAAO3O,KAAK2O,IAAIqR,YAAY,eAEnCkK,OAAM,SAAEC,GACNnqB,KAAK2O,KAAO3O,KAAK2O,IAAIqR,YAAY,eAAgBmK,KAGrDpd,QAAS,CACPqd,IAAKnN,GACLoN,IAAK5E,KAgBT,OAZItI,EAAWmN,kBACbf,EAAiBe,gBAAkBnN,EAAWmN,iBAG5CjkB,MAAMC,QAAQ6W,EAAWoN,iBAC3BpN,EAAWoN,eAAe3kB,SAAQ,SAAA4kB,GAChCjB,EAAiBxc,QAAQyd,GAAc,SAAUpa,GAC/C,OAAOpQ,KAAK2O,IAAI6b,GAAYpa,OAK9BgZ,EACK,CAACG,EAAkBpM,EAAYmD,GAEpCjF,EACKkO,EAEF,CAACA,EAAkBjJ,GAG5B,SAASmK,GAAgBtB,EAAqBC,GAC5C,OAAOF,GAAmBC,EAAqB,CAC7C9N,UACAE,iBACC6N,GAGL,IAAMsB,GAAU,CACd,SACA,SACA,YAKF,SAASC,GAAeC,GACtB,MAAkCH,GAAeG,GAAgB,GAAK,qBAA/DC,EAAW,KAAE1N,EAAU,KAqB9B,OAnBA2C,GAAU+K,EAAY9d,QAAS2d,GAASvN,GAExC0N,EAAY9d,QAAQ+d,OAAS,SAAUC,GACrC/qB,KAAKgI,QAAU+iB,EACf,IAAMC,EAAY3mB,OAAOwF,OAAO,GAAIkhB,UAC7BC,EAAUrE,OACjB3mB,KAAK6P,MAAQ,CACXC,SAAU,KAAO9P,KAAKsb,OAAStb,KAAKkmB,IAAM2C,GAAemC,IAE3DhrB,KAAK2O,IAAI2Q,IAAIyL,MAAQA,EACrB/qB,KAAK2O,IAAIqR,YAAY,SAAU+K,IAG/B9K,GAAiB4K,EAAY9d,QAAS6d,EAAgB,CAAC,YAGvD/M,GAAmBgN,EAAY9d,QAASoQ,EAAWpQ,SAG9C8d,EAGT,SAASI,GAAWL,GAClB,OAAOD,GAAcC,GAGvB,SAASM,GAAYN,GAEjB,OAAOtM,UAAU2M,GAAUL,IAI/B,SAASO,GAAiBhO,GAEtB,OAAOmB,UAAUmM,GAAetN,IAIpC,SAASiO,GAAqBxQ,GAC5B,IAAMoN,EAAaK,GAASzN,GACtBnM,EAAMD,OAAO,CACjBE,cAAc,IAEhBkM,EAAGK,OAASxM,EACZ,IAAMyZ,EAAazZ,EAAIyZ,WAuBvB,GAtBIA,GACF7jB,OAAOqB,KAAKsiB,EAAWE,YAAYtiB,SAAQ,SAAAqC,GACpCnD,EAAOojB,EAAYjgB,KACtBigB,EAAWjgB,GAAQ+f,EAAWE,WAAWjgB,OAI/C5D,OAAOqB,KAAKsiB,GAAYpiB,SAAQ,SAAAqC,GACzBnD,EAAO2J,EAAKxG,KACfwG,EAAIxG,GAAQ+f,EAAW/f,OAGvB1D,EAAKyjB,EAAWqD,SAAWrpB,EAAGspB,WAChCtpB,EAAGspB,WAAU,WAAa,2BAATlb,EAAI,yBAAJA,EAAI,gBACnBwK,EAAGoF,YAAY,SAAU5P,MAGzB7L,EAAKyjB,EAAWuD,SAAWvpB,EAAGwpB,WAChCxpB,EAAGwpB,WAAU,WAAa,2BAATpb,EAAI,yBAAJA,EAAI,gBACnBwK,EAAGoF,YAAY,SAAU5P,MAGzB7L,EAAKyjB,EAAWC,UAAW,CAC7B,IAAM7X,EAAOpO,EAAG3B,sBAAwB2B,EAAG3B,uBAC3Cua,EAAGoF,YAAY,WAAY5P,GAE7B,OAAOwK,EAGT,SAAS6Q,GAAc7Q,GACrB,IAAMoN,EAAaK,GAASzN,GAW5B,GAVIrW,EAAKyjB,EAAWqD,SAAWrpB,EAAGspB,WAChCtpB,EAAGspB,WAAU,WAAa,2BAATlb,EAAI,yBAAJA,EAAI,gBACnBwK,EAAGoF,YAAY,SAAU5P,MAGzB7L,EAAKyjB,EAAWuD,SAAWvpB,EAAGwpB,WAChCxpB,EAAGwpB,WAAU,WAAa,2BAATpb,EAAI,yBAAJA,EAAI,gBACnBwK,EAAGoF,YAAY,SAAU5P,MAGzB7L,EAAKyjB,EAAWC,UAAW,CAC7B,IAAM7X,EAAOpO,EAAG3B,sBAAwB2B,EAAG3B,uBAC3Cua,EAAGoF,YAAY,WAAY5P,GAE7B,OAAOwK,EA/FT8P,GAAQjkB,KAAI,MAAZikB,GAAgBtL,IAkGhB1I,GAAM9Q,SAAQ,SAAA8lB,GACZjV,GAAUiV,IAAW,KAGvB/U,GAAS/Q,SAAQ,SAAA+lB,GACf,IAAMC,EAAUnV,GAAUkV,IAAelV,GAAUkV,GAAY1jB,KAAOwO,GAAUkV,GAAY1jB,KACxF0jB,EACC3pB,EAAGpB,QAAQgrB,KACdnV,GAAUkV,IAAc,MAI5B,IAAIE,GAAM,GAEW,qBAAVC,MACTD,GAAM,IAAIC,MAAM,GAAI,CAClBne,IAAG,SAAE7N,EAAQmI,GACX,OAAInD,EAAOhF,EAAQmI,GACVnI,EAAOmI,GAEZkH,GAAQlH,GACHkH,GAAQlH,GAEbQ,GAAIR,GACCwB,GAAUxB,EAAMQ,GAAIR,IAGvBuQ,GAASvQ,GACJwB,GAAUxB,EAAMuQ,GAASvQ,IAE9B4P,GAAS5P,GACJwB,GAAUxB,EAAM4P,GAAS5P,IAGhCkR,GAASlR,GACJkR,GAASlR,GAEXwB,GAAUxB,EAAMuP,GAAQvP,EAAMjG,EAAGiG,MAE1C2F,IAAG,SAAE9N,EAAQmI,EAAM+B,GAEjB,OADAlK,EAAOmI,GAAQ+B,GACR,MAIX3F,OAAOqB,KAAKyJ,IAASvJ,SAAQ,SAAAqC,GAC3B4jB,GAAI5jB,GAAQkH,GAAQlH,MAIpB5D,OAAOqB,KAAKmS,IAAUjS,SAAQ,SAAAqC,GAC5B4jB,GAAI5jB,GAAQwB,GAAUxB,EAAM4P,GAAS5P,OAEvC5D,OAAOqB,KAAK8S,IAAU5S,SAAQ,SAAAqC,GAC5B4jB,GAAI5jB,GAAQwB,GAAUxB,EAAMuQ,GAASvQ,OAIzC5D,OAAOqB,KAAKyT,IAAUvT,SAAQ,SAAAqC,GAC5B4jB,GAAI5jB,GAAQkR,GAASlR,MAGvB5D,OAAOqB,KAAK+C,IAAK7C,SAAQ,SAAAqC,GACvB4jB,GAAI5jB,GAAQwB,GAAUxB,EAAMQ,GAAIR,OAGlC5D,OAAOqB,KAAK1D,GAAI4D,SAAQ,SAAAqC,IAClBnD,EAAO9C,EAAIiG,IAASnD,EAAO2R,GAAWxO,MACxC4jB,GAAI5jB,GAAQwB,GAAUxB,EAAMuP,GAAQvP,EAAMjG,EAAGiG,UAKnDjG,EAAGsmB,UAAYA,GACftmB,EAAGkpB,WAAaA,GAChBlpB,EAAGmpB,gBAAkBA,GACrBnpB,EAAGopB,oBAAsBA,GACzBppB,EAAGypB,aAAeA,GAElB,IAAIM,GAAQF,GAAI,GAEDE,GAAK,e,+CCjqFpB,IAAIC,EAGJA,EAAI,WACH,OAAOhsB,KADJ,GAIJ,IAECgsB,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACR,MAAO5S,GAEc,kBAAX6S,SAAqBF,EAAIE,QAOrCC,EAAOC,QAAUJ,G,cCnBjB,SAASK,EAAuB1nB,GAC9B,OAAOA,GAAOA,EAAI2nB,WAAa3nB,EAAM,CACnC,QAAWA,GAGfwnB,EAAOC,QAAUC,EAAwBF,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCL9G,IAAIG,EAAiBC,EAAQ,GACzBC,EAAuBD,EAAQ,GAC/BE,EAA6BF,EAAQ,GACrCG,EAAkBH,EAAQ,IAC9B,SAASI,EAAepI,EAAKvgB,GAC3B,OAAOsoB,EAAe/H,IAAQiI,EAAqBjI,EAAKvgB,IAAMyoB,EAA2BlI,EAAKvgB,IAAM0oB,IAEtGR,EAAOC,QAAUQ,EAAgBT,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCPtG,SAASS,EAAgBrI,GACvB,GAAIne,MAAMC,QAAQke,GAAM,OAAOA,EAEjC2H,EAAOC,QAAUS,EAAiBV,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCHvG,SAASU,EAAsBC,EAAGC,GAChC,IAAIxgB,EAAI,MAAQugB,EAAI,KAAO,oBAAsBE,QAAUF,EAAEE,OAAOC,WAAaH,EAAE,cACnF,GAAI,MAAQvgB,EAAG,CACb,IAAI6M,EACF8T,EACAlpB,EACAmpB,EACAC,EAAI,GACJC,GAAI,EACJC,GAAI,EACN,IACE,GAAItpB,GAAKuI,EAAIA,EAAE3H,KAAKkoB,IAAIS,KAAM,IAAMR,EAAG,CACrC,GAAI3oB,OAAOmI,KAAOA,EAAG,OACrB8gB,GAAI,OACC,OAASA,GAAKjU,EAAIpV,EAAEY,KAAK2H,IAAIihB,QAAUJ,EAAE5mB,KAAK4S,EAAErP,OAAQqjB,EAAElrB,SAAW6qB,GAAIM,GAAI,IACpF,MAAOP,GACPQ,GAAI,EAAIJ,EAAIJ,EACZ,QACA,IACE,IAAKO,GAAK,MAAQ9gB,EAAE,YAAc4gB,EAAI5gB,EAAE,YAAanI,OAAO+oB,KAAOA,GAAI,OACvE,QACA,GAAIG,EAAG,MAAMJ,GAGjB,OAAOE,GAGXlB,EAAOC,QAAUU,EAAuBX,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBC3B7G,IAAIsB,EAAmBlB,EAAQ,GAC/B,SAASmB,EAA4BJ,EAAGK,GACtC,GAAKL,EAAL,CACA,GAAiB,kBAANA,EAAgB,OAAOG,EAAiBH,EAAGK,GACtD,IAAIT,EAAI9oB,OAAOpB,UAAUtB,SAASkD,KAAK0oB,GAAG3rB,MAAM,GAAI,GAEpD,MADU,WAANurB,GAAkBI,EAAExjB,cAAaojB,EAAII,EAAExjB,YAAY9B,MAC7C,QAANklB,GAAqB,QAANA,EAAoB9mB,MAAMwnB,KAAKN,GACxC,cAANJ,GAAqB,2CAA2CvpB,KAAKupB,GAAWO,EAAiBH,EAAGK,QAAxG,GAEFzB,EAAOC,QAAUuB,EAA6BxB,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCTnH,SAAS0B,EAAkBtJ,EAAK7U,IACnB,MAAPA,GAAeA,EAAM6U,EAAIriB,UAAQwN,EAAM6U,EAAIriB,QAC/C,IAAK,IAAI8B,EAAI,EAAG8pB,EAAO,IAAI1nB,MAAMsJ,GAAM1L,EAAI0L,EAAK1L,IAAK8pB,EAAK9pB,GAAKugB,EAAIvgB,GACnE,OAAO8pB,EAET5B,EAAOC,QAAU0B,EAAmB3B,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCLzG,SAAS4B,IACP,MAAM,IAAIC,UAAU,6IAEtB9B,EAAOC,QAAU4B,EAAkB7B,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCHxG,IAAI8B,EAAgB1B,EAAQ,IAC5B,SAAS2B,EAAgBxpB,EAAK1E,EAAK+J,GAYjC,OAXA/J,EAAMiuB,EAAcjuB,GAChBA,KAAO0E,EACTN,OAAOqJ,eAAe/I,EAAK1E,EAAK,CAC9B+J,MAAOA,EACPwT,YAAY,EACZD,cAAc,EACd6Q,UAAU,IAGZzpB,EAAI1E,GAAO+J,EAENrF,EAETwnB,EAAOC,QAAU+B,EAAiBhC,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCfvG,IAAIiC,EAAU7B,EAAQ,IAAe,WACjC8B,EAAc9B,EAAQ,IAC1B,SAAS0B,EAAc1hB,GACrB,IAAIvI,EAAIqqB,EAAY9hB,EAAG,UACvB,MAAO,UAAY6hB,EAAQpqB,GAAKA,EAAIA,EAAI,GAE1CkoB,EAAOC,QAAU8B,EAAe/B,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCNrG,SAASiC,EAAQd,GAGf,OAAQpB,EAAOC,QAAUiC,EAAU,mBAAqBpB,QAAU,iBAAmBA,OAAOC,SAAW,SAAUK,GAC/G,cAAcA,GACZ,SAAUA,GACZ,OAAOA,GAAK,mBAAqBN,QAAUM,EAAExjB,cAAgBkjB,QAAUM,IAAMN,OAAOhqB,UAAY,gBAAkBsqB,GACjHpB,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,QAAUiC,EAAQd,GAE5FpB,EAAOC,QAAUiC,EAASlC,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCT/F,IAAIiC,EAAU7B,EAAQ,IAAe,WACrC,SAAS8B,EAAY9hB,EAAGugB,GACtB,GAAI,UAAYsB,EAAQ7hB,KAAOA,EAAG,OAAOA,EACzC,IAAI6M,EAAI7M,EAAEygB,OAAOqB,aACjB,QAAI,IAAWjV,EAAG,CAChB,IAAIpV,EAAIoV,EAAExU,KAAK2H,EAAGugB,GAAK,WACvB,GAAI,UAAYsB,EAAQpqB,GAAI,OAAOA,EACnC,MAAM,IAAIgqB,UAAU,gDAEtB,OAAQ,WAAalB,EAAIrpB,OAASsH,QAAQwB,GAE5C2f,EAAOC,QAAUkC,EAAanC,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCXnG,IAAImC,EAAiB/B,EAAQ,IACzBgC,EAA2BhC,EAAQ,IACvC,SAASiC,EAAWjiB,EAAG6M,EAAG0T,GACxB,GAAIyB,IAA4B,OAAOE,QAAQC,UAAU/W,MAAM,KAAMiB,WACrE,IAAI0U,EAAI,CAAC,MACTA,EAAE9mB,KAAKmR,MAAM2V,EAAGlU,GAChB,IAAIuV,EAAI,IAAKpiB,EAAEqiB,KAAKjX,MAAMpL,EAAG+gB,IAC7B,OAAOR,GAAKwB,EAAeK,EAAG7B,EAAE9pB,WAAY2rB,EAE9CzC,EAAOC,QAAUqC,EAAYtC,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCTlG,SAAS0C,EAAgBvB,EAAGqB,GAK1B,OAJAzC,EAAOC,QAAU0C,EAAkBzqB,OAAOkqB,eAAiBlqB,OAAOkqB,eAAeM,OAAS,SAAyBtB,EAAGqB,GAEpH,OADArB,EAAEle,UAAYuf,EACPrB,GACNpB,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,QACjE0C,EAAgBvB,EAAGqB,GAE5BzC,EAAOC,QAAU0C,EAAiB3C,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCPvG,SAAS2C,IACP,IACE,IAAIviB,GAAK8U,QAAQre,UAAU+rB,QAAQnqB,KAAK6pB,QAAQC,UAAUrN,QAAS,IAAI,gBACvE,MAAO9U,IACT,OAAQ2f,EAAOC,QAAU2C,EAA4B,WACnD,QAASviB,GACR2f,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,WAE1ED,EAAOC,QAAU2C,EAA2B5C,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCRjH,IAAI6C,EAAoBzC,EAAQ,IAC5B0C,EAAkB1C,EAAQ,IAC1BE,EAA6BF,EAAQ,GACrC2C,EAAoB3C,EAAQ,IAChC,SAAS4C,EAAmB5K,GAC1B,OAAOyK,EAAkBzK,IAAQ0K,EAAgB1K,IAAQkI,EAA2BlI,IAAQ2K,IAE9FhD,EAAOC,QAAUgD,EAAoBjD,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCP1G,IAAIsB,EAAmBlB,EAAQ,GAC/B,SAAS6C,EAAmB7K,GAC1B,GAAIne,MAAMC,QAAQke,GAAM,OAAOkJ,EAAiBlJ,GAElD2H,EAAOC,QAAUiD,EAAoBlD,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCJ1G,SAASkD,EAAiBC,GACxB,GAAsB,qBAAXtC,QAAmD,MAAzBsC,EAAKtC,OAAOC,WAA2C,MAAtBqC,EAAK,cAAuB,OAAOlpB,MAAMwnB,KAAK0B,GAEtHpD,EAAOC,QAAUkD,EAAkBnD,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,cCHxG,SAASoD,IACP,MAAM,IAAIvB,UAAU,wIAEtB9B,EAAOC,QAAUoD,EAAoBrD,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,sYCHpG1nB,EAAW,SAACqkB,GAAG,OAAa,OAARA,GAA+B,YAAf,aAAOA,IAC3C0G,EAAoB,CAAC,IAAK,KAC1BC,EAAa,WACf,cAAc,qBACV1vB,KAAK2vB,QAAUtrB,OAAOa,OAAO,MAYhC,OAXA,yCACD,SAAYtC,EAASqK,GAAwC,IAAhC2iB,EAAa,UAAH,6CAAGH,EACtC,IAAKxiB,EACD,MAAO,CAACrK,GAEZ,IAAIitB,EAAS7vB,KAAK2vB,QAAQ/sB,GAK1B,OAJKitB,IACDA,EAASptB,EAAMG,EAASgtB,GACxB5vB,KAAK2vB,QAAQ/sB,GAAWitB,GAErBC,EAAQD,EAAQ5iB,OAC1B,EAdc,GAcd,cAEL,IAAM8iB,EAAsB,WACtBC,EAAuB,WAC7B,SAASvtB,EAAMwtB,EAAQ,GAAgC,yBAA/BC,EAAc,KAAEC,EAAY,KAC1CN,EAAS,GACXO,EAAW,EACXC,EAAO,GACX,MAAOD,EAAWH,EAAO9tB,OAAQ,CAC7B,IAAImuB,EAAOL,EAAOG,KAClB,GAAIE,IAASJ,EAAgB,CACrBG,GACAR,EAAOppB,KAAK,CAAEkT,KAAM,OAAQ3P,MAAOqmB,IAEvCA,EAAO,GACP,IAAIE,EAAM,GACVD,EAAOL,EAAOG,KACd,WAAgB1b,IAAT4b,GAAsBA,IAASH,EAClCI,GAAOD,EACPA,EAAOL,EAAOG,KAElB,IAAMI,EAAWF,IAASH,EACpBxW,EAAOoW,EAAoBnsB,KAAK2sB,GAChC,OACAC,GAAYR,EAAqBpsB,KAAK2sB,GAClC,QACA,UACVV,EAAOppB,KAAK,CAAEuD,MAAOumB,EAAK5W,cAS1B0W,GAAQC,EAIhB,OADAD,GAAQR,EAAOppB,KAAK,CAAEkT,KAAM,OAAQ3P,MAAOqmB,IACpCR,EAEX,SAASC,EAAQD,EAAQ5iB,GACrB,IAAMwjB,EAAW,GACb7pB,EAAQ,EACN8pB,EAAOrqB,MAAMC,QAAQ2G,GACrB,OACAvI,EAASuI,GACL,QACA,UACV,GAAa,YAATyjB,EACA,OAAOD,EAEX,MAAO7pB,EAAQipB,EAAO1tB,OAAQ,CAC1B,IAAMJ,EAAQ8tB,EAAOjpB,GACrB,OAAQ7E,EAAM4X,MACV,IAAK,OACD8W,EAAShqB,KAAK1E,EAAMiI,OACpB,MACJ,IAAK,OACDymB,EAAShqB,KAAKwG,EAAOuD,SAASzO,EAAMiI,MAAO,MAC3C,MACJ,IAAK,QACY,UAAT0mB,EACAD,EAAShqB,KAAKwG,EAAOlL,EAAMiI,QAIvBqN,QAAQC,KAAK,kBAAD,OAAmBvV,EAAM4X,KAAI,kCAA0B+W,EAAI,mBAG/E,MACJ,IAAK,UAEGrZ,QAAQC,KAAK,mCAEjB,MAER1Q,IAEJ,OAAO6pB,EAGX,IAAMrlB,EAAiB,UAAU,mBACjC,IAAMC,EAAiB,UAAU,mBACjC,IAAMC,EAAY,KAAK,cACvB,IAAMC,EAAY,KAAK,cACvB,IAAMC,EAAY,KAAK,cACvB,IAAMlH,EAAiBD,OAAOpB,UAAUqB,eAClCQ,EAAS,SAACikB,EAAK9oB,GAAG,OAAKqE,EAAeO,KAAKkkB,EAAK9oB,IAChD0wB,EAAmB,IAAIjB,EAC7B,SAAS3hB,EAAQ1M,EAAK2M,GAClB,QAASA,EAAMC,MAAK,SAACC,GAAI,OAA4B,IAAvB7M,EAAIZ,QAAQyN,MAE9C,SAASC,EAAW9M,EAAK2M,GACrB,OAAOA,EAAMC,MAAK,SAACC,GAAI,OAA2B,IAAtB7M,EAAIZ,QAAQyN,MAE5C,SAASpC,EAAgBX,EAAQM,GAC7B,GAAKN,EAAL,CAIA,GADAA,EAASA,EAAOiD,OAAOzK,QAAQ,KAAM,KACjC8H,GAAYA,EAASN,GACrB,OAAOA,EAGX,GADAA,EAASA,EAAOkD,cACD,YAAXlD,EAEA,OAAOC,EAEX,GAA6B,IAAzBD,EAAO1K,QAAQ,MACf,OAAI0K,EAAO1K,QAAQ,UAAY,EACpB2K,EAEPD,EAAO1K,QAAQ,UAAY,GAG3BsN,EAAQ5C,EAAQ,CAAC,MAAO,MAAO,MAAO,SAF/BE,EAKJD,EAEX,IAAIe,EAAU,CAACb,EAAWC,EAAWC,GACjCC,GAAYpH,OAAOqB,KAAK+F,GAAUtJ,OAAS,IAC3CgK,EAAU9H,OAAOqB,KAAK+F,IAE1B,IAAM6C,EAAOH,EAAWhD,EAAQgB,GAChC,OAAImC,QAAJ,GAGH,IACKsiB,EAAI,WACN,cAAsE,IAAxDzlB,EAAM,EAANA,OAAQ0lB,EAAc,EAAdA,eAAgBplB,EAAQ,EAARA,SAAUqlB,EAAO,EAAPA,QAASC,EAAQ,EAARA,UAAQ,qBAC7D/wB,KAAKmL,OAASG,EACdtL,KAAK6wB,eAAiBvlB,EACtBtL,KAAK4C,QAAU,GACf5C,KAAKyL,SAAW,GAChBzL,KAAKgxB,SAAW,GACZH,IACA7wB,KAAK6wB,eAAiBA,GAE1B7wB,KAAK+wB,SAAWA,GAAYJ,EAC5B3wB,KAAKyL,SAAWA,GAAY,GAC5BzL,KAAKkN,UAAU/B,GAAUG,GACrBwlB,GACA9wB,KAAK4M,YAAYkkB,GA8DxB,OA5DA,uCACD,SAAU3lB,GAAQ,WACR2D,EAAY9O,KAAKmL,OACvBnL,KAAKmL,OAASW,EAAgBX,EAAQnL,KAAKyL,WAAazL,KAAK6wB,eACxD7wB,KAAKyL,SAASzL,KAAKmL,UAEpBnL,KAAKyL,SAASzL,KAAKmL,QAAU,IAEjCnL,KAAK4C,QAAU5C,KAAKyL,SAASzL,KAAKmL,QAE9B2D,IAAc9O,KAAKmL,QACnBnL,KAAKgxB,SAASprB,SAAQ,SAACkrB,GACnBA,EAAQ,EAAK3lB,OAAQ2D,QAGhC,uBACD,WACI,OAAO9O,KAAKmL,SACf,yBACD,SAAY3G,GAAI,WACNoC,EAAQ5G,KAAKgxB,SAASvqB,KAAKjC,GAAM,EACvC,OAAO,WACH,EAAKwsB,SAASnqB,OAAOD,EAAO,MAEnC,iBACD,SAAIuE,EAAQvI,GAA0B,IAAjBquB,IAAW,UAAH,+CACnB7kB,EAAcpM,KAAKyL,SAASN,GAC9BiB,EACI6kB,EACA5sB,OAAOwF,OAAOuC,EAAaxJ,GAG3ByB,OAAOqB,KAAK9C,GAASgD,SAAQ,SAAC3F,GACrB6E,EAAOsH,EAAanM,KACrBmM,EAAYnM,GAAO2C,EAAQ3C,OAMvCD,KAAKyL,SAASN,GAAUvI,IAE/B,eACD,SAAEA,EAASqK,EAAQ2iB,GACf,OAAO5vB,KAAK+wB,SAASG,YAAYtuB,EAASqK,EAAQ2iB,GAAY1vB,KAAK,MACtE,eACD,SAAED,EAAKkL,EAAQ8B,GACX,IAAIrK,EAAU5C,KAAK4C,QAQnB,MAPsB,kBAAXuI,GACPA,EAASW,EAAgBX,EAAQnL,KAAKyL,UACtCN,IAAWvI,EAAU5C,KAAKyL,SAASN,KAGnC8B,EAAS9B,EAERrG,EAAOlC,EAAS3C,GAIdD,KAAK+wB,SAASG,YAAYtuB,EAAQ3C,GAAMgN,GAAQ/M,KAAK,KAHxDmX,QAAQC,KAAK,yCAAD,OAA0CrX,EAAG,2CAClDA,OAGd,EA5EK,GA+EV,SAASkxB,EAAe9jB,EAAOf,GAEvBe,EAAMI,aAENJ,EAAMI,cAAa,SAAC2jB,GAChB9kB,EAAKY,UAAUkkB,MAInB/jB,EAAMgkB,QAAO,kBAAMhkB,EAAMuB,WAAS,SAACwiB,GAC/B9kB,EAAKY,UAAUkkB,MAI3B,SAASE,IACL,MAAmB,qBAARzF,GAAuBA,EAAI1e,UAC3B0e,EAAI1e,YAGO,qBAAX8B,GAA0BA,EAAO9B,UACjC8B,EAAO9B,YAEX7B,EAEX,SAASiB,EAAYpB,GAAgD,IAAxCM,EAAW,UAAH,6CAAG,GAAIolB,EAAc,uCAAEC,EAAO,uCAE/D,GAAsB,kBAAX3lB,EAAqB,OACP,CACjBM,EACAN,GAFHA,EAAM,KAAEM,EAAQ,KAKC,kBAAXN,IAEPA,EAASmmB,KAEiB,kBAAnBT,IACPA,EAC4B,qBAAhB3kB,aAA+BA,YAAY2kB,gBAC/CvlB,GAEZ,IAAMgB,EAAO,IAAIskB,EAAK,CAClBzlB,SACA0lB,iBACAplB,WACAqlB,YAEAtkB,EAAI,SAACvM,EAAKgN,GACV,GAAsB,oBAAXuB,OAGPhC,EAAI,SAAUvM,EAAKgN,GACf,OAAOX,EAAKE,EAAEvM,EAAKgN,QAGtB,CACD,IAAIskB,GAAqB,EACzB/kB,EAAI,SAAUvM,EAAKgN,GACf,IAAMI,EAAQmB,SAASG,IAsBvB,OARItB,IAEAA,EAAMuB,QACD2iB,IACDA,GAAqB,EACrBJ,EAAe9jB,EAAOf,KAGvBA,EAAKE,EAAEvM,EAAKgN,IAG3B,OAAOT,EAAEvM,EAAKgN,IAElB,MAAO,CACHX,OACAghB,EAAC,SAAC1qB,EAASqK,EAAQ2iB,GACf,OAAOtjB,EAAKghB,EAAE1qB,EAASqK,EAAQ2iB,IAEnCpjB,EAAC,SAACvM,EAAKgN,GACH,OAAOT,EAAEvM,EAAKgN,IAElBukB,IAAG,SAACrmB,EAAQvI,GAA0B,IAAjBquB,IAAW,UAAH,+CACzB,OAAO3kB,EAAKklB,IAAIrmB,EAAQvI,EAASquB,IAErCnjB,MAAK,SAACtJ,GACF,OAAO8H,EAAKM,YAAYpI,IAE5B2I,UAAS,WACL,OAAOb,EAAKa,aAEhBD,UAAS,SAACkkB,GACN,OAAO9kB,EAAKY,UAAUkkB,KA1G7B,SA+GL,IACIL,EADEU,EAAW,SAAC1I,GAAG,MAAoB,kBAARA,GAEjC,SAAS2I,EAAYC,EAAS/B,GAI1B,OAHKmB,IACDA,EAAW,IAAIrB,GAEZkC,EAAYD,GAAS,SAACA,EAAS1xB,GAClC,IAAM+J,EAAQ2nB,EAAQ1xB,GACtB,OAAIwxB,EAASznB,KACL6nB,EAAU7nB,EAAO4lB,SAArB,EAKO8B,EAAY1nB,EAAO4lB,MAItC,SAASkC,EAAcH,EAAS1kB,EAAQ2iB,GAepC,OAdKmB,IACDA,EAAW,IAAIrB,GAEnBkC,EAAYD,GAAS,SAACA,EAAS1xB,GAC3B,IAAM+J,EAAQ2nB,EAAQ1xB,GAClBwxB,EAASznB,GACL6nB,EAAU7nB,EAAO4lB,KACjB+B,EAAQ1xB,GAAO8xB,EAAW/nB,EAAOiD,EAAQ2iB,IAI7CkC,EAAc9nB,EAAOiD,EAAQ2iB,MAG9B+B,EAEX,SAASK,EAAmBC,EAAS,GAAkC,IAAhC9mB,EAAM,EAANA,OAAQgB,EAAO,EAAPA,QAASyjB,EAAU,EAAVA,WACpD,IAAKiC,EAAUI,EAASrC,GACpB,OAAOqC,EAENlB,IACDA,EAAW,IAAIrB,GAEnB,IAAMwC,EAAe,GACrB7tB,OAAOqB,KAAKyG,GAASvG,SAAQ,SAACqC,GACtBA,IAASkD,GACT+mB,EAAazrB,KAAK,CACd0E,OAAQlD,EACRgF,OAAQd,EAAQlE,QAI5BiqB,EAAaC,QAAQ,CAAEhnB,SAAQ8B,OAAQd,EAAQhB,KAC/C,IACI,OAAO3I,KAAK2e,UAAUiR,EAAe5vB,KAAKC,MAAMwvB,GAAUC,EAActC,GAAa,KAAM,GAE/F,MAAOvW,IACP,OAAO4Y,EAEX,SAASJ,EAAU7nB,EAAO4lB,GACtB,OAAO5lB,EAAMvJ,QAAQmvB,EAAW,KAAO,EAE3C,SAASmC,EAAW/nB,EAAOiD,EAAQ2iB,GAC/B,OAAOmB,EAASG,YAAYlnB,EAAOiD,EAAQ2iB,GAAY1vB,KAAK,IAEhE,SAASmyB,EAAaV,EAAS1xB,EAAKiyB,EAActC,GAC9C,IAAM5lB,EAAQ2nB,EAAQ1xB,GACtB,GAAIwxB,EAASznB,IAET,GAAI6nB,EAAU7nB,EAAO4lB,KACjB+B,EAAQ1xB,GAAO8xB,EAAW/nB,EAAOkoB,EAAa,GAAGjlB,OAAQ2iB,GACrDsC,EAAa/vB,OAAS,GAAG,CAEzB,IAAMmwB,EAAgBX,EAAQ1xB,EAAM,WAAa,GACjDiyB,EAAatsB,SAAQ,SAAC2sB,GAClBD,EAAaC,EAAWpnB,QAAU4mB,EAAW/nB,EAAOuoB,EAAWtlB,OAAQ2iB,YAMnFwC,EAAepoB,EAAOkoB,EAActC,GAG5C,SAASwC,EAAeT,EAASO,EAActC,GAI3C,OAHAgC,EAAYD,GAAS,SAACA,EAAS1xB,GAC3BoyB,EAAaV,EAAS1xB,EAAKiyB,EAActC,MAEtC+B,EAEX,SAASC,EAAYD,EAASa,GAC1B,GAAInsB,MAAMC,QAAQqrB,IACd,IAAK,IAAI1tB,EAAI,EAAGA,EAAI0tB,EAAQxvB,OAAQ8B,IAChC,GAAIuuB,EAAKb,EAAS1tB,GACd,OAAO,OAId,GAAIS,EAASitB,GACd,IAAK,IAAM1xB,KAAO0xB,EACd,GAAIa,EAAKb,EAAS1xB,GACd,OAAO,EAInB,OAAO,EAGX,SAASwyB,EAActmB,GACnB,OAAO,SAAChB,GACJ,OAAKA,GAGLA,EAASW,EAAgBX,IAAWA,EAC7BunB,EAAmBvnB,GAAQ8C,MAAK,SAAC9C,GAAM,OAAKgB,EAAQ1L,QAAQ0K,IAAW,MAHnEA,GAMnB,SAASunB,EAAmBvnB,GACxB,IAAMwnB,EAAQ,GACR9C,EAAS1kB,EAAO5J,MAAM,KAC5B,MAAOsuB,EAAO1tB,OACVwwB,EAAMlsB,KAAKopB,EAAO3vB,KAAK,MACvB2vB,EAAO+C,MAEX,OAAOD,EA3HuC,e,+CC9UlD,SAASE,EAAgBC,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAI9E,UAAU,qCAGxB9B,EAAOC,QAAUyG,EAAiB1G,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,gBCLvG,IAAI8B,EAAgB1B,EAAQ,IAC5B,SAASwG,EAAkBlzB,EAAQmiB,GACjC,IAAK,IAAIhe,EAAI,EAAGA,EAAIge,EAAM9f,OAAQ8B,IAAK,CACrC,IAAIgvB,EAAahR,EAAMhe,GACvBgvB,EAAWzV,WAAayV,EAAWzV,aAAc,EACjDyV,EAAW1V,cAAe,EACtB,UAAW0V,IAAYA,EAAW7E,UAAW,GACjD/pB,OAAOqJ,eAAe5N,EAAQouB,EAAc+E,EAAWhzB,KAAMgzB,IAGjE,SAASC,EAAaH,EAAaI,EAAYC,GAM7C,OALID,GAAYH,EAAkBD,EAAY9vB,UAAWkwB,GACrDC,GAAaJ,EAAkBD,EAAaK,GAChD/uB,OAAOqJ,eAAeqlB,EAAa,YAAa,CAC9C3E,UAAU,IAEL2E,EAET5G,EAAOC,QAAU8G,EAAc/G,EAAOC,QAAQE,YAAa,EAAMH,EAAOC,QAAQ,WAAaD,EAAOC,S,6BClBpG;;;;;;AAOA,IAAIiH,EAAchvB,OAAO+K,OAAO,IAIhC,SAASkkB,EAASzlB,GAChB,YAAa6G,IAAN7G,GAAyB,OAANA,EAG5B,SAAS0lB,EAAO1lB,GACd,YAAa6G,IAAN7G,GAAyB,OAANA,EAG5B,SAAS2lB,EAAQ3lB,GACf,OAAa,IAANA,EAGT,SAAS4lB,EAAS5lB,GAChB,OAAa,IAANA,EAMT,SAAS6lB,EAAa1pB,GACpB,MACmB,kBAAVA,GACU,kBAAVA,GAEU,kBAAVA,GACU,mBAAVA,EASX,SAAStF,EAAUC,GACjB,OAAe,OAARA,GAA+B,kBAARA,EAMhC,IAAIP,EAAYC,OAAOpB,UAAUtB,SAEjC,SAASgyB,EAAW3pB,GAClB,OAAO5F,EAAUS,KAAKmF,GAAOpI,MAAM,GAAI,GAOzC,SAASgD,EAAeD,GACtB,MAA+B,oBAAxBP,EAAUS,KAAKF,GAGxB,SAASivB,EAAU/lB,GACjB,MAA6B,oBAAtBzJ,EAAUS,KAAKgJ,GAMxB,SAASgmB,EAAmB9K,GAC1B,IAAIoE,EAAI2G,WAAWpwB,OAAOqlB,IAC1B,OAAOoE,GAAK,GAAKliB,KAAKC,MAAMiiB,KAAOA,GAAK4G,SAAShL,GAGnD,SAASvhB,EAAWuhB,GAClB,OACEwK,EAAMxK,IACc,oBAAbA,EAAIthB,MACU,oBAAdshB,EAAIzf,MAOf,SAAS3H,EAAUonB,GACjB,OAAc,MAAPA,EACH,GACA1iB,MAAMC,QAAQyiB,IAASnkB,EAAcmkB,IAAQA,EAAIpnB,WAAayC,EAC5D5B,KAAK2e,UAAU4H,EAAK,KAAM,GAC1BrlB,OAAOqlB,GAOf,SAASiL,EAAUjL,GACjB,IAAIoE,EAAI2G,WAAW/K,GACnB,OAAOrY,MAAMyc,GAAKpE,EAAMoE,EAO1B,SAAS8G,EACP5yB,EACA6yB,GAIA,IAFA,IAAI1yB,EAAM6C,OAAOa,OAAO,MACpBivB,EAAO9yB,EAAIE,MAAM,KACZ0C,EAAI,EAAGA,EAAIkwB,EAAKhyB,OAAQ8B,IAC/BzC,EAAI2yB,EAAKlwB,KAAM,EAEjB,OAAOiwB,EACH,SAAUnL,GAAO,OAAOvnB,EAAIunB,EAAI1a,gBAChC,SAAU0a,GAAO,OAAOvnB,EAAIunB,IAMlC,IAAIqL,EAAeH,EAAQ,kBAAkB,GAKzCI,EAAsBJ,EAAQ,8BAKlC,SAASK,EAAQ9P,EAAK3T,GACpB,GAAI2T,EAAIriB,OAAQ,CACd,IAAIyE,EAAQ4d,EAAI/jB,QAAQoQ,GACxB,GAAIjK,GAAS,EACX,OAAO4d,EAAI3d,OAAOD,EAAO,IAQ/B,IAAItC,EAAiBD,OAAOpB,UAAUqB,eACtC,SAASQ,EAAQH,EAAK1E,GACpB,OAAOqE,EAAeO,KAAKF,EAAK1E,GAMlC,SAAS+E,EAAQR,GACf,IAAIS,EAAQZ,OAAOa,OAAO,MAC1B,OAAO,SAAoB7D,GACzB,IAAI8D,EAAMF,EAAM5D,GAChB,OAAO8D,IAAQF,EAAM5D,GAAOmD,EAAGnD,KAOnC,IAAI+D,EAAa,SACbC,EAAWL,GAAO,SAAU3D,GAC9B,OAAOA,EAAIsC,QAAQyB,GAAY,SAAUE,EAAG7D,GAAK,OAAOA,EAAIA,EAAE8D,cAAgB,SAM5EgvB,EAAavvB,GAAO,SAAU3D,GAChC,OAAOA,EAAI6C,OAAO,GAAGqB,cAAgBlE,EAAIO,MAAM,MAM7C4yB,EAAc,aACdC,EAAYzvB,GAAO,SAAU3D,GAC/B,OAAOA,EAAIsC,QAAQ6wB,EAAa,OAAOnmB,iBAYzC,SAASqmB,EAAclwB,EAAImU,GACzB,SAASgc,EAAStH,GAChB,IAAIL,EAAInU,UAAU1W,OAClB,OAAO6qB,EACHA,EAAI,EACFxoB,EAAGoT,MAAMe,EAAKE,WACdrU,EAAGK,KAAK8T,EAAK0U,GACf7oB,EAAGK,KAAK8T,GAId,OADAgc,EAAQC,QAAUpwB,EAAGrC,OACdwyB,EAGT,SAASE,EAAYrwB,EAAImU,GACvB,OAAOnU,EAAGqqB,KAAKlW,GAGjB,IAAIkW,EAAO5C,SAAShpB,UAAU4rB,KAC1BgG,EACAH,EAKJ,SAASI,EAASX,EAAMY,GACtBA,EAAQA,GAAS,EACjB,IAAI9wB,EAAIkwB,EAAKhyB,OAAS4yB,EAClBjQ,EAAM,IAAIze,MAAMpC,GACpB,MAAOA,IACL6gB,EAAI7gB,GAAKkwB,EAAKlwB,EAAI8wB,GAEpB,OAAOjQ,EAMT,SAASvE,EAAQyU,EAAIC,GACnB,IAAK,IAAIh1B,KAAOg1B,EACdD,EAAG/0B,GAAOg1B,EAAMh1B,GAElB,OAAO+0B,EAMT,SAASE,EAAU1Q,GAEjB,IADA,IAAIre,EAAM,GACDlC,EAAI,EAAGA,EAAIugB,EAAIriB,OAAQ8B,IAC1BugB,EAAIvgB,IACNsc,EAAOpa,EAAKqe,EAAIvgB,IAGpB,OAAOkC,EAUT,SAASpB,EAAMsoB,EAAG8H,EAAG1zB,IAKrB,IAAI2zB,EAAK,SAAU/H,EAAG8H,EAAG1zB,GAAK,OAAO,GAOjC4zB,EAAW,SAAU/vB,GAAK,OAAOA,GAMrC,SAASgwB,EAAYjI,EAAG8H,GACtB,GAAI9H,IAAM8H,EAAK,OAAO,EACtB,IAAII,EAAY7wB,EAAS2oB,GACrBmI,EAAY9wB,EAASywB,GACzB,IAAII,IAAaC,EAwBV,OAAKD,IAAcC,GACjB9xB,OAAO2pB,KAAO3pB,OAAOyxB,GAxB5B,IACE,IAAIM,EAAWpvB,MAAMC,QAAQ+mB,GACzBqI,EAAWrvB,MAAMC,QAAQ6uB,GAC7B,GAAIM,GAAYC,EACd,OAAOrI,EAAElrB,SAAWgzB,EAAEhzB,QAAUkrB,EAAExQ,OAAM,SAAUxD,EAAGpV,GACnD,OAAOqxB,EAAWjc,EAAG8b,EAAElxB,OAEpB,GAAIopB,aAAa9pB,MAAQ4xB,aAAa5xB,KAC3C,OAAO8pB,EAAEsI,YAAcR,EAAEQ,UACpB,GAAKF,GAAaC,EAQvB,OAAO,EAPP,IAAIE,EAAQvxB,OAAOqB,KAAK2nB,GACpBwI,EAAQxxB,OAAOqB,KAAKyvB,GACxB,OAAOS,EAAMzzB,SAAW0zB,EAAM1zB,QAAUyzB,EAAM/Y,OAAM,SAAU5c,GAC5D,OAAOq1B,EAAWjI,EAAEptB,GAAMk1B,EAAEl1B,OAMhC,MAAOoZ,GAEP,OAAO,GAcb,SAASyc,EAActR,EAAKuE,GAC1B,IAAK,IAAI9kB,EAAI,EAAGA,EAAIugB,EAAIriB,OAAQ8B,IAC9B,GAAIqxB,EAAW9Q,EAAIvgB,GAAI8kB,GAAQ,OAAO9kB,EAExC,OAAQ,EAMV,SAASkiB,EAAM3hB,GACb,IAAIuxB,GAAS,EACb,OAAO,WACAA,IACHA,GAAS,EACTvxB,EAAGoT,MAAM5X,KAAM6Y,aAKrB,IAAImd,EAAc,CAChB,YACA,YACA,UAGEC,EAAkB,CACpB,eACA,UACA,cACA,UACA,eACA,UACA,gBACA,YACA,YACA,cACA,gBACA,kBAOEC,EAAS,CAKXC,sBAAuB9xB,OAAOa,OAAO,MAKrCkxB,QAAQ,EAKRC,eAAe,EAKfC,UAAU,EAKVC,aAAa,EAKbC,aAAc,KAKdC,YAAa,KAKbC,gBAAiB,GAMjBC,SAAUtyB,OAAOa,OAAO,MAMxB0xB,cAAexB,EAMfyB,eAAgBzB,EAMhB0B,iBAAkB1B,EAKlB2B,gBAAiBhyB,EAKjBiyB,qBAAsB3B,EAMtB4B,YAAa7B,EAMb8B,OAAO,EAKPC,gBAAiBlB,GAUfmB,EAAgB,8JAKpB,SAASC,EAAYh2B,GACnB,IAAII,GAAKJ,EAAM,IAAIK,WAAW,GAC9B,OAAa,KAAND,GAAoB,KAANA,EAMvB,SAAS61B,EAAK3yB,EAAK1E,EAAK8oB,EAAKvL,GAC3BnZ,OAAOqJ,eAAe/I,EAAK1E,EAAK,CAC9B+J,MAAO+e,EACPvL,aAAcA,EACd4Q,UAAU,EACV7Q,cAAc,IAOlB,IAAIga,EAAS,IAAIC,OAAQ,KAAQJ,EAAoB,OAAI,WACzD,SAASK,EAAWxR,GAClB,IAAIsR,EAAO3zB,KAAKqiB,GAAhB,CAGA,IAAIyR,EAAWzR,EAAK1kB,MAAM,KAC1B,OAAO,SAAUoD,GACf,IAAK,IAAIV,EAAI,EAAGA,EAAIyzB,EAASv1B,OAAQ8B,IAAK,CACxC,IAAKU,EAAO,OACZA,EAAMA,EAAI+yB,EAASzzB,IAErB,OAAOU,IAOX,IA+BIgzB,EA/BAC,EAAW,aAAe,GAG1BC,EAA8B,qBAAX3L,OACnB4L,EAAkC,qBAAlBC,iBAAmCA,cAAcrtB,SACjEstB,EAAeF,GAAUC,cAAcrtB,SAAS2D,cAChD4pB,GAAKJ,GAAa3L,OAAOgM,WAAahM,OAAOgM,UAAUC,UAAU9pB,cACjE+pB,GAAOH,IAAM,eAAer0B,KAAKq0B,IAEjCI,IADQJ,IAAMA,GAAGx3B,QAAQ,YAChBw3B,IAAMA,GAAGx3B,QAAQ,SAAW,GAErC2J,IADa6tB,IAAMA,GAAGx3B,QAAQ,WACrBw3B,IAAM,uBAAuBr0B,KAAKq0B,KAA0B,QAAjBD,GAMpDM,IALWL,IAAM,cAAcr0B,KAAKq0B,IACtBA,IAAM,YAAYr0B,KAAKq0B,IAC9BA,IAAMA,GAAGha,MAAM,kBAGR,GAAKnQ,OACvB,GAAI+pB,EACF,IACE,IAAI5U,GAAO,GACX5e,OAAOqJ,eAAeuV,GAAM,UAAW,CACrCtV,IAAK,eAGPue,OAAOqM,iBAAiB,eAAgB,KAAMtV,IAC9C,MAAO5J,KAMX,IAAImf,GAAoB,WAWtB,YAVkB9jB,IAAdijB,IAOAA,GALGE,IAAcC,GAA4B,qBAAX7oB,IAGtBA,EAAO,YAAgD,WAAlCA,EAAO,WAAW2G,IAAI6iB,UAKpDd,GAILrB,GAAWuB,GAAa3L,OAAOwM,6BAGnC,SAASC,GAAUC,GACjB,MAAuB,oBAATA,GAAuB,cAAch1B,KAAKg1B,EAAKj3B,YAG/D,IAIIk3B,GAJAC,GACgB,qBAAX7L,QAA0B0L,GAAS1L,SACvB,qBAAZyB,SAA2BiK,GAASjK,QAAQqK,SAMnDF,GAFiB,qBAARpc,KAAuBkc,GAASlc,KAElCA,IAGc,WACnB,SAASA,IACPzc,KAAK4N,IAAMvJ,OAAOa,OAAO,MAY3B,OAVAuX,EAAIxZ,UAAU+1B,IAAM,SAAc/4B,GAChC,OAAyB,IAAlBD,KAAK4N,IAAI3N,IAElBwc,EAAIxZ,UAAUuuB,IAAM,SAAcvxB,GAChCD,KAAK4N,IAAI3N,IAAO,GAElBwc,EAAIxZ,UAAUg2B,MAAQ,WACpBj5B,KAAK4N,IAAMvJ,OAAOa,OAAO,OAGpBuX,EAdW,GAoBtB,IAAInF,GAAOvS,EACPm0B,GAAMn0B,EACNo0B,GAAyB,EACzBC,GAAsB,EAGpBC,GAAgC,qBAAZhiB,QACpBiiB,GAAa,kBACbC,GAAW,SAAUl4B,GAAO,OAAOA,EACpCsC,QAAQ21B,IAAY,SAAU73B,GAAK,OAAOA,EAAE8D,iBAC5C5B,QAAQ,QAAS,KAEpB2T,GAAO,SAAUkiB,EAAK5e,GACpB,IAAI6e,EAAQ7e,EAAKue,GAAuBve,GAAM,GAE1Csb,EAAOO,YACTP,EAAOO,YAAY5xB,KAAK,KAAM20B,EAAK5e,EAAI6e,GAC9BJ,KAAgBnD,EAAOE,QAChC/e,QAAQ3U,MAAO,eAAiB82B,EAAMC,IAI1CP,GAAM,SAAUM,EAAK5e,GACfye,KAAgBnD,EAAOE,QACzB/e,QAAQC,KAAK,cAAgBkiB,GAC3B5e,EAAKue,GAAuBve,GAAM,MAKxCwe,GAAsB,SAAUxe,EAAI8e,GAClC,GAAI9e,EAAG+e,QAAU/e,EACf,OAAIA,EAAG2K,UAAY3K,EAAG2K,SAASiE,OACtB,GAAO5O,EAAG2K,SAASiE,OAErB,SAET,IAAIxhB,EAAwB,oBAAP4S,GAA+B,MAAVA,EAAG5B,IACzC4B,EAAG5S,QACH4S,EAAGgf,OACDhf,EAAG2K,UAAY3K,EAAG7Q,YAAY/B,QAC9B4S,EACF3S,EAAOD,EAAQC,MAAQD,EAAQ6xB,cAC/BrX,EAAOxa,EAAQwhB,OACnB,IAAKvhB,GAAQua,EAAM,CACjB,IAAIvE,EAAQuE,EAAKvE,MAAM,mBACvBhW,EAAOgW,GAASA,EAAM,GAGxB,OACGhW,EAAQ,IAAOsxB,GAAStxB,GAAS,IAAO,gBACxCua,IAAwB,IAAhBkX,EAAyB,OAASlX,EAAQ,KAIvD,IAAIsX,GAAS,SAAUz4B,EAAK8rB,GAC1B,IAAIhnB,EAAM,GACV,MAAOgnB,EACDA,EAAI,IAAM,IAAKhnB,GAAO9E,GACtB8rB,EAAI,IAAK9rB,GAAOA,GACpB8rB,IAAM,EAER,OAAOhnB,GAGTgzB,GAAyB,SAAUve,GACjC,GAAIA,EAAGgf,QAAUhf,EAAG0K,QAAS,CAC3B,IAAIyU,EAAO,GACPC,EAA2B,EAC/B,MAAOpf,GAA2B,aAArBA,EAAG2K,SAAStd,KAAqB,CAC5C,GAAI8xB,EAAK53B,OAAS,EAAG,CACnB,IAAI83B,EAAOF,EAAKA,EAAK53B,OAAS,GAC9B,GAAI83B,EAAKlwB,cAAgB6Q,EAAG7Q,YAAa,CACvCiwB,IACApf,EAAKA,EAAG0K,QACR,SACS0U,EAA2B,IACpCD,EAAKA,EAAK53B,OAAS,GAAK,CAAC83B,EAAMD,GAC/BA,EAA2B,IAG9Bpf,EAAG2K,SAAS8R,YAAc0C,EAAKtzB,KAAKmU,GACrCA,EAAKA,EAAG0K,QAEV,MAAO,mBAAqByU,EACzBv4B,KAAI,SAAUoZ,EAAI3W,GAAK,MAAQ,IAAY,IAANA,EAAU,WAAU61B,GAAO,IAAK,EAAQ,EAAJ71B,KAAWoC,MAAMC,QAAQsU,GAC3Fwe,GAAoBxe,EAAG,IAAO,QAAWA,EAAG,GAAM,oBACpDwe,GAAoBxe,OACzB1a,KAAK,MAER,MAAQ,iBAAoBk5B,GAAoBxe,GAAO,KAO7D,IAAIxY,GAAM,EAMN83B,GAAM,WACRl6B,KAAKsmB,GAAKlkB,KACVpC,KAAKm6B,KAAO,IAwCd,SAASC,GAAYt6B,GACnBo6B,GAAIG,aAAaC,YAAY7zB,KAAK3G,GAClCo6B,GAAIG,aAAav6B,OAASA,EAC1Bo6B,GAAIp6B,OAASA,EAGf,SAASy6B,KACPL,GAAIG,aAAaC,YAAY1H,MAC7BsH,GAAIG,aAAav6B,OAASo6B,GAAIG,aAAaC,YAAYJ,GAAIG,aAAaC,YAAYn4B,OAAS,GAC7F+3B,GAAIp6B,OAASo6B,GAAIG,aAAav6B,OA9ChCo6B,GAAIj3B,UAAUu3B,OAAS,SAAiBjK,GACtCvwB,KAAKm6B,KAAK1zB,KAAK8pB,IAGjB2J,GAAIj3B,UAAUw3B,UAAY,SAAoBlK,GAC5C+D,EAAOt0B,KAAKm6B,KAAM5J,IAGpB2J,GAAIj3B,UAAUy3B,OAAS,WACjBR,GAAIG,aAAav6B,QACnBo6B,GAAIG,aAAav6B,OAAO66B,OAAO36B,OAInCk6B,GAAIj3B,UAAU23B,OAAS,WAErB,IAAIT,EAAOn6B,KAAKm6B,KAAKv4B,QACyBs0B,EAAOgB,OAInDiD,EAAKx0B,MAAK,SAAU0nB,EAAG8H,GAAK,OAAO9H,EAAE/G,GAAK6O,EAAE7O,MAE9C,IAAK,IAAIriB,EAAI,EAAG+oB,EAAImN,EAAKh4B,OAAQ8B,EAAI+oB,EAAG/oB,IACtCk2B,EAAKl2B,GAAG42B,UASZX,GAAIG,aAAe,GACnBH,GAAIG,aAAav6B,OAAS,KAC1Bo6B,GAAIG,aAAaC,YAAc,GAgB/B,IAAIQ,GAAQ,SACVC,EACAxzB,EACAyzB,EACA3K,EACA4K,EACAha,EACAsI,EACA2R,GAEAl7B,KAAK+6B,IAAMA,EACX/6B,KAAKuH,KAAOA,EACZvH,KAAKg7B,SAAWA,EAChBh7B,KAAKqwB,KAAOA,EACZrwB,KAAKi7B,IAAMA,EACXj7B,KAAKm7B,QAAKzmB,EACV1U,KAAKihB,QAAUA,EACfjhB,KAAKo7B,eAAY1mB,EACjB1U,KAAKq7B,eAAY3mB,EACjB1U,KAAKs7B,eAAY5mB,EACjB1U,KAAKC,IAAMsH,GAAQA,EAAKtH,IACxBD,KAAKupB,iBAAmBA,EACxBvpB,KAAKu7B,uBAAoB7mB,EACzB1U,KAAKod,YAAS1I,EACd1U,KAAKw7B,KAAM,EACXx7B,KAAKy7B,UAAW,EAChBz7B,KAAK07B,cAAe,EACpB17B,KAAK27B,WAAY,EACjB37B,KAAK47B,UAAW,EAChB57B,KAAK6lB,QAAS,EACd7lB,KAAKk7B,aAAeA,EACpBl7B,KAAK67B,eAAYnnB,EACjB1U,KAAK87B,oBAAqB,GAGxBC,GAAqB,CAAEC,MAAO,CAAEze,cAAc,IAIlDwe,GAAmBC,MAAMruB,IAAM,WAC7B,OAAO3N,KAAKu7B,mBAGdl3B,OAAO43B,iBAAkBnB,GAAM73B,UAAW84B,IAE1C,IAAIG,GAAmB,SAAU7L,QACjB,IAATA,IAAkBA,EAAO,IAE9B,IAAI8L,EAAO,IAAIrB,GAGf,OAFAqB,EAAK9L,KAAOA,EACZ8L,EAAKR,WAAY,EACVQ,GAGT,SAASC,GAAiBrT,GACxB,OAAO,IAAI+R,QAAMpmB,OAAWA,OAAWA,EAAWhR,OAAOqlB,IAO3D,SAASsT,GAAYC,GACnB,IAAIC,EAAS,IAAIzB,GACfwB,EAAMvB,IACNuB,EAAM/0B,KAIN+0B,EAAMtB,UAAYsB,EAAMtB,SAASp5B,QACjC06B,EAAMjM,KACNiM,EAAMrB,IACNqB,EAAMrb,QACNqb,EAAM/S,iBACN+S,EAAMpB,cAWR,OATAqB,EAAOpB,GAAKmB,EAAMnB,GAClBoB,EAAOd,SAAWa,EAAMb,SACxBc,EAAOt8B,IAAMq8B,EAAMr8B,IACnBs8B,EAAOZ,UAAYW,EAAMX,UACzBY,EAAOnB,UAAYkB,EAAMlB,UACzBmB,EAAOlB,UAAYiB,EAAMjB,UACzBkB,EAAOjB,UAAYgB,EAAMhB,UACzBiB,EAAOV,UAAYS,EAAMT,UACzBU,EAAOX,UAAW,EACXW,EAQT,IAAIC,GAAan2B,MAAMpD,UACnBw5B,GAAep4B,OAAOa,OAAOs3B,IAE7BE,GAAiB,CACnB,OACA,MACA,QACA,UACA,SACA,OACA,WAMFA,GAAe92B,SAAQ,SAAUuB,GAE/B,IAAIw1B,EAAWH,GAAWr1B,GAC1BmwB,EAAImF,GAAct1B,GAAQ,WACxB,IAAIiJ,EAAO,GAAIT,EAAMkJ,UAAU1W,OAC/B,MAAQwN,IAAQS,EAAMT,GAAQkJ,UAAWlJ,GAEzC,IAEIitB,EAFA54B,EAAS24B,EAAS/kB,MAAM5X,KAAMoQ,GAC9BysB,EAAK78B,KAAK88B,OAEd,OAAQ31B,GACN,IAAK,OACL,IAAK,UACHy1B,EAAWxsB,EACX,MACF,IAAK,SACHwsB,EAAWxsB,EAAKxO,MAAM,GACtB,MAKJ,OAHIg7B,GAAYC,EAAGE,aAAaH,GAEhCC,EAAGG,IAAIpC,SACA52B,QAMX,IAAIi5B,GAAY54B,OAAO64B,oBAAoBT,IAMvCU,IAAgB,EAEpB,SAASC,GAAiBpzB,GACxBmzB,GAAgBnzB,EASlB,IAAIqzB,GAAW,SAAmBrzB,GAChChK,KAAKgK,MAAQA,EACbhK,KAAKg9B,IAAM,IAAI9C,GACfl6B,KAAKs9B,QAAU,EACfhG,EAAIttB,EAAO,SAAUhK,MACjBqG,MAAMC,QAAQ0D,IACZ4tB,EAEG5tB,EAAMvD,OAASuD,EAAMqF,UAAU5I,KAChC82B,GAAYvzB,EAAOyyB,GAAcQ,IAEjCO,GAAaxzB,EAAOyyB,IAIxBc,GAAYvzB,EAAOyyB,GAAcQ,IAEnCj9B,KAAK+8B,aAAa/yB,IAElBhK,KAAKwyB,KAAKxoB,IA+Bd,SAASwzB,GAAc19B,EAAQ29B,GAE7B39B,EAAOuP,UAAYouB,EASrB,SAASF,GAAaz9B,EAAQ29B,EAAK/3B,GACjC,IAAK,IAAIzB,EAAI,EAAG+oB,EAAItnB,EAAKvD,OAAQ8B,EAAI+oB,EAAG/oB,IAAK,CAC3C,IAAIhE,EAAMyF,EAAKzB,GACfqzB,EAAIx3B,EAAQG,EAAKw9B,EAAIx9B,KASzB,SAASy9B,GAAS1zB,EAAO2zB,GAIvB,IAAId,EAHJ,GAAKn4B,EAASsF,MAAUA,aAAiB8wB,IAmBzC,OAfIh2B,EAAOkF,EAAO,WAAaA,EAAM8yB,kBAAkBO,GACrDR,EAAK7yB,EAAM8yB,QAEXK,IACC3E,OACAnyB,MAAMC,QAAQ0D,KAAUpF,EAAcoF,KACvC3F,OAAOsZ,aAAa3T,IACnBA,EAAM4vB,QACN5vB,EAAM4zB,oBAEPf,EAAK,IAAIQ,GAASrzB,IAEhB2zB,GAAcd,GAChBA,EAAGS,UAEET,EAMT,SAASgB,GACPl5B,EACA1E,EACA8oB,EACA+U,EACAC,GAEA,IAAIf,EAAM,IAAI9C,GAEV8D,EAAW35B,OAAO45B,yBAAyBt5B,EAAK1E,GACpD,IAAI+9B,IAAsC,IAA1BA,EAASzgB,aAAzB,CAKA,IAAI2gB,EAASF,GAAYA,EAASrwB,IAC9BwwB,EAASH,GAAYA,EAASpwB,IAC5BswB,IAAUC,GAAgC,IAArBtlB,UAAU1W,SACnC4mB,EAAMpkB,EAAI1E,IAGZ,IAAIm+B,GAAWL,GAAWL,GAAQ3U,GAClC1kB,OAAOqJ,eAAe/I,EAAK1E,EAAK,CAC9Bud,YAAY,EACZD,cAAc,EACd5P,IAAK,WACH,IAAI3D,EAAQk0B,EAASA,EAAOr5B,KAAKF,GAAOokB,EAUxC,OATImR,GAAIG,aAAav6B,SACnBk9B,EAAItC,SACA0D,IACFA,EAAQpB,IAAItC,SACRr0B,MAAMC,QAAQ0D,IAChBq0B,GAAYr0B,KAIXA,GAET4D,IAAK,SAAyB4T,GAC5B,IAAIxX,EAAQk0B,EAASA,EAAOr5B,KAAKF,GAAOokB,EAEpCvH,IAAWxX,GAAUwX,IAAWA,GAAUxX,IAAUA,IAIX8zB,GAC3CA,IAGEI,IAAWC,IACXA,EACFA,EAAOt5B,KAAKF,EAAK6c,GAEjBuH,EAAMvH,EAER4c,GAAWL,GAAWL,GAAQlc,GAC9Bwb,EAAIpC,eAUV,SAAShtB,GAAK9N,EAAQG,EAAK8oB,GAMzB,IAJGuK,EAAQxzB,IAAW4zB,EAAY5zB,KAEhCwX,GAAM,wEAA0E,GAE9EjR,MAAMC,QAAQxG,IAAW+zB,EAAkB5zB,GAG7C,OAFAH,EAAOqC,OAAS8I,KAAKqzB,IAAIx+B,EAAOqC,OAAQlC,GACxCH,EAAO+G,OAAO5G,EAAK,EAAG8oB,GACfA,EAET,GAAI9oB,KAAOH,KAAYG,KAAOoE,OAAOpB,WAEnC,OADAnD,EAAOG,GAAO8oB,EACPA,EAET,IAAI8T,EAAK,EAASC,OAClB,OAAIh9B,EAAO85B,QAAWiD,GAAMA,EAAGS,SACYhmB,GACvC,4HAGKyR,GAEJ8T,GAILgB,GAAkBhB,EAAG7yB,MAAO/J,EAAK8oB,GACjC8T,EAAGG,IAAIpC,SACA7R,IALLjpB,EAAOG,GAAO8oB,EACPA,GAUX,SAASwV,GAAKz+B,EAAQG,GAMpB,IAJGqzB,EAAQxzB,IAAW4zB,EAAY5zB,KAEhCwX,GAAM,2EAA6E,GAEjFjR,MAAMC,QAAQxG,IAAW+zB,EAAkB5zB,GAC7CH,EAAO+G,OAAO5G,EAAK,OADrB,CAIA,IAAI48B,EAAK,EAASC,OACdh9B,EAAO85B,QAAWiD,GAAMA,EAAGS,QACYhmB,GACvC,wFAKCxS,EAAOhF,EAAQG,YAGbH,EAAOG,GACT48B,GAGLA,EAAGG,IAAIpC,WAOT,SAASyD,GAAar0B,GACpB,IAAK,IAAIqP,OAAI,EAAUpV,EAAI,EAAG+oB,EAAIhjB,EAAM7H,OAAQ8B,EAAI+oB,EAAG/oB,IACrDoV,EAAIrP,EAAM/F,GACVoV,GAAKA,EAAEyjB,QAAUzjB,EAAEyjB,OAAOE,IAAItC,SAC1Br0B,MAAMC,QAAQ+S,IAChBglB,GAAYhlB,GAjNlBgkB,GAASp6B,UAAUuvB,KAAO,SAAe7tB,GAEvC,IADA,IAAIe,EAAOrB,OAAOqB,KAAKf,GACdV,EAAI,EAAGA,EAAIyB,EAAKvD,OAAQ8B,IAC/B45B,GAAkBl5B,EAAKe,EAAKzB,KAOhCo5B,GAASp6B,UAAU85B,aAAe,SAAuByB,GACvD,IAAK,IAAIv6B,EAAI,EAAG+oB,EAAIwR,EAAMr8B,OAAQ8B,EAAI+oB,EAAG/oB,IACvCy5B,GAAQc,EAAMv6B,KAiNlB,IAAIw6B,GAASvI,EAAOC,sBAoBpB,SAASuI,GAAW1J,EAAInH,GACtB,IAAKA,EAAQ,OAAOmH,EAOpB,IANA,IAAI/0B,EAAK0+B,EAAOC,EAEZl5B,EAAOozB,GACPpK,QAAQqK,QAAQlL,GAChBxpB,OAAOqB,KAAKmoB,GAEP5pB,EAAI,EAAGA,EAAIyB,EAAKvD,OAAQ8B,IAC/BhE,EAAMyF,EAAKzB,GAEC,WAARhE,IACJ0+B,EAAQ3J,EAAG/0B,GACX2+B,EAAU/Q,EAAK5tB,GACV6E,EAAOkwB,EAAI/0B,GAGd0+B,IAAUC,GACVh6B,EAAc+5B,IACd/5B,EAAcg6B,IAEdF,GAAUC,EAAOC,GANjBhxB,GAAIonB,EAAI/0B,EAAK2+B,IASjB,OAAO5J,EAMT,SAAS6J,GACP54B,EACAC,EACA0U,GAEA,OAAKA,EAoBI,WAEL,IAAIkkB,EAAmC,oBAAb54B,EACtBA,EAASrB,KAAK+V,EAAIA,GAClB1U,EACA64B,EAAmC,oBAAd94B,EACrBA,EAAUpB,KAAK+V,EAAIA,GACnB3U,EACJ,OAAI64B,EACKJ,GAAUI,EAAcC,GAExBA,GA7BN74B,EAGAD,EAQE,WACL,OAAOy4B,GACe,oBAAbx4B,EAA0BA,EAASrB,KAAK7E,KAAMA,MAAQkG,EACxC,oBAAdD,EAA2BA,EAAUpB,KAAK7E,KAAMA,MAAQiG,IAV1DC,EAHAD,EA2Db,SAASD,GACPC,EACAC,GAEA,IAAIC,EAAMD,EACND,EACEA,EAAUG,OAAOF,GACjBG,MAAMC,QAAQJ,GACZA,EACA,CAACA,GACLD,EACJ,OAAOE,EACHI,GAAYJ,GACZA,EAGN,SAASI,GAAaC,GAEpB,IADA,IAAIL,EAAM,GACDlC,EAAI,EAAGA,EAAIuC,EAAMrE,OAAQ8B,KACD,IAA3BkC,EAAI1F,QAAQ+F,EAAMvC,KACpBkC,EAAIM,KAAKD,EAAMvC,IAGnB,OAAOkC,EAcT,SAAS64B,GACP/4B,EACAC,EACA0U,EACA3a,GAEA,IAAIkG,EAAM9B,OAAOa,OAAOe,GAAa,MACrC,OAAIC,GACuC+4B,GAAiBh/B,EAAKiG,EAAU0U,GAClE2F,EAAOpa,EAAKD,IAEZC,EA/JTs4B,GAAOS,GAAKT,GAAOxX,UAAY,SAAU7J,EAAQ4e,EAAOphB,EAAI3a,GAO1D,OANK2a,GACHtD,GACE,WAAcrX,EAAd,uEAIGk/B,GAAa/hB,EAAQ4e,IA+EhCyC,GAAOl3B,KAAO,SACZtB,EACAC,EACA0U,GAEA,OAAKA,EAcEikB,GAAc54B,EAAWC,EAAU0U,GAbpC1U,GAAgC,oBAAbA,GACoBoR,GACvC,qGAGAsD,GAGK3U,GAEF44B,GAAc54B,EAAWC,IAmCpC+vB,EAAgBrwB,SAAQ,SAAUe,GAChC83B,GAAO93B,GAAQX,MAyBjBgwB,EAAYpwB,SAAQ,SAAU+T,GAC5B8kB,GAAO9kB,EAAO,KAAOqlB,MASvBP,GAAO3wB,MAAQ,SACb7H,EACAC,EACA0U,EACA3a,GAMA,GAHIgG,IAAcqyB,KAAeryB,OAAYyO,GACzCxO,IAAaoyB,KAAepyB,OAAWwO,IAEtCxO,EAAY,OAAO7B,OAAOa,OAAOe,GAAa,MAInD,GAFEg5B,GAAiBh/B,EAAKiG,EAAU0U,IAE7B3U,EAAa,OAAOC,EACzB,IAAI4e,EAAM,GAEV,IAAK,IAAIsa,KADT7e,EAAOuE,EAAK7e,GACMC,EAAU,CAC1B,IAAIkX,EAAS0H,EAAIsa,GACbpD,EAAQ91B,EAASk5B,GACjBhiB,IAAW/W,MAAMC,QAAQ8W,KAC3BA,EAAS,CAACA,IAEZ0H,EAAIsa,GAAShiB,EACTA,EAAOhX,OAAO41B,GACd31B,MAAMC,QAAQ01B,GAASA,EAAQ,CAACA,GAEtC,OAAOlX,GAMT2Z,GAAOxc,MACPwc,GAAO1xB,QACP0xB,GAAOY,OACPZ,GAAOa,SAAW,SAChBr5B,EACAC,EACA0U,EACA3a,GAKA,GAHIiG,GACF+4B,GAAiBh/B,EAAKiG,EAAU0U,IAE7B3U,EAAa,OAAOC,EACzB,IAAI4e,EAAMzgB,OAAOa,OAAO,MAGxB,OAFAqb,EAAOuE,EAAK7e,GACRC,GAAYqa,EAAOuE,EAAK5e,GACrB4e,GAET2Z,GAAOc,QAAUV,GAKjB,IAAIM,GAAe,SAAUl5B,EAAWC,GACtC,YAAoBwO,IAAbxO,EACHD,EACAC,GAMN,SAASs5B,GAAiBx3B,GACxB,IAAK,IAAI/H,KAAO+H,EAAQ8T,WACtB2jB,GAAsBx/B,GAI1B,SAASw/B,GAAuBx3B,GACzB,IAAIuvB,OAAQ,uBAA0BJ,EAAoB,OAAI,OAAQxzB,KAAKqE,IAC9EqP,GACE,4BAA8BrP,EAA9B,2FAIAmsB,EAAansB,IAASiuB,EAAOU,cAAc3uB,KAC7CqP,GACE,kEACSrP,GASf,SAASy3B,GAAgB13B,EAAS4S,GAChC,IAAIqH,EAAQja,EAAQia,MACpB,GAAKA,EAAL,CACA,IACIhe,EAAG8kB,EAAK9gB,EADR9B,EAAM,GAEV,GAAIE,MAAMC,QAAQ2b,GAAQ,CACxBhe,EAAIge,EAAM9f,OACV,MAAO8B,IACL8kB,EAAM9G,EAAMhe,GACO,kBAAR8kB,GACT9gB,EAAO5C,EAAS0jB,GAChB5iB,EAAI8B,GAAQ,CAAE0R,KAAM,OAEpBrC,GAAK,uDAGJ,GAAI1S,EAAcqd,GACvB,IAAK,IAAIhiB,KAAOgiB,EACd8G,EAAM9G,EAAMhiB,GACZgI,EAAO5C,EAASpF,GAChBkG,EAAI8B,GAAQrD,EAAcmkB,GACtBA,EACA,CAAEpP,KAAMoP,QAGdzR,GACE,6EACcqc,EAAU1R,GAAU,IAClCrH,GAGJ5S,EAAQia,MAAQ9b,GAMlB,SAASw5B,GAAiB33B,EAAS4S,GACjC,IAAIykB,EAASr3B,EAAQq3B,OACrB,GAAKA,EAAL,CACA,IAAIO,EAAa53B,EAAQq3B,OAAS,GAClC,GAAIh5B,MAAMC,QAAQ+4B,GAChB,IAAK,IAAIp7B,EAAI,EAAGA,EAAIo7B,EAAOl9B,OAAQ8B,IACjC27B,EAAWP,EAAOp7B,IAAM,CAAE4pB,KAAMwR,EAAOp7B,SAEpC,GAAIW,EAAcy6B,GACvB,IAAK,IAAIp/B,KAAOo/B,EAAQ,CACtB,IAAItW,EAAMsW,EAAOp/B,GACjB2/B,EAAW3/B,GAAO2E,EAAcmkB,GAC5BxI,EAAO,CAAEsN,KAAM5tB,GAAO8oB,GACtB,CAAE8E,KAAM9E,QAGdzR,GACE,8EACcqc,EAAU0L,GAAW,IACnCzkB,IAQN,SAASilB,GAAqB73B,GAC5B,IAAI83B,EAAO93B,EAAQ+3B,WACnB,GAAID,EACF,IAAK,IAAI7/B,KAAO6/B,EAAM,CACpB,IAAIE,EAASF,EAAK7/B,GACI,oBAAX+/B,IACTF,EAAK7/B,GAAO,CAAE4uB,KAAMmR,EAAQnF,OAAQmF,KAM5C,SAASf,GAAkBh3B,EAAM+B,EAAO4Q,GACjChW,EAAcoF,IACjBsN,GACE,6BAAgCrP,EAAhC,kCACc0rB,EAAU3pB,GAAU,IAClC4Q,GASN,SAASqlB,GACP7iB,EACA4e,EACAphB,GAkBA,GAfE4kB,GAAgBxD,GAGG,oBAAVA,IACTA,EAAQA,EAAMh0B,SAGhB03B,GAAe1D,EAAOphB,GACtB+kB,GAAgB3D,EAAOphB,GACvBilB,GAAoB7D,IAMfA,EAAMkE,QACLlE,EAAMla,UACR1E,EAAS6iB,GAAa7iB,EAAQ4e,EAAMla,QAASlH,IAE3CohB,EAAMnc,QACR,IAAK,IAAI5b,EAAI,EAAG+oB,EAAIgP,EAAMnc,OAAO1d,OAAQ8B,EAAI+oB,EAAG/oB,IAC9CmZ,EAAS6iB,GAAa7iB,EAAQ4e,EAAMnc,OAAO5b,GAAI2W,GAKrD,IACI3a,EADA+H,EAAU,GAEd,IAAK/H,KAAOmd,EACV+iB,EAAWlgC,GAEb,IAAKA,KAAO+7B,EACLl3B,EAAOsY,EAAQnd,IAClBkgC,EAAWlgC,GAGf,SAASkgC,EAAYlgC,GACnB,IAAImgC,EAAQ3B,GAAOx+B,IAAQk/B,GAC3Bn3B,EAAQ/H,GAAOmgC,EAAMhjB,EAAOnd,GAAM+7B,EAAM/7B,GAAM2a,EAAI3a,GAEpD,OAAO+H,EAQT,SAASq4B,GACPr4B,EACA2R,EACA2M,EACAga,GAGA,GAAkB,kBAAPha,EAAX,CAGA,IAAIia,EAASv4B,EAAQ2R,GAErB,GAAI7U,EAAOy7B,EAAQja,GAAO,OAAOia,EAAOja,GACxC,IAAIka,EAAcn7B,EAASihB,GAC3B,GAAIxhB,EAAOy7B,EAAQC,GAAgB,OAAOD,EAAOC,GACjD,IAAIC,EAAelM,EAAWiM,GAC9B,GAAI17B,EAAOy7B,EAAQE,GAAiB,OAAOF,EAAOE,GAElD,IAAIt6B,EAAMo6B,EAAOja,IAAOia,EAAOC,IAAgBD,EAAOE,GAOtD,OAN6CH,IAAgBn6B,GAC3DmR,GACE,qBAAuBqC,EAAK/X,MAAM,GAAI,GAAK,KAAO0kB,EAClDte,GAGG7B,GAOT,SAASu6B,GACPzgC,EACA0gC,EACA1Z,EACArM,GAEA,IAAIgmB,EAAOD,EAAY1gC,GACnB4gC,GAAU/7B,EAAOmiB,EAAWhnB,GAC5B+J,EAAQid,EAAUhnB,GAElB6gC,EAAeC,GAAazf,QAASsf,EAAKjnB,MAC9C,GAAImnB,GAAgB,EAClB,GAAID,IAAW/7B,EAAO87B,EAAM,WAC1B52B,GAAQ,OACH,GAAc,KAAVA,GAAgBA,IAAUyqB,EAAUx0B,GAAM,CAGnD,IAAI+gC,EAAcD,GAAar9B,OAAQk9B,EAAKjnB,OACxCqnB,EAAc,GAAKF,EAAeE,KACpCh3B,GAAQ,GAKd,QAAc0K,IAAV1K,EAAqB,CACvBA,EAAQi3B,GAAoBrmB,EAAIgmB,EAAM3gC,GAGtC,IAAIihC,EAAoB/D,GACxBC,IAAgB,GAChBM,GAAQ1zB,GACRozB,GAAgB8D,GASlB,OAFEC,GAAWP,EAAM3gC,EAAK+J,EAAO4Q,EAAIimB,GAE5B72B,EAMT,SAASi3B,GAAqBrmB,EAAIgmB,EAAM3gC,GAEtC,GAAK6E,EAAO87B,EAAM,WAAlB,CAGA,IAAItJ,EAAMsJ,EAAKlhB,QAYf,OAV6Chb,EAAS4yB,IACpDhgB,GACE,mCAAqCrX,EAArC,2FAGA2a,GAKAA,GAAMA,EAAG2K,SAAS0B,gBACWvS,IAA/BkG,EAAG2K,SAAS0B,UAAUhnB,SACHyU,IAAnBkG,EAAGwmB,OAAOnhC,GAEH2a,EAAGwmB,OAAOnhC,GAIG,oBAARq3B,GAA6C,aAAvB+J,GAAQT,EAAKjnB,MAC7C2d,EAAIzyB,KAAK+V,GACT0c,GAMN,SAAS6J,GACPP,EACA34B,EACA+B,EACA4Q,EACAimB,GAEA,GAAID,EAAKU,UAAYT,EACnBvpB,GACE,2BAA6BrP,EAAO,IACpC2S,QAIJ,GAAa,MAAT5Q,GAAkB42B,EAAKU,SAA3B,CAGA,IAAI3nB,EAAOinB,EAAKjnB,KACZ4nB,GAAS5nB,IAAiB,IAATA,EACjB6nB,EAAgB,GACpB,GAAI7nB,EAAM,CACHtT,MAAMC,QAAQqT,KACjBA,EAAO,CAACA,IAEV,IAAK,IAAI1V,EAAI,EAAGA,EAAI0V,EAAKxX,SAAWo/B,EAAOt9B,IAAK,CAC9C,IAAIw9B,EAAeC,GAAW13B,EAAO2P,EAAK1V,IAC1Cu9B,EAAc/6B,KAAKg7B,EAAaE,cAAgB,IAChDJ,EAAQE,EAAaF,OAIzB,GAAKA,EAAL,CAOA,IAAIK,EAAYhB,EAAKgB,UACjBA,IACGA,EAAU53B,IACbsN,GACE,yDAA2DrP,EAAO,KAClE2S,SAXJtD,GACEuqB,GAAsB55B,EAAM+B,EAAOw3B,GACnC5mB,IAeN,IAAIknB,GAAgB,4CAEpB,SAASJ,GAAY13B,EAAO2P,GAC1B,IAAI4nB,EACAI,EAAeN,GAAQ1nB,GAC3B,GAAImoB,GAAcl+B,KAAK+9B,GAAe,CACpC,IAAIn1B,SAAWxC,EACfu3B,EAAQ/0B,IAAMm1B,EAAatzB,cAEtBkzB,GAAe,WAAN/0B,IACZ+0B,EAAQv3B,aAAiB2P,QAG3B4nB,EAD0B,WAAjBI,EACD/8B,EAAcoF,GACI,UAAjB23B,EACDt7B,MAAMC,QAAQ0D,GAEdA,aAAiB2P,EAE3B,MAAO,CACL4nB,MAAOA,EACPI,aAAcA,GASlB,SAASN,GAAS78B,GAChB,IAAIyZ,EAAQzZ,GAAMA,EAAG7C,WAAWsc,MAAM,sBACtC,OAAOA,EAAQA,EAAM,GAAK,GAG5B,SAAS8jB,GAAY1U,EAAG8H,GACtB,OAAOkM,GAAQhU,KAAOgU,GAAQlM,GAGhC,SAAS4L,GAAcpnB,EAAM6nB,GAC3B,IAAKn7B,MAAMC,QAAQk7B,GACjB,OAAOO,GAAWP,EAAe7nB,GAAQ,GAAK,EAEhD,IAAK,IAAI1V,EAAI,EAAG0L,EAAM6xB,EAAcr/B,OAAQ8B,EAAI0L,EAAK1L,IACnD,GAAI89B,GAAWP,EAAcv9B,GAAI0V,GAC/B,OAAO1V,EAGX,OAAQ,EAGV,SAAS49B,GAAuB55B,EAAM+B,EAAOw3B,GAC3C,IAAI5+B,EAAU,6CAAgDqF,EAAhD,eACIu5B,EAAchgC,IAAI+yB,GAAYr0B,KAAK,MACjDyhC,EAAeH,EAAc,GAC7BQ,EAAerO,EAAU3pB,GACzBi4B,EAAgBC,GAAWl4B,EAAO23B,GAClCQ,EAAgBD,GAAWl4B,EAAOg4B,GAYtC,OAV6B,IAAzBR,EAAcr/B,QACdigC,GAAaT,KACZU,GAAUV,EAAcK,KAC3Bp/B,GAAW,eAAiBq/B,GAE9Br/B,GAAW,SAAWo/B,EAAe,IAEjCI,GAAaJ,KACfp/B,GAAW,cAAgBu/B,EAAgB,KAEtCv/B,EAGT,SAASs/B,GAAYl4B,EAAO2P,GAC1B,MAAa,WAATA,EACM,IAAO3P,EAAQ,IACL,WAAT2P,EACD,GAAM3O,OAAOhB,GAEb,GAAKA,EAIjB,SAASo4B,GAAcp4B,GACrB,IAAIs4B,EAAgB,CAAC,SAAU,SAAU,WACzC,OAAOA,EAAcC,MAAK,SAAUC,GAAQ,OAAOx4B,EAAMqE,gBAAkBm0B,KAG7E,SAASH,KACP,IAAIjyB,EAAO,GAAIT,EAAMkJ,UAAU1W,OAC/B,MAAQwN,IAAQS,EAAMT,GAAQkJ,UAAWlJ,GAEzC,OAAOS,EAAKmyB,MAAK,SAAUC,GAAQ,MAA8B,YAAvBA,EAAKn0B,iBAKjD,SAASo0B,GAAal5B,EAAKqR,EAAI8nB,GAG7BtI,KACA,IACE,GAAIxf,EAAI,CACN,IAAI+nB,EAAM/nB,EACV,MAAQ+nB,EAAMA,EAAIrd,QAAU,CAC1B,IAAI9e,EAAQm8B,EAAIpd,SAASqd,cACzB,GAAIp8B,EACF,IAAK,IAAIvC,EAAI,EAAGA,EAAIuC,EAAMrE,OAAQ8B,IAChC,IACE,IAAI4+B,GAAgD,IAAtCr8B,EAAMvC,GAAGY,KAAK89B,EAAKp5B,EAAKqR,EAAI8nB,GAC1C,GAAIG,EAAW,OACf,MAAOxpB,IACPypB,GAAkBzpB,GAAGspB,EAAK,wBAMpCG,GAAkBv5B,EAAKqR,EAAI8nB,GAC3B,QACAnI,MAIJ,SAASwI,GACP/c,EACA/E,EACA7Q,EACAwK,EACA8nB,GAEA,IAAIv8B,EACJ,IACEA,EAAMiK,EAAO4V,EAAQpO,MAAMqJ,EAAS7Q,GAAQ4V,EAAQnhB,KAAKoc,GACrD9a,IAAQA,EAAIyzB,QAAUpyB,EAAUrB,KAASA,EAAI68B,WAC/C78B,EAAImD,OAAM,SAAU+P,GAAK,OAAOopB,GAAYppB,EAAGuB,EAAI8nB,EAAO,uBAG1Dv8B,EAAI68B,UAAW,GAEjB,MAAO3pB,IACPopB,GAAYppB,GAAGuB,EAAI8nB,GAErB,OAAOv8B,EAGT,SAAS28B,GAAmBv5B,EAAKqR,EAAI8nB,GACnC,GAAIxM,EAAOM,aACT,IACE,OAAON,EAAOM,aAAa3xB,KAAK,KAAM0E,EAAKqR,EAAI8nB,GAC/C,MAAOrpB,IAGHA,KAAM9P,GACR05B,GAAS5pB,GAAG,KAAM,uBAIxB4pB,GAAS15B,EAAKqR,EAAI8nB,GAGpB,SAASO,GAAU15B,EAAKqR,EAAI8nB,GAK1B,GAHEprB,GAAM,YAAcorB,EAAO,MAAUn5B,EAAI5H,WAAc,IAAOiZ,IAG3Did,IAAaC,GAA8B,qBAAZzgB,QAGlC,MAAM9N,EAFN8N,QAAQ3U,MAAM6G,GAQlB,IAuBI25B,GAiFAC,GAxGAC,GAAY,GACZC,IAAU,EAEd,SAASC,KACPD,IAAU,EACV,IAAIE,EAASH,GAAUxhC,MAAM,GAC7BwhC,GAAUjhC,OAAS,EACnB,IAAK,IAAI8B,EAAI,EAAGA,EAAIs/B,EAAOphC,OAAQ8B,IACjCs/B,EAAOt/B,KAwBX,GAAuB,qBAAZ2D,SAA2B+wB,GAAS/wB,SAAU,CACvD,IAAIgnB,GAAIhnB,QAAQC,UAChBq7B,GAAY,WACVtU,GAAEnnB,KAAK67B,IAMHl5B,IAASo5B,WAAWz+B,SAErB,GAAKqzB,IAAoC,qBAArBqL,mBACzB9K,GAAS8K,mBAEuB,yCAAhCA,iBAAiB9hC,WAmBjBuhC,GAJiC,qBAAjBQ,cAAgC/K,GAAS+K,cAI7C,WACVA,aAAaJ,KAIH,WACVE,WAAWF,GAAgB,QAxB5B,CAID,IAAIK,GAAU,EACV5gB,GAAW,IAAI0gB,iBAAiBH,IAChCM,GAAWC,SAASC,eAAepgC,OAAOigC,KAC9C5gB,GAAS2a,QAAQkG,GAAU,CACzBG,eAAe,IAEjBb,GAAY,WACVS,IAAWA,GAAU,GAAK,EAC1BC,GAASr8B,KAAO7D,OAAOigC,KAgB3B,SAASK,GAAUC,EAAItrB,GACrB,IAAIurB,EAiBJ,GAhBAd,GAAU38B,MAAK,WACb,GAAIw9B,EACF,IACEA,EAAGp/B,KAAK8T,GACR,MAAOU,IACPopB,GAAYppB,GAAGV,EAAK,iBAEburB,GACTA,EAASvrB,MAGR0qB,KACHA,IAAU,EACVH,OAGGe,GAAyB,qBAAZr8B,QAChB,OAAO,IAAIA,SAAQ,SAAUC,GAC3Bq8B,EAAWr8B,KAYf,IAAIs8B,GAAiBlQ,EACnB,qMAMEmQ,GAAiB,SAAUtkC,EAAQG,GACrCqX,GACE,uBAA0BrX,EAA1B,kRAKAH,IAIAukC,GAAqB,SAAUvkC,EAAQG,GACzCqX,GACE,aAAgBrX,EAAM,kCAAsCA,EAA5D,iKAIAH,IAIAwkC,GACe,qBAAVxY,OAAyB6M,GAAS7M,OAE3C,GAAIwY,GAAU,CACZ,IAAIC,GAAoBtQ,EAAQ,+CAChCiC,EAAOS,SAAW,IAAI7K,MAAMoK,EAAOS,SAAU,CAC3C/oB,IAAK,SAAc9N,EAAQG,EAAK+J,GAC9B,OAAIu6B,GAAkBtkC,IACpBqX,GAAM,4DAA8DrX,IAC7D,IAEPH,EAAOG,GAAO+J,GACP,MAMf,IAAIw6B,GAAa,CACfxL,IAAK,SAAcl5B,EAAQG,GACzB,IAAI+4B,EAAM/4B,KAAOH,EACb2kC,EAAYN,GAAelkC,IACb,kBAARA,GAAsC,MAAlBA,EAAIiE,OAAO,MAAgBjE,KAAOH,EAAO4kC,OAKvE,OAJK1L,GAAQyL,IACPxkC,KAAOH,EAAO4kC,MAASL,GAAmBvkC,EAAQG,GAC/CmkC,GAAetkC,EAAQG,IAEzB+4B,IAAQyL,IAIfE,GAAa,CACfh3B,IAAK,SAAc7N,EAAQG,GAKzB,MAJmB,kBAARA,GAAsBA,KAAOH,IAClCG,KAAOH,EAAO4kC,MAASL,GAAmBvkC,EAAQG,GAC/CmkC,GAAetkC,EAAQG,IAEzBH,EAAOG,KAIlBkjC,GAAY,SAAoBvoB,GAC9B,GAAI0pB,GAAU,CAEZ,IAAIt8B,EAAU4S,EAAG2K,SACbqf,EAAW58B,EAAQ68B,QAAU78B,EAAQ68B,OAAOC,cAC5CH,GACAH,GACJ5pB,EAAGmqB,aAAe,IAAIjZ,MAAMlR,EAAIgqB,QAEhChqB,EAAGmqB,aAAenqB,GAOxB,IAmCIoqB,GACAC,GApCAC,GAAc,IAAIrM,GAOtB,SAASsM,GAAUpc,GACjBqc,GAAUrc,EAAKmc,IACfA,GAAYjM,QAGd,SAASmM,GAAWrc,EAAKsc,GACvB,IAAIphC,EAAGyB,EACH4/B,EAAMj/B,MAAMC,QAAQyiB,GACxB,MAAMuc,IAAQ5gC,EAASqkB,IAAS1kB,OAAOkhC,SAASxc,IAAQA,aAAe+R,IAAvE,CAGA,GAAI/R,EAAI+T,OAAQ,CACd,IAAI0I,EAAQzc,EAAI+T,OAAOE,IAAI1W,GAC3B,GAAI+e,EAAKrM,IAAIwM,GACX,OAEFH,EAAK7T,IAAIgU,GAEX,GAAIF,EAAK,CACPrhC,EAAI8kB,EAAI5mB,OACR,MAAO8B,IAAOmhC,GAAUrc,EAAI9kB,GAAIohC,OAC3B,CACL3/B,EAAOrB,OAAOqB,KAAKqjB,GACnB9kB,EAAIyB,EAAKvD,OACT,MAAO8B,IAAOmhC,GAAUrc,EAAIrjB,EAAKzB,IAAKohC,KAQxC,IAAII,GAAO5N,GAAa3L,OAAOqK,YAG7BkP,IACAA,GAAKT,MACLS,GAAKR,SACLQ,GAAKC,YACLD,GAAKE,gBAELX,GAAO,SAAUjK,GAAO,OAAO0K,GAAKT,KAAKjK,IACzCkK,GAAU,SAAUh9B,EAAM29B,EAAUC,GAClCJ,GAAKR,QAAQh9B,EAAM29B,EAAUC,GAC7BJ,GAAKC,WAAWE,GAChBH,GAAKC,WAAWG,KAQtB,IAAIC,GAAiB9gC,GAAO,SAAUiD,GACpC,IAAI89B,EAA6B,MAAnB99B,EAAK/D,OAAO,GAC1B+D,EAAO89B,EAAU99B,EAAKrG,MAAM,GAAKqG,EACjC,IAAI+9B,EAA6B,MAAnB/9B,EAAK/D,OAAO,GAC1B+D,EAAO+9B,EAAU/9B,EAAKrG,MAAM,GAAKqG,EACjC,IAAI46B,EAA6B,MAAnB56B,EAAK/D,OAAO,GAE1B,OADA+D,EAAO46B,EAAU56B,EAAKrG,MAAM,GAAKqG,EAC1B,CACLA,KAAMA,EACNke,KAAM6f,EACNnD,QAASA,EACTkD,QAASA,MAIb,SAASE,GAAiBC,EAAKtrB,GAC7B,SAASurB,IACP,IAAIC,EAAcvtB,UAEdqtB,EAAMC,EAAQD,IAClB,IAAI7/B,MAAMC,QAAQ4/B,GAOhB,OAAOnD,GAAwBmD,EAAK,KAAMrtB,UAAW+B,EAAI,gBALzD,IADA,IAAI2hB,EAAS2J,EAAItkC,QACRqC,EAAI,EAAGA,EAAIs4B,EAAOp6B,OAAQ8B,IACjC8+B,GAAwBxG,EAAOt4B,GAAI,KAAMmiC,EAAaxrB,EAAI,gBAQhE,OADAurB,EAAQD,IAAMA,EACPC,EAGT,SAASE,GACPC,EACAC,EACA/U,EACAgV,EACAC,EACA7rB,GAEA,IAAI3S,EAAc06B,EAAK+D,EAAKxpB,EAC5B,IAAKjV,KAAQq+B,EACF3D,EAAM2D,EAAGr+B,GAClBy+B,EAAMH,EAAMt+B,GACZiV,EAAQ4oB,GAAe79B,GACnBqrB,EAAQqP,GAC+BrrB,GACvC,8BAAkC4F,EAAU,KAAI,UAAaxZ,OAAOi/B,GACpE/nB,GAEO0Y,EAAQoT,IACbpT,EAAQqP,EAAIuD,OACdvD,EAAM2D,EAAGr+B,GAAQg+B,GAAgBtD,EAAK/nB,IAEpC4Y,EAAOtW,EAAMiJ,QACfwc,EAAM2D,EAAGr+B,GAAQw+B,EAAkBvpB,EAAMjV,KAAM06B,EAAKzlB,EAAM2lB,UAE5DrR,EAAItU,EAAMjV,KAAM06B,EAAKzlB,EAAM2lB,QAAS3lB,EAAM6oB,QAAS7oB,EAAM5V,SAChDq7B,IAAQ+D,IACjBA,EAAIR,IAAMvD,EACV2D,EAAGr+B,GAAQy+B,GAGf,IAAKz+B,KAAQs+B,EACPjT,EAAQgT,EAAGr+B,MACbiV,EAAQ4oB,GAAe79B,GACvBu+B,EAAUtpB,EAAMjV,KAAMs+B,EAAMt+B,GAAOiV,EAAM2lB,UAU/C,SAAS8D,GAA+Bp/B,EAAMqxB,EAAMzyB,EAAK8a,GACvD,IAAI0f,EAAc/H,EAAK5wB,QAAQ+X,WAAa6Y,EAAK5wB,QAAQ+X,UAAUoC,WACnE,GAAImR,EAAQqN,GACV,OAAOx6B,EAET,IAAImkB,EAAkBsO,EAAK5wB,QAAQ+X,UAAUuK,iBAAmB,GAC5Dsc,EAAQr/B,EAAKq/B,MACb3kB,EAAQ1a,EAAK0a,MACjB,GAAIsR,EAAMqT,IAAUrT,EAAMtR,GACxB,IAAK,IAAIhiB,KAAO0gC,EAAa,CAC3B,IAAIkG,EAASpS,EAAUx0B,GACnB+D,EAAS8iC,GAAU3gC,EAAK8b,EAAOhiB,EAAK4mC,GAAQ,IAC5CC,GAAU3gC,EAAKygC,EAAO3mC,EAAK4mC,GAAQ,GAGrC7iC,GACAmC,EAAIlG,KACiC,IAArCqqB,EAAgB7pB,QAAQomC,IACxB5lB,EAAQ5b,EAASc,EAAIlG,OAGrBkG,EAAIlG,GAAOghB,EAAQ5b,EAASc,EAAIlG,MAItC,OAAOkG,EAGT,SAAS4gC,GACPx/B,EACAqxB,EACAmC,EACA9Z,GAKA,IAAI0f,EAAc/H,EAAK5wB,QAAQia,MAC/B,GAAIqR,EAAQqN,GAEV,OAAOgG,GAA+Bp/B,EAAMqxB,EAAM,GAAI3X,GAExD,IAAI9a,EAAM,GACNygC,EAAQr/B,EAAKq/B,MACb3kB,EAAQ1a,EAAK0a,MACjB,GAAIsR,EAAMqT,IAAUrT,EAAMtR,GACxB,IAAK,IAAIhiB,KAAO0gC,EAAa,CAC3B,IAAIkG,EAASpS,EAAUx0B,GAEjB+mC,EAAiB/mC,EAAIoO,cAEvBpO,IAAQ+mC,GACRJ,GAAS9hC,EAAO8hC,EAAOI,IAEvB9N,GACE,SAAY8N,EAAiB,4BAC5B5N,GAAoB2B,GAAOnC,GAD5B,oCAEQ34B,EAFR,yKAK0C4mC,EAAS,iBAAqB5mC,EAAM,MAIpF6mC,GAAU3gC,EAAK8b,EAAOhiB,EAAK4mC,GAAQ,IACnCC,GAAU3gC,EAAKygC,EAAO3mC,EAAK4mC,GAAQ,GAIvC,OAAOF,GAA+Bp/B,EAAMqxB,EAAMzyB,EAAK8a,GAGzD,SAAS6lB,GACP3gC,EACA8gC,EACAhnC,EACA4mC,EACAK,GAEA,GAAI3T,EAAM0T,GAAO,CACf,GAAIniC,EAAOmiC,EAAMhnC,GAKf,OAJAkG,EAAIlG,GAAOgnC,EAAKhnC,GACXinC,UACID,EAAKhnC,IAEP,EACF,GAAI6E,EAAOmiC,EAAMJ,GAKtB,OAJA1gC,EAAIlG,GAAOgnC,EAAKJ,GACXK,UACID,EAAKJ,IAEP,EAGX,OAAO,EAiBT,SAASM,GAAyBnM,GAChC,IAAK,IAAI/2B,EAAI,EAAGA,EAAI+2B,EAAS74B,OAAQ8B,IACnC,GAAIoC,MAAMC,QAAQ00B,EAAS/2B,IACzB,OAAOoC,MAAMpD,UAAUmD,OAAOwR,MAAM,GAAIojB,GAG5C,OAAOA,EAOT,SAASoM,GAAmBpM,GAC1B,OAAOtH,EAAYsH,GACf,CAACoB,GAAgBpB,IACjB30B,MAAMC,QAAQ00B,GACZqM,GAAuBrM,QACvBtmB,EAGR,SAAS4yB,GAAYnL,GACnB,OAAO5I,EAAM4I,IAAS5I,EAAM4I,EAAK9L,OAASoD,EAAQ0I,EAAKR,WAGzD,SAAS0L,GAAwBrM,EAAUuM,GACzC,IACItjC,EAAGxC,EAAG+lC,EAAWvN,EADjB9zB,EAAM,GAEV,IAAKlC,EAAI,EAAGA,EAAI+2B,EAAS74B,OAAQ8B,IAC/BxC,EAAIu5B,EAAS/2B,GACTqvB,EAAQ7xB,IAAmB,mBAANA,IACzB+lC,EAAYrhC,EAAIhE,OAAS,EACzB83B,EAAO9zB,EAAIqhC,GAEPnhC,MAAMC,QAAQ7E,GACZA,EAAEU,OAAS,IACbV,EAAI4lC,GAAuB5lC,GAAK8lC,GAAe,IAAM,IAAMtjC,GAEvDqjC,GAAW7lC,EAAE,KAAO6lC,GAAWrN,KACjC9zB,EAAIqhC,GAAapL,GAAgBnC,EAAK5J,KAAQ5uB,EAAE,GAAI4uB,MACpD5uB,EAAEgmC,SAEJthC,EAAIM,KAAKmR,MAAMzR,EAAK1E,IAEbiyB,EAAYjyB,GACjB6lC,GAAWrN,GAIb9zB,EAAIqhC,GAAapL,GAAgBnC,EAAK5J,KAAO5uB,GAC9B,KAANA,GAET0E,EAAIM,KAAK21B,GAAgB36B,IAGvB6lC,GAAW7lC,IAAM6lC,GAAWrN,GAE9B9zB,EAAIqhC,GAAapL,GAAgBnC,EAAK5J,KAAO5uB,EAAE4uB,OAG3CmD,EAAOwH,EAAS0M,WAClBnU,EAAM9xB,EAAEs5B,MACRzH,EAAQ7xB,EAAExB,MACVszB,EAAMgU,KACN9lC,EAAExB,IAAM,UAAYsnC,EAAc,IAAMtjC,EAAI,MAE9CkC,EAAIM,KAAKhF,KAIf,OAAO0E,EAKT,SAASwhC,GAAa/sB,GACpB,IAAI2kB,EAAU3kB,EAAG2K,SAASga,QACtBA,IACF3kB,EAAGgtB,UAA+B,oBAAZrI,EAClBA,EAAQ16B,KAAK+V,GACb2kB,GAIR,SAASsI,GAAgBjtB,GACvB,IAAI5W,EAAS8jC,GAAcltB,EAAG2K,SAAS8Z,OAAQzkB,GAC3C5W,IACFo5B,IAAgB,GAChB/4B,OAAOqB,KAAK1B,GAAQ4B,SAAQ,SAAU3F,GAGlC49B,GAAkBjjB,EAAI3a,EAAK+D,EAAO/D,IAAM,WACtCqX,GACE,yJAEgCrX,EAAM,IACtC2a,SAORwiB,IAAgB,IAIpB,SAAS0K,GAAezI,EAAQzkB,GAC9B,GAAIykB,EAAQ,CAOV,IALA,IAAIr7B,EAASK,OAAOa,OAAO,MACvBQ,EAAOozB,GACPpK,QAAQqK,QAAQsG,GAChBh7B,OAAOqB,KAAK25B,GAEPp7B,EAAI,EAAGA,EAAIyB,EAAKvD,OAAQ8B,IAAK,CACpC,IAAIhE,EAAMyF,EAAKzB,GAEf,GAAY,WAARhE,EAAJ,CACA,IAAI8nC,EAAa1I,EAAOp/B,GAAK4tB,KACzBma,EAASptB,EACb,MAAOotB,EAAQ,CACb,GAAIA,EAAOJ,WAAa9iC,EAAOkjC,EAAOJ,UAAWG,GAAa,CAC5D/jC,EAAO/D,GAAO+nC,EAAOJ,UAAUG,GAC/B,MAEFC,EAASA,EAAO1iB,QAElB,IAAK0iB,EACH,GAAI,YAAa3I,EAAOp/B,GAAM,CAC5B,IAAIgoC,EAAiB5I,EAAOp/B,GAAKyf,QACjC1b,EAAO/D,GAAiC,oBAAnBgoC,EACjBA,EAAepjC,KAAK+V,GACpBqtB,OAEJ3wB,GAAM,cAAiBrX,EAAM,cAAiB2a,IAIpD,OAAO5W,GAWX,SAASkkC,GACPlN,EACA/Z,GAEA,IAAK+Z,IAAaA,EAAS74B,OACzB,MAAO,GAGT,IADA,IAAIgmC,EAAQ,GACHlkC,EAAI,EAAG+oB,EAAIgO,EAAS74B,OAAQ8B,EAAI+oB,EAAG/oB,IAAK,CAC/C,IAAI+3B,EAAQhB,EAAS/2B,GACjBsD,EAAOy0B,EAAMz0B,KAOjB,GALIA,GAAQA,EAAKq/B,OAASr/B,EAAKq/B,MAAMzf,aAC5B5f,EAAKq/B,MAAMzf,KAIf6U,EAAM/a,UAAYA,GAAW+a,EAAMZ,YAAcna,IACpD1Z,GAAqB,MAAbA,EAAK4f,KAWV6U,EAAMH,WAAaG,EAAMH,UAAUt0B,MAAsC,SAA9By0B,EAAMH,UAAUt0B,KAAK4f,MAChEghB,EAAM,UAAYA,EAAM,QAAU,KAAK1hC,KAAKu1B,IAE5CmM,EAAMzoB,UAAYyoB,EAAMzoB,QAAU,KAAKjZ,KAAKu1B,OAb/C,CACA,IAAI/zB,EAAOV,EAAK4f,KACZA,EAAQghB,EAAMlgC,KAAUkgC,EAAMlgC,GAAQ,IACxB,aAAd+zB,EAAMjB,IACR5T,EAAK1gB,KAAKmR,MAAMuP,EAAM6U,EAAMhB,UAAY,IAExC7T,EAAK1gB,KAAKu1B,IAYhB,IAAK,IAAIoM,KAAUD,EACbA,EAAMC,GAAQvrB,MAAMwrB,YACfF,EAAMC,GAGjB,OAAOD,EAGT,SAASE,GAAclM,GACrB,OAAQA,EAAKR,YAAcQ,EAAKjB,cAA+B,MAAdiB,EAAK9L,KAKxD,SAASiY,GACPH,EACAI,EACAC,GAEA,IAAIriC,EACAsiC,EAAiBpkC,OAAOqB,KAAK6iC,GAAapmC,OAAS,EACnDumC,EAAWP,IAAUA,EAAMQ,SAAWF,EACtCxoC,EAAMkoC,GAASA,EAAMS,KACzB,GAAKT,EAEE,IAAIA,EAAMU,YAEf,OAAOV,EAAMU,YACR,GACLH,GACAF,GACAA,IAAcnV,GACdpzB,IAAQuoC,EAAUI,OACjBH,IACAD,EAAUM,WAIX,OAAON,EAGP,IAAK,IAAIpJ,KADTj5B,EAAM,GACYgiC,EACZA,EAAM/I,IAAuB,MAAbA,EAAM,KACxBj5B,EAAIi5B,GAAS2J,GAAoBR,EAAanJ,EAAO+I,EAAM/I,UAnB/Dj5B,EAAM,GAwBR,IAAK,IAAI6iC,KAAST,EACVS,KAAS7iC,IACbA,EAAI6iC,GAASC,GAAgBV,EAAaS,IAW9C,OANIb,GAAS9jC,OAAOsZ,aAAawqB,KAC/B,EAAQU,YAAc1iC,GAExBmxB,EAAInxB,EAAK,UAAWuiC,GACpBpR,EAAInxB,EAAK,OAAQlG,GACjBq3B,EAAInxB,EAAK,aAAcsiC,GAChBtiC,EAGT,SAAS4iC,GAAoBR,EAAatoC,EAAKuE,GAC7C,IAAIo7B,EAAa,WACf,IAAIz5B,EAAM0S,UAAU1W,OAASqC,EAAGoT,MAAM,KAAMiB,WAAarU,EAAG,IAI5D,OAHA2B,EAAMA,GAAsB,kBAARA,IAAqBE,MAAMC,QAAQH,GACnD,CAACA,GACDihC,GAAkBjhC,GACfA,IACU,IAAfA,EAAIhE,QACY,IAAfgE,EAAIhE,QAAgBgE,EAAI,GAAGw1B,gBAC1BjnB,EACAvO,GAYN,OAPI3B,EAAG0kC,OACL7kC,OAAOqJ,eAAe66B,EAAatoC,EAAK,CACtC0N,IAAKiyB,EACLpiB,YAAY,EACZD,cAAc,IAGXqiB,EAGT,SAASqJ,GAAgBd,EAAOloC,GAC9B,OAAO,WAAc,OAAOkoC,EAAMloC,IAQpC,SAASkpC,GACPpgB,EACA8b,GAEA,IAAI/f,EAAK7gB,EAAG+oB,EAAGtnB,EAAMzF,EACrB,GAAIoG,MAAMC,QAAQyiB,IAAuB,kBAARA,EAE/B,IADAjE,EAAM,IAAIze,MAAM0iB,EAAI5mB,QACf8B,EAAI,EAAG+oB,EAAIjE,EAAI5mB,OAAQ8B,EAAI+oB,EAAG/oB,IACjC6gB,EAAI7gB,GAAK4gC,EAAO9b,EAAI9kB,GAAIA,EAAGA,EAAGA,QAE3B,GAAmB,kBAAR8kB,EAEhB,IADAjE,EAAM,IAAIze,MAAM0iB,GACX9kB,EAAI,EAAGA,EAAI8kB,EAAK9kB,IACnB6gB,EAAI7gB,GAAK4gC,EAAO5gC,EAAI,EAAGA,EAAGA,EAAGA,QAE1B,GAAIS,EAASqkB,GAClB,GAAI+P,IAAa/P,EAAIkE,OAAOC,UAAW,CACrCpI,EAAM,GACN,IAAIoI,EAAWnE,EAAIkE,OAAOC,YACtBlpB,EAASkpB,EAASM,OACtB,OAAQxpB,EAAOypB,KACb3I,EAAIre,KAAKo+B,EAAO7gC,EAAOgG,MAAO8a,EAAI3iB,OAAQ8B,EAAGA,MAC7CD,EAASkpB,EAASM,YAKpB,IAFA9nB,EAAOrB,OAAOqB,KAAKqjB,GACnBjE,EAAM,IAAIze,MAAMX,EAAKvD,QAChB8B,EAAI,EAAG+oB,EAAItnB,EAAKvD,OAAQ8B,EAAI+oB,EAAG/oB,IAClChE,EAAMyF,EAAKzB,GACX6gB,EAAI7gB,GAAK4gC,EAAO9b,EAAI9oB,GAAMA,EAAKgE,EAAGA,GAQxC,OAJKsvB,EAAMzO,KACTA,EAAM,IAER,EAAM4iB,UAAW,EACV5iB,EAQT,SAASskB,GACPnhC,EACAohC,EACApnB,EACAqnB,GAEA,IACIC,EADAC,EAAexpC,KAAK4gB,aAAa3Y,GAEjCuhC,GACFvnB,EAAQA,GAAS,GACbqnB,IAC4C5kC,EAAS4kC,IACrDhyB,GACE,iDACAtX,MAGJiiB,EAAQ1B,EAAOA,EAAO,GAAI+oB,GAAarnB,IAGzCsnB,EAAQC,EAAavnB,EAAOjiB,KAAMiiB,EAAMwnB,KAAOJ,GAE/CE,EAAQvpC,KAAK0gB,OAAOzY,IAASohC,EAG/B,IAAIvpC,EAASmiB,GAASA,EAAMkF,KAC5B,OAAIrnB,EACKE,KAAK0pC,eAAe,WAAY,CAAEviB,KAAMrnB,GAAUypC,GAElDA,EASX,SAASI,GAAerjB,GACtB,OAAO+Z,GAAargC,KAAKulB,SAAU,UAAWe,GAAI,IAAS+O,EAK7D,SAASuU,GAAeC,EAAQC,GAC9B,OAAIzjC,MAAMC,QAAQujC,IACmB,IAA5BA,EAAOppC,QAAQqpC,GAEfD,IAAWC,EAStB,SAASC,GACPC,EACA/pC,EACAgqC,EACAC,EACAC,GAEA,IAAIC,EAAgBlU,EAAOS,SAAS12B,IAAQgqC,EAC5C,OAAIE,GAAkBD,IAAiBhU,EAAOS,SAAS12B,GAC9C2pC,GAAcO,EAAgBD,GAC5BE,EACFR,GAAcQ,EAAeJ,GAC3BE,EACFzV,EAAUyV,KAAkBjqC,OAD9B,EAUT,SAASoqC,GACP9iC,EACAwzB,EACA/wB,EACAsgC,EACAC,GAEA,GAAIvgC,EACF,GAAKtF,EAASsF,GAKP,CAIL,IAAIi9B,EAHA5gC,MAAMC,QAAQ0D,KAChBA,EAAQkrB,EAASlrB,IAGnB,IAAI+G,EAAO,SAAW9Q,GACpB,GACU,UAARA,GACQ,UAARA,GACAo0B,EAAoBp0B,GAEpBgnC,EAAO1/B,MACF,CACL,IAAIoS,EAAOpS,EAAKq/B,OAASr/B,EAAKq/B,MAAMjtB,KACpCstB,EAAOqD,GAAUpU,EAAOe,YAAY8D,EAAKphB,EAAM1Z,GAC3CsH,EAAKijC,WAAajjC,EAAKijC,SAAW,IAClCjjC,EAAKq/B,QAAUr/B,EAAKq/B,MAAQ,IAElC,IAAI6D,EAAeplC,EAASpF,GACxByqC,EAAgBjW,EAAUx0B,GAC9B,KAAMwqC,KAAgBxD,MAAWyD,KAAiBzD,KAChDA,EAAKhnC,GAAO+J,EAAM/J,GAEdsqC,GAAQ,CACV,IAAIjE,EAAK/+B,EAAK++B,KAAO/+B,EAAK++B,GAAK,IAC/BA,EAAI,UAAYrmC,GAAQ,SAAU0qC,GAChC3gC,EAAM/J,GAAO0qC,KAMrB,IAAK,IAAI1qC,KAAO+J,EAAO+G,EAAM9Q,QApCYqX,GACvC,2DACAtX,MAqCN,OAAOuH,EAQT,SAASqjC,GACPhkC,EACAikC,GAEA,IAAI7lC,EAAShF,KAAK8qC,eAAiB9qC,KAAK8qC,aAAe,IACnD/Q,EAAO/0B,EAAO4B,GAGlB,OAAImzB,IAAS8Q,IAIb9Q,EAAO/0B,EAAO4B,GAAS5G,KAAKulB,SAASwlB,gBAAgBnkC,GAAO/B,KAC1D7E,KAAK+kC,aACL,KACA/kC,MAEFgrC,GAAWjR,EAAO,aAAenzB,GAAQ,IARhCmzB,EAgBX,SAASkR,GACPlR,EACAnzB,EACA3G,GAGA,OADA+qC,GAAWjR,EAAO,WAAanzB,GAAS3G,EAAO,IAAMA,EAAO,KAAM,GAC3D85B,EAGT,SAASiR,GACPjR,EACA95B,EACA4lB,GAEA,GAAIxf,MAAMC,QAAQyzB,GAChB,IAAK,IAAI91B,EAAI,EAAGA,EAAI81B,EAAK53B,OAAQ8B,IAC3B81B,EAAK91B,IAAyB,kBAAZ81B,EAAK91B,IACzBinC,GAAenR,EAAK91B,GAAKhE,EAAM,IAAMgE,EAAI4hB,QAI7CqlB,GAAenR,EAAM95B,EAAK4lB,GAI9B,SAASqlB,GAAgB/O,EAAMl8B,EAAK4lB,GAClCsW,EAAKV,UAAW,EAChBU,EAAKl8B,IAAMA,EACXk8B,EAAKtW,OAASA,EAKhB,SAASslB,GAAqB5jC,EAAMyC,GAClC,GAAIA,EACF,GAAKpF,EAAcoF,GAKZ,CACL,IAAIs8B,EAAK/+B,EAAK++B,GAAK/+B,EAAK++B,GAAK/lB,EAAO,GAAIhZ,EAAK++B,IAAM,GACnD,IAAK,IAAIrmC,KAAO+J,EAAO,CACrB,IAAIohC,EAAW9E,EAAGrmC,GACdorC,EAAOrhC,EAAM/J,GACjBqmC,EAAGrmC,GAAOmrC,EAAW,GAAGhlC,OAAOglC,EAAUC,GAAQA,QATV/zB,GACvC,gDACAtX,MAWN,OAAOuH,EAKT,SAAS+jC,GACPpF,EACA//B,EAEAolC,EACAC,GAEArlC,EAAMA,GAAO,CAAEwiC,SAAU4C,GACzB,IAAK,IAAItnC,EAAI,EAAGA,EAAIiiC,EAAI/jC,OAAQ8B,IAAK,CACnC,IAAIkjB,EAAO+e,EAAIjiC,GACXoC,MAAMC,QAAQ6gB,GAChBmkB,GAAmBnkB,EAAMhhB,EAAKolC,GACrBpkB,IAELA,EAAK+hB,QACP/hB,EAAK3iB,GAAG0kC,OAAQ,GAElB/iC,EAAIghB,EAAKlnB,KAAOknB,EAAK3iB,IAMzB,OAHIgnC,IACF,EAAM5C,KAAO4C,GAERrlC,EAKT,SAASslC,GAAiBC,EAASz+B,GACjC,IAAK,IAAIhJ,EAAI,EAAGA,EAAIgJ,EAAO9K,OAAQ8B,GAAK,EAAG,CACzC,IAAIhE,EAAMgN,EAAOhJ,GACE,kBAARhE,GAAoBA,EAC7ByrC,EAAQz+B,EAAOhJ,IAAMgJ,EAAOhJ,EAAI,GAC0B,KAARhE,GAAsB,OAARA,GAEhEqX,GACG,2EAA6ErX,EAC9ED,MAIN,OAAO0rC,EAMT,SAASC,GAAiB3hC,EAAO4hC,GAC/B,MAAwB,kBAAV5hC,EAAqB4hC,EAAS5hC,EAAQA,EAKtD,SAAS6hC,GAAsB/rC,GAC7BA,EAAOgsC,GAAKb,GACZnrC,EAAOisC,GAAK/X,EACZl0B,EAAOksC,GAAKrqC,EACZ7B,EAAOmsC,GAAK9C,GACZrpC,EAAOosC,GAAK9C,GACZtpC,EAAOqsC,GAAK7W,EACZx1B,EAAO2pC,GAAK3T,EACZh2B,EAAOyV,GAAKq1B,GACZ9qC,EAAOssC,GAAKzC,GACZ7pC,EAAOusC,GAAKtC,GACZjqC,EAAOwsC,GAAKjC,GACZvqC,EAAOysC,GAAKnQ,GACZt8B,EAAO0sC,GAAKtQ,GACZp8B,EAAO2sC,GAAKnB,GACZxrC,EAAO4sC,GAAKvB,GACZrrC,EAAO6sC,GAAKlB,GACZ3rC,EAAO8sC,GAAKjB,GAKd,SAASkB,GACPtlC,EACA0a,EACA+Y,EACA5d,EACAwb,GAEA,IAKIkU,EALAC,EAAS/sC,KAETgI,EAAU4wB,EAAK5wB,QAIflD,EAAOsY,EAAQ,SACjB0vB,EAAYzoC,OAAOa,OAAOkY,GAE1B0vB,EAAUE,UAAY5vB,IAKtB0vB,EAAY1vB,EAEZA,EAASA,EAAO4vB,WAElB,IAAIC,EAAazZ,EAAOxrB,EAAQklC,WAC5BC,GAAqBF,EAEzBjtC,KAAKuH,KAAOA,EACZvH,KAAKiiB,MAAQA,EACbjiB,KAAKg7B,SAAWA,EAChBh7B,KAAKod,OAASA,EACdpd,KAAKotC,UAAY7lC,EAAK++B,IAAMjT,EAC5BrzB,KAAKqtC,WAAavF,GAAc9/B,EAAQq3B,OAAQjiB,GAChDpd,KAAKmoC,MAAQ,WAOX,OANK4E,EAAOrsB,QACV4nB,GACE/gC,EAAK+lC,YACLP,EAAOrsB,OAASwnB,GAAalN,EAAU5d,IAGpC2vB,EAAOrsB,QAGhBrc,OAAOqJ,eAAe1N,KAAM,cAAe,CACzCwd,YAAY,EACZ7P,IAAK,WACH,OAAO26B,GAAqB/gC,EAAK+lC,YAAattC,KAAKmoC,YAKnD8E,IAEFjtC,KAAKulB,SAAWvd,EAEhBhI,KAAK0gB,OAAS1gB,KAAKmoC,QACnBnoC,KAAK4gB,aAAe0nB,GAAqB/gC,EAAK+lC,YAAattC,KAAK0gB,SAG9D1Y,EAAQulC,SACVvtC,KAAKwtC,GAAK,SAAUngB,EAAG8H,EAAG1zB,EAAGgsC,GAC3B,IAAInR,EAAQoR,GAAcZ,EAAWzf,EAAG8H,EAAG1zB,EAAGgsC,EAAGN,GAKjD,OAJI7Q,IAAUj2B,MAAMC,QAAQg2B,KAC1BA,EAAMhB,UAAYtzB,EAAQulC,SAC1BjR,EAAMlB,UAAYhe,GAEbkf,GAGTt8B,KAAKwtC,GAAK,SAAUngB,EAAG8H,EAAG1zB,EAAGgsC,GAAK,OAAOC,GAAcZ,EAAWzf,EAAG8H,EAAG1zB,EAAGgsC,EAAGN,IAMlF,SAASQ,GACP/U,EACA3R,EACA1f,EACAulC,EACA9R,GAEA,IAAIhzB,EAAU4wB,EAAK5wB,QACfia,EAAQ,GACR0e,EAAc34B,EAAQia,MAC1B,GAAIsR,EAAMoN,GACR,IAAK,IAAI1gC,KAAO0gC,EACd1e,EAAMhiB,GAAOygC,GAAazgC,EAAK0gC,EAAa1Z,GAAaoM,QAGvDE,EAAMhsB,EAAKq/B,QAAUgH,GAAW3rB,EAAO1a,EAAKq/B,OAC5CrT,EAAMhsB,EAAK0a,QAAU2rB,GAAW3rB,EAAO1a,EAAK0a,OAGlD,IAAI4rB,EAAgB,IAAIhB,GACtBtlC,EACA0a,EACA+Y,EACA8R,EACAlU,GAGE0D,EAAQt0B,EAAQ68B,OAAOhgC,KAAK,KAAMgpC,EAAcL,GAAIK,GAExD,GAAIvR,aAAiBxB,GACnB,OAAOgT,GAA6BxR,EAAO/0B,EAAMsmC,EAAczwB,OAAQpV,EAAS6lC,GAC3E,GAAIxnC,MAAMC,QAAQg2B,GAAQ,CAG/B,IAFA,IAAIyR,EAAS3G,GAAkB9K,IAAU,GACrCn2B,EAAM,IAAIE,MAAM0nC,EAAO5rC,QAClB8B,EAAI,EAAGA,EAAI8pC,EAAO5rC,OAAQ8B,IACjCkC,EAAIlC,GAAK6pC,GAA6BC,EAAO9pC,GAAIsD,EAAMsmC,EAAczwB,OAAQpV,EAAS6lC,GAExF,OAAO1nC,GAIX,SAAS2nC,GAA8BxR,EAAO/0B,EAAMulC,EAAW9kC,EAAS6lC,GAItE,IAAIG,EAAQ3R,GAAWC,GASvB,OARA0R,EAAM5S,UAAY0R,EAClBkB,EAAM3S,UAAYrzB,GAEfgmC,EAAMC,aAAeD,EAAMC,cAAgB,IAAIJ,cAAgBA,EAE9DtmC,EAAK4f,QACN6mB,EAAMzmC,OAASymC,EAAMzmC,KAAO,KAAK4f,KAAO5f,EAAK4f,MAEzC6mB,EAGT,SAASJ,GAAY5Y,EAAInH,GACvB,IAAK,IAAI5tB,KAAO4tB,EACdmH,EAAG3vB,EAASpF,IAAQ4tB,EAAK5tB,GA7D7B4rC,GAAqBgB,GAAwB5pC,WA0E7C,IAAIirC,GAAsB,CACxBC,KAAM,SAAe7R,EAAO8R,GAC1B,GACE9R,EAAMf,oBACLe,EAAMf,kBAAkB8S,cACzB/R,EAAM/0B,KAAK+mC,UACX,CAEA,IAAIC,EAAcjS,EAClB4R,GAAoBM,SAASD,EAAaA,OACrC,CACL,IAAIvS,EAAQM,EAAMf,kBAAoBkT,GACpCnS,EACAoS,IAEF1S,EAAMrS,OAAOykB,EAAY9R,EAAMrB,SAAMvmB,EAAW05B,KAIpDI,SAAU,SAAmBG,EAAUrS,GACrC,IAAIt0B,EAAUs0B,EAAM/S,iBAChByS,EAAQM,EAAMf,kBAAoBoT,EAASpT,kBAC/CqT,GACE5S,EACAh0B,EAAQif,UACRjf,EAAQolC,UACR9Q,EACAt0B,EAAQgzB,WAIZ6T,OAAQ,SAAiBvS,GACvB,IAAIrb,EAAUqb,EAAMrb,QAChBsa,EAAoBe,EAAMf,kBACzBA,EAAkBpT,aACrBzB,GAAS6U,EAAmB,oBAC5B7U,GAAS6U,EAAmB,qBAC5BA,EAAkBpT,YAAa,EAC/BzB,GAAS6U,EAAmB,YAE1Be,EAAM/0B,KAAK+mC,YACTrtB,EAAQkH,WAMV2mB,GAAwBvT,GAExBwT,GAAuBxT,GAAmB,KAKhDyT,QAAS,SAAkB1S,GACzB,IAAIf,EAAoBe,EAAMf,kBACzBA,EAAkB8S,eAChB/R,EAAM/0B,KAAK+mC,UAGdW,GAAyB1T,GAAmB,GAF5CA,EAAkBzR,cAQtBolB,GAAe7qC,OAAOqB,KAAKwoC,IAE/B,SAAS/iB,GACPyN,EACArxB,EACA0Z,EACA+Z,EACAD,GAEA,IAAIzH,EAAQsF,GAAZ,CAIA,IAAIuW,EAAWluB,EAAQsE,SAAS2a,MAShC,GANIx7B,EAASk0B,KACXA,EAAOuW,EAAS5uB,OAAOqY,IAKL,oBAATA,EAAX,CAQA,IAAIsC,EACJ,GAAI5H,EAAQsF,EAAK5f,OACfkiB,EAAetC,EACfA,EAAOwW,GAAsBlU,EAAciU,QAC9Bz6B,IAATkkB,GAIF,OAAOyW,GACLnU,EACA3zB,EACA0Z,EACA+Z,EACAD,GAKNxzB,EAAOA,GAAQ,GAIf+nC,GAA0B1W,GAGtBrF,EAAMhsB,EAAK4K,QACbo9B,GAAe3W,EAAK5wB,QAAST,GAI/B,IAAI0f,EAAY8f,GAA0Bx/B,EAAMqxB,EAAMmC,EAAK9Z,GAG3D,GAAIuS,EAAOoF,EAAK5wB,QAAQwnC,YACtB,OAAO7B,GAA0B/U,EAAM3R,EAAW1f,EAAM0Z,EAAS+Z,GAKnE,IAAIoS,EAAY7lC,EAAK++B,GAKrB,GAFA/+B,EAAK++B,GAAK/+B,EAAKkoC,SAEXjc,EAAOoF,EAAK5wB,QAAQ0nC,UAAW,CAKjC,IAAIvoB,EAAO5f,EAAK4f,KAChB5f,EAAO,GACH4f,IACF5f,EAAK4f,KAAOA,GAKhBwoB,GAAsBpoC,GAGtB,IAAIU,EAAO2wB,EAAK5wB,QAAQC,MAAQ8yB,EAC5BuB,EAAQ,IAAIxB,GACb,iBAAoBlC,EAAQ,KAAK3wB,EAAQ,IAAMA,EAAQ,IACxDV,OAAMmN,OAAWA,OAAWA,EAAWuM,EACvC,CAAE2X,KAAMA,EAAM3R,UAAWA,EAAWmmB,UAAWA,EAAWrS,IAAKA,EAAKC,SAAUA,GAC9EE,GAGF,OAAOoB,EA1EHhlB,GAAM,iCAAoC5T,OAAOk1B,GAAS3X,IA6EhE,SAASwtB,GACPnS,EACAlf,GAEA,IAAIpV,EAAU,CACZ4nC,cAAc,EACdC,aAAcvT,EACdlf,OAAQA,GAGN0yB,EAAiBxT,EAAM/0B,KAAKuoC,eAKhC,OAJIvc,EAAMuc,KACR9nC,EAAQ68B,OAASiL,EAAejL,OAChC78B,EAAQ+iC,gBAAkB+E,EAAe/E,iBAEpC,IAAIzO,EAAM/S,iBAAiBqP,KAAK5wB,GAGzC,SAAS2nC,GAAuBpoC,GAE9B,IADA,IAAIf,EAAQe,EAAKZ,OAASY,EAAKZ,KAAO,IAC7B1C,EAAI,EAAGA,EAAIirC,GAAa/sC,OAAQ8B,IAAK,CAC5C,IAAIhE,EAAMivC,GAAajrC,GACnBmnC,EAAW5kC,EAAMvG,GACjB8vC,EAAU7B,GAAoBjuC,GAC9BmrC,IAAa2E,GAAa3E,GAAYA,EAAS4E,UACjDxpC,EAAMvG,GAAOmrC,EAAW6E,GAAYF,EAAS3E,GAAY2E,IAK/D,SAASE,GAAaC,EAAIC,GACxB,IAAIC,EAAS,SAAU/iB,EAAG8H,GAExB+a,EAAG7iB,EAAG8H,GACNgb,EAAG9iB,EAAG8H,IAGR,OADAib,EAAOJ,SAAU,EACVI,EAKT,SAASb,GAAgBvnC,EAAST,GAChC,IAAIq5B,EAAQ54B,EAAQmK,OAASnK,EAAQmK,MAAMyuB,MAAS,QAChD1jB,EAASlV,EAAQmK,OAASnK,EAAQmK,MAAM+K,OAAU,SACpD3V,EAAKq/B,QAAUr/B,EAAKq/B,MAAQ,KAAKhG,GAAQr5B,EAAK4K,MAAMnI,MACtD,IAAIs8B,EAAK/+B,EAAK++B,KAAO/+B,EAAK++B,GAAK,IAC3B8E,EAAW9E,EAAGppB,GACdpV,EAAWP,EAAK4K,MAAMrK,SACtByrB,EAAM6X,IAEN/kC,MAAMC,QAAQ8kC,IACsB,IAAhCA,EAAS3qC,QAAQqH,GACjBsjC,IAAatjC,KAEjBw+B,EAAGppB,GAAS,CAACpV,GAAU1B,OAAOglC,IAGhC9E,EAAGppB,GAASpV,EAMhB,IAAIuoC,GAAmB,EACnBC,GAAmB,EAIvB,SAAS5C,GACPzsB,EACA8Z,EACAxzB,EACAyzB,EACAuV,EACAC,GAUA,OARInqC,MAAMC,QAAQiB,IAASmsB,EAAYnsB,MACrCgpC,EAAoBvV,EACpBA,EAAWzzB,EACXA,OAAOmN,GAEL8e,EAAOgd,KACTD,EAAoBD,IAEfG,GAAexvB,EAAS8Z,EAAKxzB,EAAMyzB,EAAUuV,GAGtD,SAASE,GACPxvB,EACA8Z,EACAxzB,EACAyzB,EACAuV,GAEA,GAAIhd,EAAMhsB,IAASgsB,EAAM,EAAOuJ,QAM9B,OALyCxlB,GACvC,mDAAsD9U,KAAK2e,UAAU5Z,GAArE,2DAEA0Z,GAEKib,KAMT,GAHI3I,EAAMhsB,IAASgsB,EAAMhsB,EAAK2e,MAC5B6U,EAAMxzB,EAAK2e,KAER6U,EAEH,OAAOmB,KA2BT,IAAII,EAAOnB,EAELvC,GAzBJrF,EAAMhsB,IAASgsB,EAAMhsB,EAAKtH,OAASyzB,EAAYnsB,EAAKtH,MAGlDqX,GACE,2EAEA2J,GAKF5a,MAAMC,QAAQ00B,IACO,oBAAhBA,EAAS,KAEhBzzB,EAAOA,GAAQ,GACfA,EAAK+lC,YAAc,CAAE5tB,QAASsb,EAAS,IACvCA,EAAS74B,OAAS,GAEhBouC,IAAsBD,GACxBtV,EAAWoM,GAAkBpM,GACpBuV,IAAsBF,KAC/BrV,EAAWmM,GAAwBnM,IAGlB,kBAARD,IAETI,EAAMla,EAAQyvB,QAAUzvB,EAAQyvB,OAAOvV,IAAOjF,EAAOa,gBAAgBgE,GACjE7E,EAAOU,cAAcmE,IAEsBxH,EAAMhsB,IAASgsB,EAAMhsB,EAAKkoC,WACrEn4B,GACG,iFAAmFyjB,EAAM,KAC1F9Z,GAGJqb,EAAQ,IAAIxB,GACV5E,EAAOc,qBAAqB+D,GAAMxzB,EAAMyzB,OACxCtmB,OAAWA,EAAWuM,IASxBqb,EAPW/0B,GAASA,EAAKopC,MAAQpd,EAAMqF,EAAOyH,GAAapf,EAAQsE,SAAU,aAAcwV,IAOnF,IAAID,GACVC,EAAKxzB,EAAMyzB,OACXtmB,OAAWA,EAAWuM,GAPhBkK,GAAgByN,EAAMrxB,EAAM0Z,EAAS+Z,EAAUD,IAYzDuB,EAAQnR,GAAgB4P,EAAKxzB,EAAM0Z,EAAS+Z,GAE9C,OAAI30B,MAAMC,QAAQg2B,GACTA,EACE/I,EAAM+I,IACX/I,EAAM4H,IAAOyV,GAAQtU,EAAOnB,GAC5B5H,EAAMhsB,IAASspC,GAAqBtpC,GACjC+0B,GAEAJ,KAIX,SAAS0U,GAAStU,EAAOnB,EAAI2V,GAO3B,GANAxU,EAAMnB,GAAKA,EACO,kBAAdmB,EAAMvB,MAERI,OAAKzmB,EACLo8B,GAAQ,GAENvd,EAAM+I,EAAMtB,UACd,IAAK,IAAI/2B,EAAI,EAAG+oB,EAAIsP,EAAMtB,SAAS74B,OAAQ8B,EAAI+oB,EAAG/oB,IAAK,CACrD,IAAI+3B,EAAQM,EAAMtB,SAAS/2B,GACvBsvB,EAAMyI,EAAMjB,OACdzH,EAAQ0I,EAAMb,KAAQ3H,EAAOsd,IAAwB,QAAd9U,EAAMjB,MAC7C6V,GAAQ5U,EAAOb,EAAI2V,IAS3B,SAASD,GAAsBtpC,GACzB7C,EAAS6C,EAAKwpC,QAChB5L,GAAS59B,EAAKwpC,OAEZrsC,EAAS6C,EAAKypC,QAChB7L,GAAS59B,EAAKypC,OAMlB,SAASC,GAAYr2B,GACnBA,EAAGs2B,OAAS,KACZt2B,EAAGkwB,aAAe,KAClB,IAAI9iC,EAAU4S,EAAG2K,SACb4rB,EAAcv2B,EAAG81B,OAAS1oC,EAAQ6nC,aAClChC,EAAgBsD,GAAeA,EAAYlwB,QAC/CrG,EAAG8F,OAASwnB,GAAalgC,EAAQopC,gBAAiBvD,GAClDjzB,EAAGgG,aAAeyS,EAKlBzY,EAAG4yB,GAAK,SAAUngB,EAAG8H,EAAG1zB,EAAGgsC,GAAK,OAAOC,GAAc9yB,EAAIyS,EAAG8H,EAAG1zB,EAAGgsC,GAAG,IAGrE7yB,EAAG8uB,eAAiB,SAAUrc,EAAG8H,EAAG1zB,EAAGgsC,GAAK,OAAOC,GAAc9yB,EAAIyS,EAAG8H,EAAG1zB,EAAGgsC,GAAG,IAIjF,IAAI4D,EAAaF,GAAeA,EAAY5pC,KAI1Cs2B,GAAkBjjB,EAAI,SAAUy2B,GAAcA,EAAWzK,OAASvT,GAAa,YAC5Eie,IAA4Bh6B,GAAK,sBAAuBsD,MACxD,GACHijB,GAAkBjjB,EAAI,aAAc5S,EAAQupC,kBAAoBle,GAAa,YAC1Eie,IAA4Bh6B,GAAK,0BAA2BsD,MAC5D,GAOP,IAkQI9a,GAlQA0xC,GAA2B,KAE/B,SAASC,GAAazuC,GAEpB6oC,GAAqB7oC,EAAIC,WAEzBD,EAAIC,UAAUyuC,UAAY,SAAUltC,GAClC,OAAOw/B,GAASx/B,EAAIxE,OAGtBgD,EAAIC,UAAU0uC,QAAU,WACtB,IAiBIrV,EAjBA1hB,EAAK5a,KACLgc,EAAMpB,EAAG2K,SACTsf,EAAS7oB,EAAI6oB,OACbgL,EAAe7zB,EAAI6zB,aAEnBA,IACFj1B,EAAGgG,aAAe0nB,GAChBuH,EAAatoC,KAAK+lC,YAClB1yB,EAAG8F,OACH9F,EAAGgG,eAMPhG,EAAG81B,OAASb,EAGZ,IAIE2B,GAA2B52B,EAC3B0hB,EAAQuI,EAAOhgC,KAAK+V,EAAGmqB,aAAcnqB,EAAG8uB,gBACxC,MAAOrwB,IAKP,GAJAopB,GAAYppB,GAAGuB,EAAI,UAI0BA,EAAG2K,SAASqsB,YACvD,IACEtV,EAAQ1hB,EAAG2K,SAASqsB,YAAY/sC,KAAK+V,EAAGmqB,aAAcnqB,EAAG8uB,eAAgBrwB,IACzE,MAAOA,IACPopB,GAAYppB,GAAGuB,EAAI,eACnB0hB,EAAQ1hB,EAAGs2B,YAGb5U,EAAQ1hB,EAAGs2B,OAEb,QACAM,GAA2B,KAmB7B,OAhBInrC,MAAMC,QAAQg2B,IAA2B,IAAjBA,EAAMn6B,SAChCm6B,EAAQA,EAAM,IAGVA,aAAiBxB,KACwBz0B,MAAMC,QAAQg2B,IACzDhlB,GACE,uGAEAsD,GAGJ0hB,EAAQJ,MAGVI,EAAMlf,OAASyyB,EACRvT,GAMX,SAASuV,GAAYC,EAAMC,GAOzB,OALED,EAAKxlB,YACJwM,IAA0C,WAA7BgZ,EAAK7kB,OAAO+kB,gBAE1BF,EAAOA,EAAKpyB,SAEPhb,EAASotC,GACZC,EAAKxxB,OAAOuxB,GACZA,EAGN,SAASzC,GACP4C,EACA1qC,EACA0Z,EACA+Z,EACAD,GAEA,IAAIoB,EAAOD,KAGX,OAFAC,EAAKjB,aAAe+W,EACpB9V,EAAKN,UAAY,CAAEt0B,KAAMA,EAAM0Z,QAASA,EAAS+Z,SAAUA,EAAUD,IAAKA,GACnEoB,EAGT,SAASiT,GACP6C,EACA9C,GAEA,GAAI3b,EAAOye,EAAQvvC,QAAU6wB,EAAM0e,EAAQC,WACzC,OAAOD,EAAQC,UAGjB,GAAI3e,EAAM0e,EAAQE,UAChB,OAAOF,EAAQE,SAGjB,IAAIC,EAAQZ,GAMZ,GALIY,GAAS7e,EAAM0e,EAAQI,UAA8C,IAAnCJ,EAAQI,OAAO5xC,QAAQ2xC,IAE3DH,EAAQI,OAAO5rC,KAAK2rC,GAGlB5e,EAAOye,EAAQK,UAAY/e,EAAM0e,EAAQM,aAC3C,OAAON,EAAQM,YAGjB,GAAIH,IAAU7e,EAAM0e,EAAQI,QAAS,CACnC,IAAIA,EAASJ,EAAQI,OAAS,CAACD,GAC3BI,GAAO,EACPC,EAAe,KACfC,EAAe,KAElB,EAAQ95B,IAAI,kBAAkB,WAAc,OAAO0b,EAAO+d,EAAQD,MAEnE,IAAIO,EAAc,SAAUC,GAC1B,IAAK,IAAI3uC,EAAI,EAAG+oB,EAAIqlB,EAAOlwC,OAAQ8B,EAAI+oB,EAAG/oB,IACvCouC,EAAOpuC,GAAI4I,eAGV+lC,IACFP,EAAOlwC,OAAS,EACK,OAAjBswC,IACFI,aAAaJ,GACbA,EAAe,MAEI,OAAjBC,IACFG,aAAaH,GACbA,EAAe,QAKjB7qC,EAAUse,GAAK,SAAUhgB,GAE3B8rC,EAAQE,SAAWN,GAAW1rC,EAAKgpC,GAG9BqD,EAGHH,EAAOlwC,OAAS,EAFhBwwC,GAAY,MAMZ/pC,EAASud,GAAK,SAAUlc,GACeqN,GACvC,sCAAyC5T,OAAOuuC,IAC/ChoC,EAAU,aAAeA,EAAU,KAElCspB,EAAM0e,EAAQC,aAChBD,EAAQvvC,OAAQ,EAChBiwC,GAAY,OAIZxsC,EAAM8rC,EAAQpqC,EAASe,GA+C3B,OA7CIlE,EAASyB,KACPqB,EAAUrB,GAERmtB,EAAQ2e,EAAQE,WAClBhsC,EAAIsB,KAAKI,EAASe,GAEXpB,EAAUrB,EAAI4V,aACvB5V,EAAI4V,UAAUtU,KAAKI,EAASe,GAExB2qB,EAAMptB,EAAIzD,SACZuvC,EAAQC,UAAYL,GAAW1rC,EAAIzD,MAAOysC,IAGxC5b,EAAMptB,EAAImsC,WACZL,EAAQM,YAAcV,GAAW1rC,EAAImsC,QAASnD,GAC5B,IAAdhpC,EAAI2sC,MACNb,EAAQK,SAAU,EAElBG,EAAejP,YAAW,WACxBiP,EAAe,KACXnf,EAAQ2e,EAAQE,WAAa7e,EAAQ2e,EAAQvvC,SAC/CuvC,EAAQK,SAAU,EAClBK,GAAY,MAEbxsC,EAAI2sC,OAAS,MAIhBvf,EAAMptB,EAAI4sC,WACZL,EAAelP,YAAW,WACxBkP,EAAe,KACXpf,EAAQ2e,EAAQE,WAClBvpC,EAEO,YAAezC,EAAW,QAAI,SAItCA,EAAI4sC,YAKbP,GAAO,EAEAP,EAAQK,QACXL,EAAQM,YACRN,EAAQE,UAMhB,SAASrW,GAAoBK,GAC3B,OAAOA,EAAKR,WAAaQ,EAAKjB,aAKhC,SAAS8X,GAAwBhY,GAC/B,GAAI30B,MAAMC,QAAQ00B,GAChB,IAAK,IAAI/2B,EAAI,EAAGA,EAAI+2B,EAAS74B,OAAQ8B,IAAK,CACxC,IAAIxC,EAAIu5B,EAAS/2B,GACjB,GAAIsvB,EAAM9xB,KAAO8xB,EAAM9xB,EAAE8nB,mBAAqBuS,GAAmBr6B,IAC/D,OAAOA,GAUf,SAASwxC,GAAYr4B,GACnBA,EAAGs4B,QAAU7uC,OAAOa,OAAO,MAC3B0V,EAAGu4B,eAAgB,EAEnB,IAAI/F,EAAYxyB,EAAG2K,SAASgsB,iBACxBnE,GACFgG,GAAyBx4B,EAAIwyB,GAMjC,SAAS5b,GAAKtU,EAAO1Y,GACnB1E,GAAO8Y,IAAIsE,EAAO1Y,GAGpB,SAAS6uC,GAAUn2B,EAAO1Y,GACxB1E,GAAOgZ,KAAKoE,EAAO1Y,GAGrB,SAASiiC,GAAmBvpB,EAAO1Y,GACjC,IAAI8uC,EAAUxzC,GACd,OAAO,SAASyzC,IACd,IAAIptC,EAAM3B,EAAGoT,MAAM,KAAMiB,WACb,OAAR1S,GACFmtC,EAAQx6B,KAAKoE,EAAOq2B,IAK1B,SAASH,GACPx4B,EACAwyB,EACAoG,GAEA1zC,GAAS8a,EACTyrB,GAAgB+G,EAAWoG,GAAgB,GAAIhiB,GAAK6hB,GAAU5M,GAAmB7rB,GACjF9a,QAAS4U,EAGX,SAAS++B,GAAazwC,GACpB,IAAI0wC,EAAS,SACb1wC,EAAIC,UAAU2V,IAAM,SAAUsE,EAAO1Y,GACnC,IAAIoW,EAAK5a,KACT,GAAIqG,MAAMC,QAAQ4W,GAChB,IAAK,IAAIjZ,EAAI,EAAG+oB,EAAI9P,EAAM/a,OAAQ8B,EAAI+oB,EAAG/oB,IACvC2W,EAAGhC,IAAIsE,EAAMjZ,GAAIO,QAGlBoW,EAAGs4B,QAAQh2B,KAAWtC,EAAGs4B,QAAQh2B,GAAS,KAAKzW,KAAKjC,GAGjDkvC,EAAO9vC,KAAKsZ,KACdtC,EAAGu4B,eAAgB,GAGvB,OAAOv4B,GAGT5X,EAAIC,UAAU6J,MAAQ,SAAUoQ,EAAO1Y,GACrC,IAAIoW,EAAK5a,KACT,SAASsmC,IACP1rB,EAAG9B,KAAKoE,EAAOopB,GACf9hC,EAAGoT,MAAMgD,EAAI/B,WAIf,OAFAytB,EAAG9hC,GAAKA,EACRoW,EAAGhC,IAAIsE,EAAOopB,GACP1rB,GAGT5X,EAAIC,UAAU6V,KAAO,SAAUoE,EAAO1Y,GACpC,IAAIoW,EAAK5a,KAET,IAAK6Y,UAAU1W,OAEb,OADAyY,EAAGs4B,QAAU7uC,OAAOa,OAAO,MACpB0V,EAGT,GAAIvU,MAAMC,QAAQ4W,GAAQ,CACxB,IAAK,IAAIy2B,EAAM,EAAG3mB,EAAI9P,EAAM/a,OAAQwxC,EAAM3mB,EAAG2mB,IAC3C/4B,EAAG9B,KAAKoE,EAAMy2B,GAAMnvC,GAEtB,OAAOoW,EAGT,IASIqpB,EATA2P,EAAMh5B,EAAGs4B,QAAQh2B,GACrB,IAAK02B,EACH,OAAOh5B,EAET,IAAKpW,EAEH,OADAoW,EAAGs4B,QAAQh2B,GAAS,KACbtC,EAIT,IAAI3W,EAAI2vC,EAAIzxC,OACZ,MAAO8B,IAEL,GADAggC,EAAK2P,EAAI3vC,GACLggC,IAAOz/B,GAAMy/B,EAAGz/B,KAAOA,EAAI,CAC7BovC,EAAI/sC,OAAO5C,EAAG,GACd,MAGJ,OAAO2W,GAGT5X,EAAIC,UAAU8V,MAAQ,SAAUmE,GAC9B,IAAItC,EAAK5a,KAEH6zC,EAAiB32B,EAAM7O,cACvBwlC,IAAmB32B,GAAStC,EAAGs4B,QAAQW,IACzC3a,GACE,UAAa2a,EAAiB,6BAC7Bza,GAAoBxe,GAAO,uCAA0CsC,EADtE,iKAIgCuX,EAAUvX,GAAU,iBAAqBA,EAAQ,MAIvF,IAAI02B,EAAMh5B,EAAGs4B,QAAQh2B,GACrB,GAAI02B,EAAK,CACPA,EAAMA,EAAIzxC,OAAS,EAAI2yB,EAAQ8e,GAAOA,EAGtC,IAFA,IAAIxjC,EAAO0kB,EAAQjc,UAAW,GAC1B6pB,EAAO,sBAAyBxlB,EAAQ,IACnCjZ,EAAI,EAAG+oB,EAAI4mB,EAAIzxC,OAAQ8B,EAAI+oB,EAAG/oB,IACrC8+B,GAAwB6Q,EAAI3vC,GAAI2W,EAAIxK,EAAMwK,EAAI8nB,GAGlD,OAAO9nB,GAMX,IAAI8zB,GAAiB,KACjB4C,IAA2B,EAE/B,SAASwC,GAAkBl5B,GACzB,IAAIm5B,EAAqBrF,GAEzB,OADAA,GAAiB9zB,EACV,WACL8zB,GAAiBqF,GAIrB,SAASC,GAAep5B,GACtB,IAAI5S,EAAU4S,EAAG2K,SAGbnI,EAASpV,EAAQoV,OACrB,GAAIA,IAAWpV,EAAQ0nC,SAAU,CAC/B,MAAOtyB,EAAOmI,SAASmqB,UAAYtyB,EAAOkI,QACxClI,EAASA,EAAOkI,QAElBlI,EAAOrC,UAAUtU,KAAKmU,GAGxBA,EAAG0K,QAAUlI,EACbxC,EAAG+e,MAAQvc,EAASA,EAAOuc,MAAQ/e,EAEnCA,EAAGG,UAAY,GACfH,EAAGiB,MAAQ,GAEXjB,EAAGq5B,SAAW,KACdr5B,EAAGs5B,UAAY,KACft5B,EAAGu5B,iBAAkB,EACrBv5B,EAAGuN,YAAa,EAChBvN,EAAGyzB,cAAe,EAClBzzB,EAAGw5B,mBAAoB,EAGzB,SAASC,GAAgBrxC,GACvBA,EAAIC,UAAUqxC,QAAU,SAAUhY,EAAO8R,GACvC,IAAIxzB,EAAK5a,KACLu0C,EAAS35B,EAAG45B,IACZC,EAAY75B,EAAGs2B,OACfwD,EAAwBZ,GAAkBl5B,GAC9CA,EAAGs2B,OAAS5U,EAQV1hB,EAAG45B,IALAC,EAKM75B,EAAG+5B,UAAUF,EAAWnY,GAHxB1hB,EAAG+5B,UAAU/5B,EAAG45B,IAAKlY,EAAO8R,GAAW,GAKlDsG,IAEIH,IACFA,EAAOK,QAAU,MAEfh6B,EAAG45B,MACL55B,EAAG45B,IAAII,QAAUh6B,GAGfA,EAAG81B,QAAU91B,EAAG0K,SAAW1K,EAAG81B,SAAW91B,EAAG0K,QAAQ4rB,SACtDt2B,EAAG0K,QAAQkvB,IAAM55B,EAAG45B,MAMxBxxC,EAAIC,UAAU4J,aAAe,WAC3B,IAAI+N,EAAK5a,KACL4a,EAAGq5B,UACLr5B,EAAGq5B,SAASpZ,UAIhB73B,EAAIC,UAAU6mB,SAAW,WACvB,IAAIlP,EAAK5a,KACT,IAAI4a,EAAGw5B,kBAAP,CAGA1tB,GAAS9L,EAAI,iBACbA,EAAGw5B,mBAAoB,EAEvB,IAAIh3B,EAASxC,EAAG0K,SACZlI,GAAWA,EAAOg3B,mBAAsBx5B,EAAG2K,SAASmqB,UACtDpb,EAAOlX,EAAOrC,UAAWH,GAGvBA,EAAGq5B,UACLr5B,EAAGq5B,SAASY,WAEd,IAAI5wC,EAAI2W,EAAGk6B,UAAU3yC,OACrB,MAAO8B,IACL2W,EAAGk6B,UAAU7wC,GAAG4wC,WAIdj6B,EAAGm6B,MAAMjY,QACXliB,EAAGm6B,MAAMjY,OAAOQ,UAGlB1iB,EAAGyzB,cAAe,EAElBzzB,EAAG+5B,UAAU/5B,EAAGs2B,OAAQ,MAExBxqB,GAAS9L,EAAI,aAEbA,EAAG9B,OAEC8B,EAAG45B,MACL55B,EAAG45B,IAAII,QAAU,MAGfh6B,EAAG81B,SACL91B,EAAG81B,OAAOtzB,OAAS,QAKzB,SAASwxB,GACPh0B,EACAqM,EACAmmB,EACA+D,EACA6D,GAGE1D,IAA2B,EAS7B,IAAI2D,EAAiB9D,EAAY5pC,KAAK+lC,YAClC4H,EAAiBt6B,EAAGgG,aACpBu0B,KACDF,IAAmBA,EAAetM,SAClCuM,IAAmB7hB,IAAgB6hB,EAAevM,SAClDsM,GAAkBr6B,EAAGgG,aAAagoB,OAASqM,EAAerM,MAMzDwM,KACFJ,GACAp6B,EAAG2K,SAAS6rB,iBACZ+D,GAkBF,GAfAv6B,EAAG2K,SAASsqB,aAAesB,EAC3Bv2B,EAAG81B,OAASS,EAERv2B,EAAGs2B,SACLt2B,EAAGs2B,OAAO9zB,OAAS+zB,GAErBv2B,EAAG2K,SAAS6rB,gBAAkB4D,EAK9Bp6B,EAAGy6B,OAASlE,EAAY5pC,KAAKq/B,OAASvT,EACtCzY,EAAG06B,WAAalI,GAAa/Z,EAGzBpM,GAAarM,EAAG2K,SAAStD,MAAO,CAClCmb,IAAgB,GAGhB,IAFA,IAAInb,EAAQrH,EAAGwmB,OACXmU,EAAW36B,EAAG2K,SAASiwB,WAAa,GAC/BvxC,EAAI,EAAGA,EAAIsxC,EAASpzC,OAAQ8B,IAAK,CACxC,IAAIhE,EAAMs1C,EAAStxC,GACf08B,EAAc/lB,EAAG2K,SAAStD,MAC9BA,EAAMhiB,GAAOygC,GAAazgC,EAAK0gC,EAAa1Z,EAAWrM,GAEzDwiB,IAAgB,GAEhBxiB,EAAG2K,SAAS0B,UAAYA,EAI1BrM,EAAG66B,oBAAsB76B,EAAG66B,mBAAmB76B,GAG/CwyB,EAAYA,GAAa/Z,EACzB,IAAImgB,EAAe54B,EAAG2K,SAASgsB,iBAC/B32B,EAAG2K,SAASgsB,iBAAmBnE,EAC/BgG,GAAyBx4B,EAAIwyB,EAAWoG,GAGpC4B,IACFx6B,EAAG8F,OAASwnB,GAAa8M,EAAgB7D,EAAYlwB,SACrDrG,EAAG/N,gBAIHykC,IAA2B,EAI/B,SAASoE,GAAkB96B,GACzB,MAAOA,IAAOA,EAAKA,EAAG0K,SACpB,GAAI1K,EAAGs5B,UAAa,OAAO,EAE7B,OAAO,EAGT,SAASnF,GAAwBn0B,EAAI+6B,GACnC,GAAIA,GAEF,GADA/6B,EAAGu5B,iBAAkB,EACjBuB,GAAiB96B,GACnB,YAEG,GAAIA,EAAGu5B,gBACZ,OAEF,GAAIv5B,EAAGs5B,WAA8B,OAAjBt5B,EAAGs5B,UAAoB,CACzCt5B,EAAGs5B,WAAY,EACf,IAAK,IAAIjwC,EAAI,EAAGA,EAAI2W,EAAGG,UAAU5Y,OAAQ8B,IACvC8qC,GAAuBn0B,EAAGG,UAAU9W,IAEtCyiB,GAAS9L,EAAI,cAIjB,SAASq0B,GAA0Br0B,EAAI+6B,GACrC,KAAIA,IACF/6B,EAAGu5B,iBAAkB,GACjBuB,GAAiB96B,OAIlBA,EAAGs5B,UAAW,CACjBt5B,EAAGs5B,WAAY,EACf,IAAK,IAAIjwC,EAAI,EAAGA,EAAI2W,EAAGG,UAAU5Y,OAAQ8B,IACvCgrC,GAAyBr0B,EAAGG,UAAU9W,IAExCyiB,GAAS9L,EAAI,gBAIjB,SAAS8L,GAAU9L,EAAIjU,GAErByzB,KACA,IAAIwK,EAAWhqB,EAAG2K,SAAS5e,GACvB+7B,EAAO/7B,EAAO,QAClB,GAAIi+B,EACF,IAAK,IAAI3gC,EAAI,EAAG2xC,EAAIhR,EAASziC,OAAQ8B,EAAI2xC,EAAG3xC,IAC1C8+B,GAAwB6B,EAAS3gC,GAAI2W,EAAI,KAAMA,EAAI8nB,GAGnD9nB,EAAGu4B,eACLv4B,EAAG7B,MAAM,QAAUpS,GAErB4zB,KAKF,IAAIsb,GAAmB,IAEnBnuC,GAAQ,GACRouC,GAAoB,GACpB9c,GAAM,GACN+c,GAAW,GACXC,IAAU,EACVC,IAAW,EACXrvC,GAAQ,EAKZ,SAASsvC,KACPtvC,GAAQc,GAAMvF,OAAS2zC,GAAkB3zC,OAAS,EAClD62B,GAAM,GAEJ+c,GAAW,GAEbC,GAAUC,IAAW,EAQvB,IAGIE,GAAS5yC,KAAKC,IAQlB,GAAIq0B,IAAcO,GAAM,CACtB,IAAI7B,GAAcrK,OAAOqK,YAEvBA,IAC2B,oBAApBA,GAAY/yB,KACnB2yC,KAAWtS,SAASuS,YAAY,SAASC,YAMzCF,GAAS,WAAc,OAAO5f,GAAY/yB,QAO9C,SAAS8yC,KAGP,IAAIxlB,EAASxK,EAcb,IAhBwB6vB,KACxBF,IAAW,EAWXvuC,GAAM/B,MAAK,SAAU0nB,EAAG8H,GAAK,OAAO9H,EAAE/G,GAAK6O,EAAE7O,MAIxC1f,GAAQ,EAAGA,GAAQc,GAAMvF,OAAQyE,KASpC,GARAkqB,EAAUppB,GAAMd,IACZkqB,EAAQylB,QACVzlB,EAAQylB,SAEVjwB,EAAKwK,EAAQxK,GACb0S,GAAI1S,GAAM,KACVwK,EAAQ0lB,MAEgD,MAAXxd,GAAI1S,KAC/CyvB,GAASzvB,IAAOyvB,GAASzvB,IAAO,GAAK,EACjCyvB,GAASzvB,GAAMuvB,IAAkB,CACnCv+B,GACE,yCACEwZ,EAAQ2lB,KACH,+BAAmC3lB,EAAkB,WAAI,IAC1D,mCAENA,EAAQlW,IAEV,MAMN,IAAI87B,EAAiBZ,GAAkBl0C,QACnC+0C,EAAejvC,GAAM9F,QAEzBs0C,KAGAU,GAAmBF,GACnBG,GAAiBF,GAIbrgB,IAAYJ,EAAOI,UACrBA,GAASwgB,KAAK,SAIlB,SAASD,GAAkBnvC,GACzB,IAAIzD,EAAIyD,EAAMvF,OACd,MAAO8B,IAAK,CACV,IAAI6sB,EAAUppB,EAAMzD,GAChB2W,EAAKkW,EAAQlW,GACbA,EAAGq5B,WAAanjB,GAAWlW,EAAGuN,aAAevN,EAAGyzB,cAClD3nB,GAAS9L,EAAI,YASnB,SAASk0B,GAAyBl0B,GAGhCA,EAAGs5B,WAAY,EACf4B,GAAkBrvC,KAAKmU,GAGzB,SAASg8B,GAAoBlvC,GAC3B,IAAK,IAAIzD,EAAI,EAAGA,EAAIyD,EAAMvF,OAAQ8B,IAChCyD,EAAMzD,GAAGiwC,WAAY,EACrBnF,GAAuBrnC,EAAMzD,IAAI,GASrC,SAAS8yC,GAAcjmB,GACrB,IAAIxK,EAAKwK,EAAQxK,GACjB,GAAe,MAAX0S,GAAI1S,GAAa,CAEnB,GADA0S,GAAI1S,IAAM,EACL2vB,GAEE,CAGL,IAAIhyC,EAAIyD,GAAMvF,OAAS,EACvB,MAAO8B,EAAI2C,IAASc,GAAMzD,GAAGqiB,GAAKwK,EAAQxK,GACxCriB,IAEFyD,GAAMb,OAAO5C,EAAI,EAAG,EAAG6sB,QARvBppB,GAAMjB,KAAKqqB,GAWb,IAAKklB,GAAS,CAGZ,GAFAA,IAAU,GAEoC9f,EAAOgB,MAEnD,YADAof,KAGFtS,GAASsS,MASf,IAAIU,GAAQ,EAORC,GAAU,SACZr8B,EACAs8B,EACAjT,EACAj8B,EACAmvC,GAEAn3C,KAAK4a,GAAKA,EACNu8B,IACFv8B,EAAGq5B,SAAWj0C,MAEhB4a,EAAGk6B,UAAUruC,KAAKzG,MAEdgI,GACFhI,KAAKo3C,OAASpvC,EAAQovC,KACtBp3C,KAAKy2C,OAASzuC,EAAQyuC,KACtBz2C,KAAKq3C,OAASrvC,EAAQqvC,KACtBr3C,KAAKwyC,OAASxqC,EAAQwqC,KACtBxyC,KAAKu2C,OAASvuC,EAAQuuC,QAEtBv2C,KAAKo3C,KAAOp3C,KAAKy2C,KAAOz2C,KAAKq3C,KAAOr3C,KAAKwyC,MAAO,EAElDxyC,KAAKikC,GAAKA,EACVjkC,KAAKsmB,KAAO0wB,GACZh3C,KAAKs3C,QAAS,EACdt3C,KAAKu3C,MAAQv3C,KAAKq3C,KAClBr3C,KAAKw3C,KAAO,GACZx3C,KAAKy3C,QAAU,GACfz3C,KAAK03C,OAAS,IAAI7e,GAClB74B,KAAK23C,UAAY,IAAI9e,GACrB74B,KAAK43C,WACDV,EAAQv1C,WAGW,oBAAZu1C,EACTl3C,KAAKk+B,OAASgZ,GAEdl3C,KAAKk+B,OAASzG,EAAUyf,GACnBl3C,KAAKk+B,SACRl+B,KAAKk+B,OAASn5B,EAC2BuS,GACvC,0BAA6B4/B,EAA7B,+FAGAt8B,KAIN5a,KAAKgK,MAAQhK,KAAKq3C,UACd3iC,EACA1U,KAAK2N,OAMXspC,GAAQh0C,UAAU0K,IAAM,WAEtB,IAAI3D,EADJowB,GAAWp6B,MAEX,IAAI4a,EAAK5a,KAAK4a,GACd,IACE5Q,EAAQhK,KAAKk+B,OAAOr5B,KAAK+V,EAAIA,GAC7B,MAAOvB,IACP,IAAIrZ,KAAKy2C,KAGP,MAAMp9B,GAFNopB,GAAYppB,GAAGuB,EAAK,uBAA2B5a,KAAe,WAAI,KAIpE,QAGIA,KAAKo3C,MACPjS,GAASn7B,GAEXuwB,KACAv6B,KAAK63C,cAEP,OAAO7tC,GAMTitC,GAAQh0C,UAAU03B,OAAS,SAAiBqC,GAC1C,IAAI1W,EAAK0W,EAAI1W,GACRtmB,KAAK23C,UAAU3e,IAAI1S,KACtBtmB,KAAK23C,UAAUnmB,IAAIlL,GACnBtmB,KAAKy3C,QAAQhxC,KAAKu2B,GACbh9B,KAAK03C,OAAO1e,IAAI1S,IACnB0W,EAAIxC,OAAOx6B,QAQjBi3C,GAAQh0C,UAAU40C,YAAc,WAC9B,IAAI5zC,EAAIjE,KAAKw3C,KAAKr1C,OAClB,MAAO8B,IAAK,CACV,IAAI+4B,EAAMh9B,KAAKw3C,KAAKvzC,GACfjE,KAAK23C,UAAU3e,IAAIgE,EAAI1W,KAC1B0W,EAAIvC,UAAUz6B,MAGlB,IAAI83C,EAAM93C,KAAK03C,OACf13C,KAAK03C,OAAS13C,KAAK23C,UACnB33C,KAAK23C,UAAYG,EACjB93C,KAAK23C,UAAU1e,QACf6e,EAAM93C,KAAKw3C,KACXx3C,KAAKw3C,KAAOx3C,KAAKy3C,QACjBz3C,KAAKy3C,QAAUK,EACf93C,KAAKy3C,QAAQt1C,OAAS,GAOxB80C,GAAQh0C,UAAU43B,OAAS,WAErB76B,KAAKq3C,KACPr3C,KAAKu3C,OAAQ,EACJv3C,KAAKwyC,KACdxyC,KAAKw2C,MAELO,GAAa/2C,OAQjBi3C,GAAQh0C,UAAUuzC,IAAM,WACtB,GAAIx2C,KAAKs3C,OAAQ,CACf,IAAIttC,EAAQhK,KAAK2N,MACjB,GACE3D,IAAUhK,KAAKgK,OAIftF,EAASsF,IACThK,KAAKo3C,KACL,CAEA,IAAIz6B,EAAW3c,KAAKgK,MAEpB,GADAhK,KAAKgK,MAAQA,EACThK,KAAKy2C,KACP,IACEz2C,KAAKikC,GAAGp/B,KAAK7E,KAAK4a,GAAI5Q,EAAO2S,GAC7B,MAAOtD,IACPopB,GAAYppB,GAAGrZ,KAAK4a,GAAK,yBAA6B5a,KAAe,WAAI,UAG3EA,KAAKikC,GAAGp/B,KAAK7E,KAAK4a,GAAI5Q,EAAO2S,MAUrCs6B,GAAQh0C,UAAU80C,SAAW,WAC3B/3C,KAAKgK,MAAQhK,KAAK2N,MAClB3N,KAAKu3C,OAAQ,GAMfN,GAAQh0C,UAAUy3B,OAAS,WACzB,IAAIz2B,EAAIjE,KAAKw3C,KAAKr1C,OAClB,MAAO8B,IACLjE,KAAKw3C,KAAKvzC,GAAGy2B,UAOjBuc,GAAQh0C,UAAU4xC,SAAW,WAC3B,GAAI70C,KAAKs3C,OAAQ,CAIVt3C,KAAK4a,GAAGw5B,mBACX9f,EAAOt0B,KAAK4a,GAAGk6B,UAAW90C,MAE5B,IAAIiE,EAAIjE,KAAKw3C,KAAKr1C,OAClB,MAAO8B,IACLjE,KAAKw3C,KAAKvzC,GAAGw2B,UAAUz6B,MAEzBA,KAAKs3C,QAAS,IAMlB,IAAIU,GAA2B,CAC7Bx6B,YAAY,EACZD,cAAc,EACd5P,IAAK5I,EACL6I,IAAK7I,GAGP,SAASmkC,GAAOppC,EAAQm4C,EAAWh4C,GACjC+3C,GAAyBrqC,IAAM,WAC7B,OAAO3N,KAAKi4C,GAAWh4C,IAEzB+3C,GAAyBpqC,IAAM,SAAsBmb,GACnD/oB,KAAKi4C,GAAWh4C,GAAO8oB,GAEzB1kB,OAAOqJ,eAAe5N,EAAQG,EAAK+3C,IAGrC,SAASE,GAAWt9B,GAClBA,EAAGk6B,UAAY,GACf,IAAI7xB,EAAOrI,EAAG2K,SACVtC,EAAKhB,OAASk2B,GAAUv9B,EAAIqI,EAAKhB,OACjCgB,EAAKlW,SAAWqrC,GAAYx9B,EAAIqI,EAAKlW,SACrCkW,EAAK1b,KACPyZ,GAASpG,GAET8iB,GAAQ9iB,EAAGm6B,MAAQ,IAAI,GAErB9xB,EAAKqc,UAAY+Y,GAAaz9B,EAAIqI,EAAKqc,UACvCrc,EAAKnV,OAASmV,EAAKnV,QAAUwqB,IAC/BggB,GAAU19B,EAAIqI,EAAKnV,OAIvB,SAASqqC,GAAWv9B,EAAI29B,GACtB,IAAItxB,EAAYrM,EAAG2K,SAAS0B,WAAa,GACrChF,EAAQrH,EAAGwmB,OAAS,GAGpB17B,EAAOkV,EAAG2K,SAASiwB,UAAY,GAC/BgD,GAAU59B,EAAG0K,QAEZkzB,GACHpb,IAAgB,GAElB,IAAIrsB,EAAO,SAAW9Q,GACpByF,EAAKe,KAAKxG,GACV,IAAI+J,EAAQ02B,GAAazgC,EAAKs4C,EAActxB,EAAWrM,GAGjD8vB,EAAgBjW,EAAUx0B,IAC1Bo0B,EAAoBqW,IACpBxU,EAAOW,eAAe6T,KACxBpzB,GACG,IAAOozB,EAAgB,kEACxB9vB,GAGJijB,GAAkB5b,EAAOhiB,EAAK+J,GAAO,WACnC,IAAKwuC,IAAWlH,GAA0B,CAEtC,GAAiB,aAAd12B,EAAGiN,QAAuC,gBAAdjN,EAAGiN,QAA0C,WAAdjN,EAAGiN,OAC7D,OAGJ,GACY,UAAR5nB,GACAoG,MAAMC,QAAQsU,EAAG2K,SAAS3D,aAC6B,IAAvDhH,EAAG2K,SAAS3D,UAAUnhB,QAAQ,oBAEhC,OAEF,GAAGma,EAAG69B,aACJ,OAEF,IAAInzB,EAAU1K,EAAG0K,QACjB,MAAMA,EAAQ,CACZ,GAAGA,EAAQozB,oBACT,OAEFpzB,EAAUA,EAAQA,QAGtBhO,GACE,0MAGkCrX,EAAM,IACxC2a,OAUF3a,KAAO2a,GACXsuB,GAAMtuB,EAAI,SAAU3a,IAIxB,IAAK,IAAIA,KAAOs4C,EAAcxnC,EAAM9Q,GACpCm9B,IAAgB,GAGlB,SAASpc,GAAUpG,GACjB,IAAIrT,EAAOqT,EAAG2K,SAAShe,KACvBA,EAAOqT,EAAGm6B,MAAwB,oBAATxtC,EACrBoxC,GAAQpxC,EAAMqT,GACdrT,GAAQ,GACP3C,EAAc2C,KACjBA,EAAO,GACkC+P,GACvC,8GAEAsD,IAIJ,IAAIlV,EAAOrB,OAAOqB,KAAK6B,GACnB0a,EAAQrH,EAAG2K,SAAStD,MACpBlV,EAAU6N,EAAG2K,SAASxY,QACtB9I,EAAIyB,EAAKvD,OACb,MAAO8B,IAAK,CACV,IAAIhE,EAAMyF,EAAKzB,GAET8I,GAAWjI,EAAOiI,EAAS9M,IAC7BqX,GACG,WAAcrX,EAAM,iDACrB2a,GAIFqH,GAASnd,EAAOmd,EAAOhiB,GACgBqX,GACvC,sBAAyBrX,EAAzB,mEAEA2a,GAEQyc,EAAWp3B,IACrBipC,GAAMtuB,EAAI,QAAS3a,GAIvBy9B,GAAQn2B,GAAM,GAGhB,SAASoxC,GAASpxC,EAAMqT,GAEtBwf,KACA,IACE,OAAO7yB,EAAK1C,KAAK+V,EAAIA,GACrB,MAAOvB,IAEP,OADAopB,GAAYppB,GAAGuB,EAAI,UACZ,GACP,QACA2f,MAIJ,IAAIqe,GAAyB,CAAEvB,MAAM,GAErC,SAASgB,GAAcz9B,EAAI0kB,GAEzB,IAAItO,EAAWpW,EAAGi+B,kBAAoBx0C,OAAOa,OAAO,MAEhD4zC,EAAQtgB,KAEZ,IAAK,IAAIv4B,KAAOq/B,EAAU,CACxB,IAAIyZ,EAAUzZ,EAASr/B,GACnBi+B,EAA4B,oBAAZ6a,EAAyBA,EAAUA,EAAQprC,IACR,MAAVuwB,GAC3C5mB,GACG,4CAA+CrX,EAAM,KACtD2a,GAICk+B,IAEH9nB,EAAS/wB,GAAO,IAAIg3C,GAClBr8B,EACAsjB,GAAUn5B,EACVA,EACA6zC,KAOE34C,KAAO2a,EAGP3a,KAAO2a,EAAG8pB,MACZptB,GAAM,0BAA6BrX,EAAM,gCAAmC2a,GACnEA,EAAG2K,SAAStD,OAAShiB,KAAO2a,EAAG2K,SAAStD,OACjD3K,GAAM,0BAA6BrX,EAAM,kCAAqC2a,GALhFo+B,GAAep+B,EAAI3a,EAAK84C,IAW9B,SAASC,GACPl5C,EACAG,EACA84C,GAEA,IAAIE,GAAezgB,KACI,oBAAZugB,GACTf,GAAyBrqC,IAAMsrC,EAC3BC,GAAqBj5C,GACrBk5C,GAAoBJ,GACxBf,GAAyBpqC,IAAM7I,IAE/BizC,GAAyBrqC,IAAMorC,EAAQprC,IACnCsrC,IAAiC,IAAlBF,EAAQ9zC,MACrBi0C,GAAqBj5C,GACrBk5C,GAAoBJ,EAAQprC,KAC9B5I,EACJizC,GAAyBpqC,IAAMmrC,EAAQnrC,KAAO7I,GAG5CizC,GAAyBpqC,MAAQ7I,IACnCizC,GAAyBpqC,IAAM,WAC7B0J,GACG,sBAAyBrX,EAAM,0CAChCD,QAINqE,OAAOqJ,eAAe5N,EAAQG,EAAK+3C,IAGrC,SAASkB,GAAsBj5C,GAC7B,OAAO,WACL,IAAI6wB,EAAU9wB,KAAK64C,mBAAqB74C,KAAK64C,kBAAkB54C,GAC/D,GAAI6wB,EAOF,OANIA,EAAQymB,OACVzmB,EAAQinB,WAEN7d,GAAIG,aAAav6B,QACnBgxB,EAAQ4J,SAEH5J,EAAQ9mB,OAKrB,SAASmvC,GAAoB30C,GAC3B,OAAO,WACL,OAAOA,EAAGK,KAAK7E,KAAMA,OAIzB,SAASo4C,GAAax9B,EAAI7N,GACxB,IAAIkV,EAAQrH,EAAG2K,SAAStD,MACxB,IAAK,IAAIhiB,KAAO8M,EAEgB,oBAAjBA,EAAQ9M,IACjBqX,GACE,WAAcrX,EAAM,sBAA2B8M,EAAQ9M,GAAvD,2EAEA2a,GAGAqH,GAASnd,EAAOmd,EAAOhiB,IACzBqX,GACG,WAAcrX,EAAM,wCACrB2a,GAGC3a,KAAO2a,GAAOyc,EAAWp3B,IAC5BqX,GACE,WAAcrX,EAAd,8GAKN2a,EAAG3a,GAA+B,oBAAjB8M,EAAQ9M,GAAsB8E,EAAO8pB,EAAK9hB,EAAQ9M,GAAM2a,GAI7E,SAAS09B,GAAW19B,EAAI9M,GACtB,IAAK,IAAI7N,KAAO6N,EAAO,CACrB,IAAIkY,EAAUlY,EAAM7N,GACpB,GAAIoG,MAAMC,QAAQ0f,GAChB,IAAK,IAAI/hB,EAAI,EAAGA,EAAI+hB,EAAQ7jB,OAAQ8B,IAClCm1C,GAAcx+B,EAAI3a,EAAK+lB,EAAQ/hB,SAGjCm1C,GAAcx+B,EAAI3a,EAAK+lB,IAK7B,SAASozB,GACPx+B,EACAs8B,EACAlxB,EACAhe,GASA,OAPIpD,EAAcohB,KAChBhe,EAAUge,EACVA,EAAUA,EAAQA,SAEG,kBAAZA,IACTA,EAAUpL,EAAGoL,IAERpL,EAAGyW,OAAO6lB,EAASlxB,EAAShe,GAGrC,SAASqxC,GAAYr2C,GAInB,IAAIs2C,EAAU,CACd,IAAc,WAAc,OAAOt5C,KAAK+0C,QACpCwE,EAAW,CACf,IAAe,WAAc,OAAOv5C,KAAKohC,SAEvCkY,EAAQ1rC,IAAM,WACZ0J,GACE,2EAEAtX,OAGJu5C,EAAS3rC,IAAM,WACb0J,GAAK,sBAAuBtX,OAGhCqE,OAAOqJ,eAAe1K,EAAIC,UAAW,QAASq2C,GAC9Cj1C,OAAOqJ,eAAe1K,EAAIC,UAAW,SAAUs2C,GAE/Cv2C,EAAIC,UAAUu2C,KAAO5rC,GACrB5K,EAAIC,UAAUw2C,QAAUlb,GAExBv7B,EAAIC,UAAUouB,OAAS,SACrB6lB,EACAjT,EACAj8B,GAEA,IAAI4S,EAAK5a,KACT,GAAI4E,EAAcq/B,GAChB,OAAOmV,GAAcx+B,EAAIs8B,EAASjT,EAAIj8B,GAExCA,EAAUA,GAAW,GACrBA,EAAQyuC,MAAO,EACf,IAAI3lB,EAAU,IAAImmB,GAAQr8B,EAAIs8B,EAASjT,EAAIj8B,GAC3C,GAAIA,EAAQ0xC,UACV,IACEzV,EAAGp/B,KAAK+V,EAAIkW,EAAQ9mB,OACpB,MAAOtH,GACP+/B,GAAY//B,EAAOkY,EAAK,mCAAuCkW,EAAkB,WAAI,KAGzF,OAAO,WACLA,EAAQ+jB,aAOd,IAAI8E,GAAQ,EAEZ,SAASC,GAAW52C,GAClBA,EAAIC,UAAU42C,MAAQ,SAAU7xC,GAC9B,IAII49B,EAAUC,EAJVjrB,EAAK5a,KAET4a,EAAGk/B,KAAOH,KAImCzjB,EAAOK,aAAeyO,KACjEY,EAAW,kBAAqBhrB,EAAO,KACvCirB,EAAS,gBAAmBjrB,EAAO,KACnCoqB,GAAKY,IAIPhrB,EAAGgf,QAAS,EAER5xB,GAAWA,EAAQ4nC,aAIrBmK,GAAsBn/B,EAAI5S,GAE1B4S,EAAG2K,SAAW0a,GACZqP,GAA0B10B,EAAG7Q,aAC7B/B,GAAW,GACX4S,GAKFuoB,GAAUvoB,GAKZA,EAAGo/B,MAAQp/B,EACXo5B,GAAcp5B,GACdq4B,GAAWr4B,GACXq2B,GAAWr2B,GACX8L,GAAS9L,EAAI,iBACZA,EAAGq/B,YAAcpS,GAAejtB,GACjCs9B,GAAUt9B,IACTA,EAAGq/B,YAActS,GAAY/sB,IAC7BA,EAAGq/B,YAAcvzB,GAAS9L,EAAI,WAGcsb,EAAOK,aAAeyO,KACjEpqB,EAAGs/B,MAAQ9gB,GAAoBxe,GAAI,GACnCoqB,GAAKa,GACLZ,GAAS,OAAUrqB,EAAQ,MAAI,QAAUgrB,EAAUC,IAGjDjrB,EAAG2K,SAAS2Z,IACdtkB,EAAG+O,OAAO/O,EAAG2K,SAAS2Z,KAK5B,SAAS6a,GAAuBn/B,EAAI5S,GAClC,IAAIib,EAAOrI,EAAG2K,SAAWlhB,OAAOa,OAAO0V,EAAG7Q,YAAY/B,SAElDmpC,EAAcnpC,EAAQ6nC,aAC1B5sB,EAAK7F,OAASpV,EAAQoV,OACtB6F,EAAK4sB,aAAesB,EAEpB,IAAIgJ,EAAwBhJ,EAAY5nB,iBACxCtG,EAAKgE,UAAYkzB,EAAsBlzB,UACvChE,EAAKsuB,iBAAmB4I,EAAsB/M,UAC9CnqB,EAAKmuB,gBAAkB+I,EAAsBnf,SAC7C/X,EAAK4W,cAAgBsgB,EAAsBpf,IAEvC/yB,EAAQ68B,SACV5hB,EAAK4hB,OAAS78B,EAAQ68B,OACtB5hB,EAAK8nB,gBAAkB/iC,EAAQ+iC,iBAInC,SAASuE,GAA2B1W,GAClC,IAAI5wB,EAAU4wB,EAAK5wB,QACnB,GAAI4wB,EAAKhZ,MAAO,CACd,IAAIw6B,EAAe9K,GAA0B1W,EAAKhZ,OAC9Cy6B,EAAqBzhB,EAAKwhB,aAC9B,GAAIA,IAAiBC,EAAoB,CAGvCzhB,EAAKwhB,aAAeA,EAEpB,IAAIE,EAAkBC,GAAuB3hB,GAEzC0hB,GACF/5B,EAAOqY,EAAKjZ,cAAe26B,GAE7BtyC,EAAU4wB,EAAK5wB,QAAUi4B,GAAama,EAAcxhB,EAAKjZ,eACrD3X,EAAQC,OACVD,EAAQ8T,WAAW9T,EAAQC,MAAQ2wB,IAIzC,OAAO5wB,EAGT,SAASuyC,GAAwB3hB,GAC/B,IAAI4hB,EACAC,EAAS7hB,EAAK5wB,QACd0yC,EAAS9hB,EAAK+hB,cAClB,IAAK,IAAI16C,KAAOw6C,EACVA,EAAOx6C,KAASy6C,EAAOz6C,KACpBu6C,IAAYA,EAAW,IAC5BA,EAASv6C,GAAOw6C,EAAOx6C,IAG3B,OAAOu6C,EAGT,SAASx3C,GAAKgF,GAERhI,gBAAgBgD,IAElBsU,GAAK,oEAEPtX,KAAK65C,MAAM7xC,GAWb,SAAS4yC,GAAS53C,GAChBA,EAAI63C,IAAM,SAAUC,GAClB,IAAIC,EAAoB/6C,KAAKg7C,oBAAsBh7C,KAAKg7C,kBAAoB,IAC5E,GAAID,EAAiBt6C,QAAQq6C,IAAW,EACtC,OAAO96C,KAIT,IAAIoQ,EAAO0kB,EAAQjc,UAAW,GAQ9B,OAPAzI,EAAK+hB,QAAQnyB,MACiB,oBAAnB86C,EAAOG,QAChBH,EAAOG,QAAQrjC,MAAMkjC,EAAQ1qC,GACF,oBAAX0qC,GAChBA,EAAOljC,MAAM,KAAMxH,GAErB2qC,EAAiBt0C,KAAKq0C,GACf96C,MAMX,SAASk7C,GAAal4C,GACpBA,EAAIyJ,MAAQ,SAAUA,GAEpB,OADAzM,KAAKgI,QAAUi4B,GAAajgC,KAAKgI,QAASyE,GACnCzM,MAMX,SAASm7C,GAAYn4C,GAMnBA,EAAIgW,IAAM,EACV,IAAIA,EAAM,EAKVhW,EAAIud,OAAS,SAAUZ,GACrBA,EAAgBA,GAAiB,GACjC,IAAIy7B,EAAQp7C,KACRq7C,EAAUD,EAAMpiC,IAChBsiC,EAAc37B,EAAc47B,QAAU57B,EAAc47B,MAAQ,IAChE,GAAID,EAAYD,GACd,OAAOC,EAAYD,GAGrB,IAAIpzC,EAAO0X,EAAc1X,MAAQmzC,EAAMpzC,QAAQC,KACFA,GAC3Cw3B,GAAsBx3B,GAGxB,IAAIuzC,EAAM,SAAuBxzC,GAC/BhI,KAAK65C,MAAM7xC,IA6Cb,OA3CAwzC,EAAIv4C,UAAYoB,OAAOa,OAAOk2C,EAAMn4C,WACpCu4C,EAAIv4C,UAAU8G,YAAcyxC,EAC5BA,EAAIxiC,IAAMA,IACVwiC,EAAIxzC,QAAUi4B,GACZmb,EAAMpzC,QACN2X,GAEF67B,EAAI,SAAWJ,EAKXI,EAAIxzC,QAAQia,OACdw5B,GAAYD,GAEVA,EAAIxzC,QAAQs3B,UACdoc,GAAeF,GAIjBA,EAAIj7B,OAAS66B,EAAM76B,OACnBi7B,EAAI/uC,MAAQ2uC,EAAM3uC,MAClB+uC,EAAIX,IAAMO,EAAMP,IAIhB7kB,EAAYpwB,SAAQ,SAAU+T,GAC5B6hC,EAAI7hC,GAAQyhC,EAAMzhC,MAGhB1R,IACFuzC,EAAIxzC,QAAQ8T,WAAW7T,GAAQuzC,GAMjCA,EAAIpB,aAAegB,EAAMpzC,QACzBwzC,EAAI77B,cAAgBA,EACpB67B,EAAIb,cAAgBp6B,EAAO,GAAIi7B,EAAIxzC,SAGnCszC,EAAYD,GAAWG,EAChBA,GAIX,SAASC,GAAaE,GACpB,IAAI15B,EAAQ05B,EAAK3zC,QAAQia,MACzB,IAAK,IAAIhiB,KAAOgiB,EACdinB,GAAMyS,EAAK14C,UAAW,SAAUhD,GAIpC,SAASy7C,GAAgBC,GACvB,IAAIrc,EAAWqc,EAAK3zC,QAAQs3B,SAC5B,IAAK,IAAIr/B,KAAOq/B,EACd0Z,GAAe2C,EAAK14C,UAAWhD,EAAKq/B,EAASr/B,IAMjD,SAAS27C,GAAoB54C,GAI3BgzB,EAAYpwB,SAAQ,SAAU+T,GAC5B3W,EAAI2W,GAAQ,SACV2M,EACAu1B,GAEA,OAAKA,GAImD,cAATliC,GAC3C8lB,GAAsBnZ,GAEX,cAAT3M,GAAwB/U,EAAci3C,KACxCA,EAAW5zC,KAAO4zC,EAAW5zC,MAAQqe,EACrCu1B,EAAa77C,KAAKgI,QAAQk4B,MAAM3f,OAAOs7B,IAE5B,cAATliC,GAA8C,oBAAfkiC,IACjCA,EAAa,CAAEhtB,KAAMgtB,EAAYhhB,OAAQghB,IAE3C77C,KAAKgI,QAAQ2R,EAAO,KAAK2M,GAAMu1B,EACxBA,GAdA77C,KAAKgI,QAAQ2R,EAAO,KAAK2M,OAwBxC,SAASw1B,GAAkB74B,GACzB,OAAOA,IAASA,EAAK2V,KAAK5wB,QAAQC,MAAQgb,EAAK8X,KAGjD,SAAS/c,GAAS+9B,EAAS9zC,GACzB,OAAI5B,MAAMC,QAAQy1C,GACTA,EAAQt7C,QAAQwH,IAAS,EACJ,kBAAZ8zC,EACTA,EAAQx6C,MAAM,KAAKd,QAAQwH,IAAS,IAClC2rB,EAASmoB,IACXA,EAAQn4C,KAAKqE,GAMxB,SAAS+zC,GAAYC,EAAmBrrC,GACtC,IAAI3L,EAAQg3C,EAAkBh3C,MAC1BS,EAAOu2C,EAAkBv2C,KACzBwrC,EAAS+K,EAAkB/K,OAC/B,IAAK,IAAIjxC,KAAOgF,EAAO,CACrB,IAAIi3C,EAAaj3C,EAAMhF,GACvB,GAAIi8C,EAAY,CACd,IAAIj0C,EAAO6zC,GAAiBI,EAAW3yB,kBACnCthB,IAAS2I,EAAO3I,IAClBk0C,GAAgBl3C,EAAOhF,EAAKyF,EAAMwrC,KAM1C,SAASiL,GACPl3C,EACAhF,EACAyF,EACA+K,GAEA,IAAI2rC,EAAYn3C,EAAMhF,IAClBm8C,GAAe3rC,GAAW2rC,EAAUrhB,MAAQtqB,EAAQsqB,KACtDqhB,EAAU7gB,kBAAkBzR,WAE9B7kB,EAAMhF,GAAO,KACbq0B,EAAO5uB,EAAMzF,GA/Mf25C,GAAU52C,IACVq2C,GAAWr2C,IACXywC,GAAYzwC,IACZqxC,GAAerxC,IACfyuC,GAAYzuC,IA8MZ,IAAIq5C,GAAe,CAAC34C,OAAQ8zB,OAAQnxB,OAEhCi2C,GAAY,CACdr0C,KAAM,aACNynC,UAAU,EAEVztB,MAAO,CACLlU,QAASsuC,GACTE,QAASF,GACT/d,IAAK,CAAC56B,OAAQsH,SAGhBwxC,QAAS,WACPx8C,KAAKiF,MAAQZ,OAAOa,OAAO,MAC3BlF,KAAK0F,KAAO,IAGd+hB,UAAW,WACT,IAAK,IAAIxnB,KAAOD,KAAKiF,MACnBk3C,GAAgBn8C,KAAKiF,MAAOhF,EAAKD,KAAK0F,OAI1C+2C,QAAS,WACP,IAAI1P,EAAS/sC,KAEbA,KAAKqxB,OAAO,WAAW,SAAUtI,GAC/BizB,GAAWjP,GAAQ,SAAU9kC,GAAQ,OAAO+V,GAAQ+K,EAAK9gB,SAE3DjI,KAAKqxB,OAAO,WAAW,SAAUtI,GAC/BizB,GAAWjP,GAAQ,SAAU9kC,GAAQ,OAAQ+V,GAAQ+K,EAAK9gB,UAI9D48B,OAAQ,WACN,IAAI1d,EAAOnnB,KAAK0gB,OAAOhB,QACnB4c,EAAQ0W,GAAuB7rB,GAC/BoC,EAAmB+S,GAASA,EAAM/S,iBACtC,GAAIA,EAAkB,CAEpB,IAAIthB,EAAO6zC,GAAiBvyB,GACxBvN,EAAMhc,KACN+N,EAAUiO,EAAIjO,QACdwuC,EAAUvgC,EAAIugC,QAClB,GAEGxuC,KAAa9F,IAAS+V,GAAQjQ,EAAS9F,KAEvCs0C,GAAWt0C,GAAQ+V,GAAQu+B,EAASt0C,GAErC,OAAOq0B,EAGT,IAAIogB,EAAQ18C,KACRiF,EAAQy3C,EAAMz3C,MACdS,EAAOg3C,EAAMh3C,KACbzF,EAAmB,MAAbq8B,EAAMr8B,IAGZspB,EAAiBqP,KAAK5f,KAAOuQ,EAAiBwR,IAAO,KAAQxR,EAAoB,IAAK,IACtF+S,EAAMr8B,IACNgF,EAAMhF,IACRq8B,EAAMf,kBAAoBt2B,EAAMhF,GAAKs7B,kBAErCjH,EAAO5uB,EAAMzF,GACbyF,EAAKe,KAAKxG,KAEVgF,EAAMhF,GAAOq8B,EACb52B,EAAKe,KAAKxG,GAEND,KAAKs+B,KAAO54B,EAAKvD,OAASqO,SAASxQ,KAAKs+B,MAC1C6d,GAAgBl3C,EAAOS,EAAK,GAAIA,EAAM1F,KAAKkxC,SAI/C5U,EAAM/0B,KAAK+mC,WAAY,EAEzB,OAAOhS,GAAUnV,GAAQA,EAAK,KAI9Bw1B,GAAoB,CACtBL,UAAWA,IAKb,SAASM,GAAe55C,GAEtB,IAAI65C,EAAY,CAChB,IAAgB,WAAc,OAAO3mB,GAEnC,IAAgB,WACd5e,GACE,0EAINjT,OAAOqJ,eAAe1K,EAAK,SAAU65C,GAKrC75C,EAAI85C,KAAO,CACTxlC,KAAMA,GACNiJ,OAAQA,EACR0f,aAAcA,GACd8c,eAAgBlf,IAGlB76B,EAAI4K,IAAMA,GACV5K,EAAI8Z,OAASyhB,GACbv7B,EAAIghC,SAAWA,GAGfhhC,EAAIuK,WAAa,SAAU5I,GAEzB,OADA+4B,GAAQ/4B,GACDA,GAGT3B,EAAIgF,QAAU3D,OAAOa,OAAO,MAC5B8wB,EAAYpwB,SAAQ,SAAU+T,GAC5B3W,EAAIgF,QAAQ2R,EAAO,KAAOtV,OAAOa,OAAO,SAK1ClC,EAAIgF,QAAQk4B,MAAQl9B,EAEpBud,EAAOvd,EAAIgF,QAAQ8T,WAAY6gC,IAE/B/B,GAAQ53C,GACRk4C,GAAYl4C,GACZm4C,GAAWn4C,GACX44C,GAAmB54C,GAGrB45C,GAAc55C,IAEdqB,OAAOqJ,eAAe1K,GAAIC,UAAW,YAAa,CAChD0K,IAAK6qB,KAGPn0B,OAAOqJ,eAAe1K,GAAIC,UAAW,cAAe,CAClD0K,IAAK,WAEH,OAAO3N,KAAK0wC,QAAU1wC,KAAK0wC,OAAOsM,cAKtC34C,OAAOqJ,eAAe1K,GAAK,0BAA2B,CACpDgH,MAAO6iC,KAGT7pC,GAAIqP,QAAU,SAKd,IAAI4qC,GAAY,iBACZC,GAAa,kBACbC,GAAW,gBACXC,GAAgB,qBAGpB,SAASC,GAAK5sC,EAASkgC,GACnB,IAAI3sC,EAAS,GAGb,OAFAs5C,GAAS7sC,EAASkgC,GAClB4M,GAAM9sC,EAASkgC,EAAK,GAAI3sC,GACjBA,EAGX,SAASs5C,GAAS7sC,EAASkgC,GACvB,GAAIlgC,IAAYkgC,EAAhB,CACA,IAAI6M,EAAkB7jC,GAAKlJ,GACvBgtC,EAAc9jC,GAAKg3B,GACvB,GAAI6M,GAAmBN,IAAcO,GAAeP,IAChD,GAAG74C,OAAOqB,KAAK+K,GAAStO,QAAUkC,OAAOqB,KAAKirC,GAAKxuC,OAC/C,IAAK,IAAIlC,KAAO0wC,EAAK,CACjB,IAAI+M,EAAejtC,EAAQxQ,QACNyU,IAAjBgpC,EACAjtC,EAAQxQ,GAAO,KAEfq9C,GAASI,EAAc/M,EAAI1wC,UAIhCu9C,GAAmBP,IAAaQ,GAAeR,IAClDxsC,EAAQtO,QAAUwuC,EAAIxuC,QACtBwuC,EAAI/qC,SAAQ,SAAUiL,EAAMjK,GACxB02C,GAAS7sC,EAAQ7J,GAAQiK,OAMzC,SAAS8sC,GAAgBC,EAAaC,GAClC,OACKD,IAAgBT,IAAYS,IAAgBR,IAC5CS,IAAYV,IAAYU,IAAYT,GAO7C,SAASG,GAAM9sC,EAASkgC,EAAK1qB,EAAMjiB,GAC/B,GAAIyM,IAAYkgC,EAAhB,CACA,IAAI6M,EAAkB7jC,GAAKlJ,GACvBgtC,EAAc9jC,GAAKg3B,GACvB,GAAI6M,GAAmBN,GACnB,GAAIO,GAAeP,IAAc74C,OAAOqB,KAAK+K,GAAStO,OAASkC,OAAOqB,KAAKirC,GAAKxuC,OAC5E27C,GAAU95C,EAAQiiB,EAAMxV,OACrB,CACH,IAAIM,EAAO,SAAW9Q,GAClB,IAAIy9C,EAAejtC,EAAQxQ,GACvB89C,EAAWpN,EAAI1wC,GACf29C,EAAcjkC,GAAK+jC,GACnBG,EAAUlkC,GAAKokC,GACnB,GAAIH,GAAeX,IAAaW,GAAeV,GACvCQ,IAAiB/M,EAAI1wC,IAAQ09C,GAAgBC,EAAaC,IAC1DC,GAAU95C,GAAiB,IAARiiB,EAAa,GAAKA,EAAO,KAAOhmB,EAAKy9C,QAEzD,GAAIE,GAAeX,GAClBY,GAAWZ,IAGPS,EAAav7C,OAAS47C,EAAS57C,OAFnC27C,GAAU95C,GAAiB,IAARiiB,EAAa,GAAKA,EAAO,KAAOhmB,EAAKy9C,GAKpDA,EAAa93C,SAAQ,SAAUiL,EAAMjK,GACjC22C,GAAM1sC,EAAMktC,EAASn3C,IAAiB,IAARqf,EAAa,GAAKA,EAAO,KAAOhmB,EAAM,IAAM2G,EAAQ,IAAK5C,WAIhG,GAAI45C,GAAeV,GACtB,GAAIW,GAAWX,IAAc74C,OAAOqB,KAAKg4C,GAAcv7C,OAASkC,OAAOqB,KAAKq4C,GAAU57C,OAClF27C,GAAU95C,GAAiB,IAARiiB,EAAa,GAAKA,EAAO,KAAOhmB,EAAKy9C,QAExD,IAAK,IAAIM,KAAUN,EACfH,GAAMG,EAAaM,GAASD,EAASC,IAAkB,IAAR/3B,EAAa,GAAKA,EAAO,KAAOhmB,EAAM,IAAM+9C,EAAQh6C,IAMnH,IAAK,IAAI/D,KAAOwQ,EAASM,EAAM9Q,QAE5Bu9C,GAAmBP,GACtBQ,GAAeR,IAGXxsC,EAAQtO,OAASwuC,EAAIxuC,OAFzB27C,GAAU95C,EAAQiiB,EAAMxV,GAKpBA,EAAQ7K,SAAQ,SAAUiL,EAAMjK,GAC5B22C,GAAM1sC,EAAM8/B,EAAI/pC,GAAQqf,EAAO,IAAMrf,EAAQ,IAAK5C,MAK9D85C,GAAU95C,EAAQiiB,EAAMxV,IAIhC,SAASqtC,GAAU95C,EAAQi6C,EAAGpwC,GAEtB7J,EAAOi6C,GAAKpwC,EAIpB,SAAS8L,GAAKhV,GACV,OAAON,OAAOpB,UAAUtB,SAASkD,KAAKF,GAK1C,SAASu5C,GAAiBtjC,GACtB,GAAIA,EAAGujC,uBAAyBvjC,EAAGujC,sBAAsBh8C,OAAQ,CAC7D,GAAI,wHAAY+e,cAAe,CAC3B,IAAIvF,EAAaf,EAAGK,OACpB5D,QAAQ+mC,IAAI,MAAQ,IAAI76C,KAAQ,MAAQoY,EAAWuK,IAAMvK,EAAWL,OAAS,KAAOV,EAAGk/B,KACnF,oBAAsBl/B,EAAGujC,sBAAsBh8C,OAAS,KAEhE,IAAIohC,EAAS3oB,EAAGujC,sBAAsBv8C,MAAM,GAC5CgZ,EAAGujC,sBAAsBh8C,OAAS,EAClC,IAAK,IAAI8B,EAAI,EAAGA,EAAIs/B,EAAOphC,OAAQ8B,IAC/Bs/B,EAAOt/B,MAKnB,SAASo6C,GAAiBzjC,GACtB,OAAOlT,GAAMuG,MAAK,SAAU6iB,GAAW,OAAOlW,EAAGq5B,WAAanjB,KAGlE,SAASwtB,GAAW1jC,EAAIqpB,GAGpB,IAAKrpB,EAAG89B,sBAAwB2F,GAAiBzjC,GAAK,CAClD,GAAG,wHAAYsG,cAAc,CACzB,IAAIvF,EAAaf,EAAGK,OACpB5D,QAAQ+mC,IAAI,MAAQ,IAAI76C,KAAQ,MAAQoY,EAAWuK,IAAMvK,EAAWL,OAAS,KAAOV,EAAGk/B,KACnF,iBAER,OAAO9V,GAASC,EAAIrpB,GAEpB,GAAG,wHAAYsG,cAAc,CACzB,IAAIq9B,EAAe3jC,EAAGK,OACtB5D,QAAQ+mC,IAAI,MAAQ,IAAI76C,KAAQ,MAAQg7C,EAAar4B,IAAMq4B,EAAajjC,OAAS,KAAOV,EAAGk/B,KACvF,gBAGZ,IAAI5V,EAgBJ,GAfKtpB,EAAGujC,wBACJvjC,EAAGujC,sBAAwB,IAE/BvjC,EAAGujC,sBAAsB13C,MAAK,WAC1B,GAAIw9B,EACA,IACIA,EAAGp/B,KAAK+V,GACV,MAAOvB,IACLopB,GAAYppB,GAAGuB,EAAI,iBAEhBspB,GACPA,EAAStpB,OAIZqpB,GAAyB,qBAAZr8B,QACd,OAAO,IAAIA,SAAQ,SAAUC,GACzBq8B,EAAWr8B,KAOvB,SAAS22C,GAAcv+C,EAAK+J,GAE1B,OAAIA,IACEA,EAAM4vB,QAAU5vB,EAAM4zB,mBACjB,GAGJ5zB,EAGT,SAASy0C,GAAc7jC,GAErB,IAAIkK,EAAMzgB,OAAOa,OAAO,MACpBw5C,EAAW,GAAGt4C,OAChB/B,OAAOqB,KAAKkV,EAAGm6B,OAAS,IACxB1wC,OAAOqB,KAAKkV,EAAGi+B,mBAAqB,KAEtC6F,EAASC,QAAO,SAAS75B,EAAK7kB,GAE5B,OADA6kB,EAAI7kB,GAAO2a,EAAG3a,GACP6kB,IACNA,GAGH,IAAI85B,EAAsBhkC,EAAGikC,2BAA6BjkC,EAAGkkC,qBACzDC,EAAcH,GAAuBA,EAAoBG,YAiB7D,OAhBIA,GACF16C,OAAOqB,KAAKq5C,GAAan5C,SAAQ,SAAU3F,GACzC6kB,EAAI7kB,GAAO2a,EAAG3a,MAKlBoE,OAAOwF,OAAOib,EAAKlK,EAAG0E,IAAI/X,MAAQ,IAEhClB,MAAMC,QAAQsU,EAAG2K,SAAS3D,aAC6B,IAAvDhH,EAAG2K,SAAS3D,UAAUnhB,QAAQ,sBAE9BqkB,EAAI,QAAUlK,EAAG3S,KACjB6c,EAAI,SAAWlK,EAAG5Q,OAGbxH,KAAKC,MAAMD,KAAK2e,UAAU2D,EAAK05B,KAGxC,IAAIQ,GAAQ,SAASrQ,EAAUrS,GAC7B,IAAIyQ,EAAS/sC,KAEb,GAAc,OAAVs8B,IAGgB,SAAhBt8B,KAAKuf,QAAqC,cAAhBvf,KAAKuf,QAAwB,CACzD,IAAI5D,EAAa3b,KAAKib,OAClB1T,EAAOlD,OAAOa,OAAO,MACzB,IACEqC,EAAOk3C,GAAcz+C,MACrB,MAAOuJ,GACP8N,QAAQ3U,MAAM6G,GAEhBhC,EAAK03C,cAAgBtjC,EAAWpU,KAAK03C,cACrC,IAAIC,EAAS76C,OAAOa,OAAO,MAC3Bb,OAAOqB,KAAK6B,GAAM3B,SAAQ,SAAU3F,GAClCi/C,EAAOj/C,GAAO0b,EAAWpU,KAAKtH,MAEhC,IAAIk/C,GAAoC,IAAzBn/C,KAAKo/C,gBAA4B73C,EAAO81C,GAAK91C,EAAM23C,GAC9D76C,OAAOqB,KAAKy5C,GAAUh9C,QACpB,wHAAY+e,eACd7J,QAAQ+mC,IAAI,MAAQ,IAAI76C,KAAQ,MAAQoY,EAAWuK,IAAMvK,EAAWL,OAAS,KAAOtb,KAAK85C,KACvF,QACAt3C,KAAK2e,UAAUg+B,IAEnBn/C,KAAK04C,qBAAsB,EAC3B/8B,EAAWqH,QAAQm8B,GAAU,WAC3BpS,EAAO2L,qBAAsB,EAC7BwF,GAAiBnR,OAGnBmR,GAAiBl+C,QAOvB,SAASq/C,MAIT,SAASC,GACP1kC,EACAskB,EACAkP,GAEA,IAAKxzB,EAAG2E,OACN,OAAO3E,EAES,QAAdA,EAAG2E,SACL3E,EAAG2K,SAASsf,OAASwa,IAElBzkC,EAAG2K,SAASsf,SACfjqB,EAAG2K,SAASsf,OAASwa,GAGdzkC,EAAG2K,SAASg6B,UAA+C,MAAnC3kC,EAAG2K,SAASg6B,SAASr7C,OAAO,IACvD0W,EAAG2K,SAAS2Z,IAAMA,EAClB5nB,GACE,wLAGAsD,GAGFtD,GACE,sEACAsD,KAMPA,EAAGq/B,YAAcvzB,GAAS9L,EAAI,eAE/B,IAAI4kC,EAAkB,WACpB5kC,EAAG05B,QAAQ15B,EAAG+2B,UAAWvD,IAc3B,OARA,IAAI6I,GAAQr8B,EAAI4kC,EAAiBz6C,EAAM,CACrCwxC,OAAQ,WACF37B,EAAGuN,aAAevN,EAAGyzB,cACvB3nB,GAAS9L,EAAI,mBAGhB,GACHwzB,GAAY,EACLxzB,EAKT,SAAS6kC,GACPC,EACAC,GAEA,OAAIpsB,EAAMmsB,IAAgBnsB,EAAMosB,GACvBv5C,GAAOs5C,EAAaE,GAAeD,IAGrC,GAGT,SAASv5C,GAAQinB,EAAG8H,GAClB,OAAO9H,EAAI8H,EAAK9H,EAAI,IAAM8H,EAAK9H,EAAK8H,GAAK,GAG3C,SAASyqB,GAAgB51C,GACvB,OAAI3D,MAAMC,QAAQ0D,GACT61C,GAAe71C,GAEpBtF,EAASsF,GACJ81C,GAAgB91C,GAEJ,kBAAVA,EACFA,EAGF,GAGT,SAAS61C,GAAgB71C,GAGvB,IAFA,IACI+1C,EADA55C,EAAM,GAEDlC,EAAI,EAAG+oB,EAAIhjB,EAAM7H,OAAQ8B,EAAI+oB,EAAG/oB,IACnCsvB,EAAMwsB,EAAcH,GAAe51C,EAAM/F,MAAwB,KAAhB87C,IAC/C55C,IAAOA,GAAO,KAClBA,GAAO45C,GAGX,OAAO55C,EAGT,SAAS25C,GAAiB91C,GACxB,IAAI7D,EAAM,GACV,IAAK,IAAIlG,KAAO+J,EACVA,EAAM/J,KACJkG,IAAOA,GAAO,KAClBA,GAAOlG,GAGX,OAAOkG,EAKT,IAAI65C,GAAiBh7C,GAAO,SAAUi7C,GACpC,IAAI95C,EAAM,GACN+5C,EAAgB,gBAChBC,EAAoB,QAOxB,OANAF,EAAQ1+C,MAAM2+C,GAAet6C,SAAQ,SAAUiL,GAC7C,GAAIA,EAAM,CACR,IAAIinC,EAAMjnC,EAAKtP,MAAM4+C,GACrBrI,EAAI31C,OAAS,IAAMgE,EAAI2xC,EAAI,GAAG1pC,QAAU0pC,EAAI,GAAG1pC,YAG5CjI,KAIT,SAASi6C,GAAuBC,GAC9B,OAAIh6C,MAAMC,QAAQ+5C,GACTnrB,EAASmrB,GAEU,kBAAjBA,EACFL,GAAeK,GAEjBA,EAKT,IAAIC,GAAa,CAAC,sBAAuB,6BAA8B,sBAAuB,mBAE9F,SAASC,GAAU57C,EAAKshB,GACtB,IAAIjY,EAAQiY,EAAK1kB,MAAM,KACnBtB,EAAM+N,EAAM,GAIhB,OAH4B,IAAxB/N,EAAIQ,QAAQ,UACdR,EAAMuQ,SAASvQ,EAAI0D,QAAQ,OAAQ,MAEhB,IAAjBqK,EAAM7L,OACDwC,EAAI1E,GAENsgD,GAAU57C,EAAI1E,GAAM+N,EAAMpM,MAAM,GAAG1B,KAAK,MAGjD,SAASsgD,GAAcx9C,GAErBA,EAAIkzB,OAAOM,aAAe,SAASjtB,EAAKqR,EAAI8nB,GAC1C1/B,EAAI85C,KAAKxlC,KAAM,YAAcorB,EAAO,MAAUn5B,EAAI5H,WAAc,IAAOiZ,GACvEvD,QAAQ3U,MAAM6G,GAEd,IAAIkF,EAAwB,oBAAXD,QAAyBA,SACtCC,GAAOA,EAAIgyC,SACbhyC,EAAIgyC,QAAQl3C,IAIhB,IAAIm3C,EAAU19C,EAAIC,UAAU8V,MAE5B/V,EAAIC,UAAU8V,MAAQ,SAASmE,GAC7B,GAAIld,KAAKib,QAAUiC,EAAO,CACxB,IAAIzB,EAAezb,KAAKib,OAAO,kBAAoBjb,KAAKib,OAAO,gBAC/D,GAAIQ,EACF,IACEA,EAAa5W,KAAK7E,KAAKib,OAAQiC,EAAO,CACpCmH,SAAUyQ,EAAQjc,UAAW,KAE/B,MAAOnW,KAKb,OAAOg+C,EAAQ9oC,MAAM5X,KAAM6Y,YAG7B7V,EAAIC,UAAUyuC,UAAY,SAASltC,GACjC,OAAO85C,GAAWt+C,KAAMwE,IAG1B87C,GAAW16C,SAAQ,SAAUuB,GAC3BnE,EAAIC,UAAUkE,GAAU,SAASiJ,GAC/B,OAAIpQ,KAAKib,QAAUjb,KAAKib,OAAO9T,GACtBnH,KAAKib,OAAO9T,GAAQiJ,GAGX,qBAAPuwC,GAGI,wBAAXx5C,EAEKw5C,GAAGC,oBAAoBxwC,GACV,+BAAXjJ,EAEFw5C,GAAGE,2BAA2BzwC,QAFhC,OANP,MAcJpN,EAAIC,UAAU69C,eAAiBnZ,GAE/B3kC,EAAIC,UAAU89C,kBAAoBlZ,GAElC7kC,EAAIC,UAAU+c,YAAc,SAASrZ,EAAMyJ,GACzC,IAAIwK,EAAK5a,KAETo6B,KACA,IAEItV,EAFA8f,EAAWhqB,EAAG2K,SAAS5e,GACvB+7B,EAAO/7B,EAAO,QAElB,GAAIi+B,EACF,IAAK,IAAI3gC,EAAI,EAAG2xC,EAAIhR,EAASziC,OAAQ8B,EAAI2xC,EAAG3xC,IAC1C6gB,EAAMie,GAAwB6B,EAAS3gC,GAAI2W,EAAIxK,EAAO,CAACA,GAAQ,KAAMwK,EAAI8nB,GAO7E,OAJI9nB,EAAGu4B,eACLv4B,EAAG7B,MAAM,QAAUpS,EAAMyJ,GAE3BmqB,KACOzV,GAGT9hB,EAAIC,UAAU+9C,YAAc,SAASlhD,EAAQG,EAAK+J,EAAOi3C,GACnD56C,MAAMC,QAAQ26C,MACmB,IAA/BA,EAAUxgD,QAAQ,UACpBuJ,EAAQA,EAAMoE,SAEqB,IAAjC6yC,EAAUxgD,QAAQ,YACpBuJ,EAAQhK,KAAK+rC,GAAG/hC,KAGflK,IACHA,EAASE,MAGXgD,EAAI4K,IAAI9N,EAAQG,EAAK+J,IAGvBhH,EAAIC,UAAUi+C,WAAa,SAASphD,EAAQG,EAAK+J,GAC1ClK,IACHA,EAASE,MAGXgD,EAAI4K,IAAI9N,EAAQG,EAAK+J,IAGvBhH,EAAIC,UAAUk+C,WAAa,SAAStwC,GAClC,OAAIjM,EAAciM,IACTA,EAAK,UAEPA,GAGT7N,EAAIC,UAAU+gB,YAAc,SAASN,EAAU5jB,GAC7C,OAAOygD,GAAUzgD,GAAUE,KAAM0jB,IAInC1gB,EAAIC,UAAUm+C,YAAc,SAASzB,EAAcD,GACjD,OAAOD,GAAYC,EAAaC,IAGlC38C,EAAIC,UAAUo+C,YAAc,SAASC,EAAcC,GACjD,IAAKD,IAAiBC,EACpB,MAAO,GAET,IAAIC,EAAkBpB,GAAsBkB,GACxCG,EAAWF,EAAchhC,EAAOghC,EAAaC,GAAmBA,EACpE,OAAOn9C,OAAOqB,KAAK+7C,GAAUjgD,KAAI,SAAUyG,GAAQ,OAASwsB,EAAUxsB,GAAS,IAAOw5C,EAASx5C,MAAY/H,KAAK,MAGlH8C,EAAIC,UAAUy+C,MAAQ,SAAS34B,EAAK44B,GAElC,IAAI78B,EAAK7gB,EAAG+oB,EAAGtnB,EAAMzF,EACrB,GAAIoG,MAAMC,QAAQyiB,GAAM,CAEtB,IADAjE,EAAM,IAAIze,MAAM0iB,EAAI5mB,QACf8B,EAAI,EAAG+oB,EAAIjE,EAAI5mB,OAAQ8B,EAAI+oB,EAAG/oB,IACjC6gB,EAAI7gB,GAAK09C,EAAS54B,EAAI9kB,GAAIA,GAE5B,OAAO6gB,EACF,GAAIpgB,EAASqkB,GAAM,CAGxB,IAFArjB,EAAOrB,OAAOqB,KAAKqjB,GACnBjE,EAAMzgB,OAAOa,OAAO,MACfjB,EAAI,EAAG+oB,EAAItnB,EAAKvD,OAAQ8B,EAAI+oB,EAAG/oB,IAClChE,EAAMyF,EAAKzB,GACX6gB,EAAI7kB,GAAO0hD,EAAS54B,EAAI9oB,GAAMA,EAAKgE,GAErC,OAAO6gB,EACF,GAAmB,kBAARiE,EAAkB,CAElC,IADAjE,EAAM,IAAIze,MAAM0iB,GACX9kB,EAAI,EAAG+oB,EAAIjE,EAAK9kB,EAAI+oB,EAAG/oB,IAE1B6gB,EAAI7gB,GAAK09C,EAAS19C,EAAGA,GAEvB,OAAO6gB,EAET,MAAO,IAOX,IAAI88B,GAAoB,CAEpB,WACA,SACA,SACA,oBACA,iBACA,gBACA,UACA,uBAEA,SACA,SAEA,UAEA,WACA,oBACA,gBACA,eACA,mBACA,kBACA,oBACA,WACA,eACA,2BACA,cACA,oCACA,sCACA,oCACA,sBACA,mBAGA,aACA,aACA,gBAEJ,SAASC,GAAiB7+C,GAGtB,IAAI8+C,EAAY9+C,EAAIud,OACpBvd,EAAIud,OAAS,SAASZ,GAClBA,EAAgBA,GAAiB,GAEjC,IAAI5S,EAAU4S,EAAc5S,QAU5B,OATIA,GACA1I,OAAOqB,KAAKqH,GAASnH,SAAQ,SAAUkR,IACU,IAAzC8qC,GAAkBnhD,QAAQqW,KAC1B6I,EAAc7I,GAAc/J,EAAQ+J,UAC7B/J,EAAQ+J,OAKpBgrC,EAAUj9C,KAAK7E,KAAM2f,IAGhC,IAAIoiC,EAAa/+C,EAAIkzB,OAAOC,sBACxBnwB,EAAY+7C,EAAWvF,QAC3BoF,GAAkBh8C,SAAQ,SAAUe,GAChCo7C,EAAWp7C,GAAQX,KAGvBhD,EAAIC,UAAUme,oBAAsBwgC,GAMxC5+C,GAAIC,UAAU0xC,UAAYqK,GAG1Bh8C,GAAIC,UAAU0mB,OAAS,SACnBuV,EACAkP,GAEA,OAAOkR,GAAiBt/C,KAAMk/B,EAAIkP,IAGtCyT,GAAiB7+C,IACjBw9C,GAAcx9C,IAIC,iB,mECx7LA,SAASg/C,EACtBC,EACApd,EACAkG,EACAmX,EACAC,EACAC,EACAC,EACAC,EACAxmC,EACAymC,GAGA,IA4CI57C,EA5CAqB,EAAmC,oBAAlBi6C,EACjBA,EAAcj6C,QACdi6C,EAGJ,GAAInmC,EAAY,CACT9T,EAAQ8T,aACX9T,EAAQ8T,WAAa,IAEvB,IAAIhX,EAAST,OAAOpB,UAAUqB,eAC9B,IAAK,IAAI2D,KAAQ6T,EACXhX,EAAOD,KAAKiX,EAAY7T,KAAUnD,EAAOD,KAAKmD,EAAQ8T,WAAY7T,KACpED,EAAQ8T,WAAW7T,GAAQ6T,EAAW7T,IA8D5C,GAzDIs6C,IACkC,oBAA1BA,EAAS71C,eACpB61C,EAAS71C,aAAe,CAAC61C,EAAS71C,gBAEhC61C,EAAS71C,eAAiB61C,EAAS71C,aAAe,KAAKylB,SAAQ,WAC9DnyB,KAAKuiD,EAASC,UAAYxiD,SAE3BgI,EAAQ6X,SAAW7X,EAAQ6X,OAAS,KAAKpZ,KAAK87C,IAI7C1d,IACF78B,EAAQ68B,OAASA,EACjB78B,EAAQ+iC,gBAAkBA,EAC1B/iC,EAAQklC,WAAY,GAIlBgV,IACFl6C,EAAQwnC,YAAa,GAInB4S,IACFp6C,EAAQulC,SAAW,UAAY6U,GAI7BC,GACF17C,EAAO,SAAUsa,GAEfA,EACEA,GACCjhB,KAAK0wC,QAAU1wC,KAAK0wC,OAAOsM,YAC3Bh9C,KAAKod,QAAUpd,KAAKod,OAAOszB,QAAU1wC,KAAKod,OAAOszB,OAAOsM,WAEtD/7B,GAA0C,qBAAxBwhC,sBACrBxhC,EAAUwhC,qBAGRN,GACFA,EAAat9C,KAAK7E,KAAMihB,GAGtBA,GAAWA,EAAQyhC,uBACrBzhC,EAAQyhC,sBAAsBlxB,IAAI6wB,IAKtCr6C,EAAQ26C,aAAeh8C,GACdw7C,IACTx7C,EAAO27C,EACH,WAAcH,EAAat9C,KAAK7E,KAAMA,KAAK25B,MAAMpU,SAASq9B,aAC1DT,GAGFx7C,EACF,GAAIqB,EAAQwnC,WAAY,CAGtBxnC,EAAQ66C,cAAgBl8C,EAExB,IAAIm8C,EAAiB96C,EAAQ68B,OAC7B78B,EAAQ68B,OAAS,SAAmCke,EAAG9hC,GAErD,OADAta,EAAK9B,KAAKoc,GACH6hC,EAAeC,EAAG9hC,QAEtB,CAEL,IAAImqB,EAAWpjC,EAAQ0E,aACvB1E,EAAQ0E,aAAe0+B,EACnB,GAAGhlC,OAAOglC,EAAUzkC,GACpB,CAACA,GAIT,MAAO,CACLylB,QAAS61B,EACTj6C,QAASA,GAnHb", "file": "common/vendor.js", "sourcesContent": ["const objectKeys = [\r\n  'qy',\r\n  'env',\r\n  'error',\r\n  'version',\r\n  'lanDebug',\r\n  'cloud',\r\n  'serviceMarket',\r\n  'router',\r\n  'worklet',\r\n  '__webpack_require_UNI_MP_PLUGIN__'\r\n]\r\nconst singlePageDisableKey = [\r\n  'lanDebug',\r\n  'router',\r\n  'worklet'\r\n]\r\nconst target = typeof globalThis !== 'undefined' ? globalThis : (function () {\r\n  return this\r\n})()\r\n\r\nconst key = ['w', 'x'].join('')\r\nconst oldWx = target[key]\r\nconst launchOption = oldWx.getLaunchOptionsSync ? oldWx.getLaunchOptionsSync() : null\r\n\r\nfunction isWxKey (key) {\r\n  if (launchOption && launchOption.scene === 1154 && singlePageDisableKey.includes(key)) {\r\n    return false\r\n  }\r\n  return objectKeys.indexOf(key) > -1 || typeof oldWx[key] === 'function'\r\n}\r\n\r\nfunction initWx () {\r\n  const newWx = {}\r\n  for (const key in oldWx) {\r\n    if (isWxKey(key)) {\r\n      // TODO wrapper function\r\n      newWx[key] = oldWx[key]\r\n    }\r\n  }\r\n  return newWx\r\n}\r\ntarget[key] = initWx()\r\nif (!target[key].canIUse('getAppBaseInfo')) {\r\n  target[key].getAppBaseInfo = target[key].getSystemInfoSync\r\n}\r\n\r\nif (!target[key].canIUse('getWindowInfo')) {\r\n  target[key].getWindowInfo = target[key].getSystemInfoSync\r\n}\r\n\r\nif (!target[key].canIUse('getDeviceInfo')) {\r\n  target[key].getDeviceInfo = target[key].getSystemInfoSync\r\n}\r\nexport default target[key]\r\n", "import { initVueI18n } from '@dcloudio/uni-i18n';\r\nimport Vue from 'vue';\r\n\r\nlet realAtob;\r\n\r\nconst b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\r\nconst b64re = /^(?:[A-Za-z\\d+/]{4})*?(?:[A-Za-z\\d+/]{2}(?:==)?|[A-Za-z\\d+/]{3}=?)?$/;\r\n\r\nif (typeof atob !== 'function') {\r\n  realAtob = function (str) {\r\n    str = String(str).replace(/[\\t\\n\\f\\r ]+/g, '');\r\n    if (!b64re.test(str)) { throw new Error(\"Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.\") }\r\n\r\n    // Adding the padding if missing, for semplicity\r\n    str += '=='.slice(2 - (str.length & 3));\r\n    var bitmap; var result = ''; var r1; var r2; var i = 0;\r\n    for (; i < str.length;) {\r\n      bitmap = b64.indexOf(str.charAt(i++)) << 18 | b64.indexOf(str.charAt(i++)) << 12 |\r\n                    (r1 = b64.indexOf(str.charAt(i++))) << 6 | (r2 = b64.indexOf(str.charAt(i++)));\r\n\r\n      result += r1 === 64 ? String.fromCharCode(bitmap >> 16 & 255)\r\n        : r2 === 64 ? String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255)\r\n          : String.fromCharCode(bitmap >> 16 & 255, bitmap >> 8 & 255, bitmap & 255);\r\n    }\r\n    return result\r\n  };\r\n} else {\r\n  // 注意atob只能在全局对象上调用，例如：`const Base64 = {atob};Base64.atob('xxxx')`是错误的用法\r\n  realAtob = atob;\r\n}\r\n\r\nfunction b64DecodeUnicode (str) {\r\n  return decodeURIComponent(realAtob(str).split('').map(function (c) {\r\n    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)\r\n  }).join(''))\r\n}\r\n\r\nfunction getCurrentUserInfo () {\r\n  const token = ( wx).getStorageSync('uni_id_token') || '';\r\n  const tokenArr = token.split('.');\r\n  if (!token || tokenArr.length !== 3) {\r\n    return {\r\n      uid: null,\r\n      role: [],\r\n      permission: [],\r\n      tokenExpired: 0\r\n    }\r\n  }\r\n  let userInfo;\r\n  try {\r\n    userInfo = JSON.parse(b64DecodeUnicode(tokenArr[1]));\r\n  } catch (error) {\r\n    throw new Error('获取当前用户信息出错，详细错误信息为：' + error.message)\r\n  }\r\n  userInfo.tokenExpired = userInfo.exp * 1000;\r\n  delete userInfo.exp;\r\n  delete userInfo.iat;\r\n  return userInfo\r\n}\r\n\r\nfunction uniIdMixin (Vue) {\r\n  Vue.prototype.uniIDHasRole = function (roleId) {\r\n    const {\r\n      role\r\n    } = getCurrentUserInfo();\r\n    return role.indexOf(roleId) > -1\r\n  };\r\n  Vue.prototype.uniIDHasPermission = function (permissionId) {\r\n    const {\r\n      permission\r\n    } = getCurrentUserInfo();\r\n    return this.uniIDHasRole('admin') || permission.indexOf(permissionId) > -1\r\n  };\r\n  Vue.prototype.uniIDTokenValid = function () {\r\n    const {\r\n      tokenExpired\r\n    } = getCurrentUserInfo();\r\n    return tokenExpired > Date.now()\r\n  };\r\n}\r\n\r\nconst _toString = Object.prototype.toString;\r\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\r\nfunction isFn (fn) {\r\n  return typeof fn === 'function'\r\n}\r\n\r\nfunction isStr (str) {\r\n  return typeof str === 'string'\r\n}\r\n\r\nfunction isObject (obj) {\r\n  return obj !== null && typeof obj === 'object'\r\n}\r\n\r\nfunction isPlainObject (obj) {\r\n  return _toString.call(obj) === '[object Object]'\r\n}\r\n\r\nfunction hasOwn (obj, key) {\r\n  return hasOwnProperty.call(obj, key)\r\n}\r\n\r\nfunction noop () {}\r\n\r\n/**\r\n * Create a cached version of a pure function.\r\n */\r\nfunction cached (fn) {\r\n  const cache = Object.create(null);\r\n  return function cachedFn (str) {\r\n    const hit = cache[str];\r\n    return hit || (cache[str] = fn(str))\r\n  }\r\n}\r\n\r\n/**\r\n * Camelize a hyphen-delimited string.\r\n */\r\nconst camelizeRE = /-(\\w)/g;\r\nconst camelize = cached((str) => {\r\n  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '')\r\n});\r\n\r\nfunction sortObject (obj) {\r\n  const sortObj = {};\r\n  if (isPlainObject(obj)) {\r\n    Object.keys(obj).sort().forEach(key => {\r\n      sortObj[key] = obj[key];\r\n    });\r\n  }\r\n  return !Object.keys(sortObj) ? obj : sortObj\r\n}\r\n\r\nconst HOOKS = [\r\n  'invoke',\r\n  'success',\r\n  'fail',\r\n  'complete',\r\n  'returnValue'\r\n];\r\n\r\nconst globalInterceptors = {};\r\nconst scopedInterceptors = {};\r\n\r\nfunction mergeHook (parentVal, childVal) {\r\n  const res = childVal\r\n    ? parentVal\r\n      ? parentVal.concat(childVal)\r\n      : Array.isArray(childVal)\r\n        ? childVal : [childVal]\r\n    : parentVal;\r\n  return res\r\n    ? dedupeHooks(res)\r\n    : res\r\n}\r\n\r\nfunction dedupeHooks (hooks) {\r\n  const res = [];\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    if (res.indexOf(hooks[i]) === -1) {\r\n      res.push(hooks[i]);\r\n    }\r\n  }\r\n  return res\r\n}\r\n\r\nfunction removeHook (hooks, hook) {\r\n  const index = hooks.indexOf(hook);\r\n  if (index !== -1) {\r\n    hooks.splice(index, 1);\r\n  }\r\n}\r\n\r\nfunction mergeInterceptorHook (interceptor, option) {\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      interceptor[hook] = mergeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction removeInterceptorHook (interceptor, option) {\r\n  if (!interceptor || !option) {\r\n    return\r\n  }\r\n  Object.keys(option).forEach(hook => {\r\n    if (HOOKS.indexOf(hook) !== -1 && isFn(option[hook])) {\r\n      removeHook(interceptor[hook], option[hook]);\r\n    }\r\n  });\r\n}\r\n\r\nfunction addInterceptor (method, option) {\r\n  if (typeof method === 'string' && isPlainObject(option)) {\r\n    mergeInterceptorHook(scopedInterceptors[method] || (scopedInterceptors[method] = {}), option);\r\n  } else if (isPlainObject(method)) {\r\n    mergeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction removeInterceptor (method, option) {\r\n  if (typeof method === 'string') {\r\n    if (isPlainObject(option)) {\r\n      removeInterceptorHook(scopedInterceptors[method], option);\r\n    } else {\r\n      delete scopedInterceptors[method];\r\n    }\r\n  } else if (isPlainObject(method)) {\r\n    removeInterceptorHook(globalInterceptors, method);\r\n  }\r\n}\r\n\r\nfunction wrapperHook (hook, params) {\r\n  return function (data) {\r\n    return hook(data, params) || data\r\n  }\r\n}\r\n\r\nfunction isPromise (obj) {\r\n  return !!obj && (typeof obj === 'object' || typeof obj === 'function') && typeof obj.then === 'function'\r\n}\r\n\r\nfunction queue (hooks, data, params) {\r\n  let promise = false;\r\n  for (let i = 0; i < hooks.length; i++) {\r\n    const hook = hooks[i];\r\n    if (promise) {\r\n      promise = Promise.resolve(wrapperHook(hook, params));\r\n    } else {\r\n      const res = hook(data, params);\r\n      if (isPromise(res)) {\r\n        promise = Promise.resolve(res);\r\n      }\r\n      if (res === false) {\r\n        return {\r\n          then () { }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return promise || {\r\n    then (callback) {\r\n      return callback(data)\r\n    }\r\n  }\r\n}\r\n\r\nfunction wrapperOptions (interceptor, options = {}) {\r\n  ['success', 'fail', 'complete'].forEach(name => {\r\n    if (Array.isArray(interceptor[name])) {\r\n      const oldCallback = options[name];\r\n      options[name] = function callbackInterceptor (res) {\r\n        queue(interceptor[name], res, options).then((res) => {\r\n          /* eslint-disable no-mixed-operators */\r\n          return isFn(oldCallback) && oldCallback(res) || res\r\n        });\r\n      };\r\n    }\r\n  });\r\n  return options\r\n}\r\n\r\nfunction wrapperReturnValue (method, returnValue) {\r\n  const returnValueHooks = [];\r\n  if (Array.isArray(globalInterceptors.returnValue)) {\r\n    returnValueHooks.push(...globalInterceptors.returnValue);\r\n  }\r\n  const interceptor = scopedInterceptors[method];\r\n  if (interceptor && Array.isArray(interceptor.returnValue)) {\r\n    returnValueHooks.push(...interceptor.returnValue);\r\n  }\r\n  returnValueHooks.forEach(hook => {\r\n    returnValue = hook(returnValue) || returnValue;\r\n  });\r\n  return returnValue\r\n}\r\n\r\nfunction getApiInterceptorHooks (method) {\r\n  const interceptor = Object.create(null);\r\n  Object.keys(globalInterceptors).forEach(hook => {\r\n    if (hook !== 'returnValue') {\r\n      interceptor[hook] = globalInterceptors[hook].slice();\r\n    }\r\n  });\r\n  const scopedInterceptor = scopedInterceptors[method];\r\n  if (scopedInterceptor) {\r\n    Object.keys(scopedInterceptor).forEach(hook => {\r\n      if (hook !== 'returnValue') {\r\n        interceptor[hook] = (interceptor[hook] || []).concat(scopedInterceptor[hook]);\r\n      }\r\n    });\r\n  }\r\n  return interceptor\r\n}\r\n\r\nfunction invokeApi (method, api, options, ...params) {\r\n  const interceptor = getApiInterceptorHooks(method);\r\n  if (interceptor && Object.keys(interceptor).length) {\r\n    if (Array.isArray(interceptor.invoke)) {\r\n      const res = queue(interceptor.invoke, options);\r\n      return res.then((options) => {\r\n        // 重新访问 getApiInterceptorHooks, 允许 invoke 中再次调用 addInterceptor,removeInterceptor\r\n        return api(\r\n          wrapperOptions(getApiInterceptorHooks(method), options),\r\n          ...params\r\n        )\r\n      })\r\n    } else {\r\n      return api(wrapperOptions(interceptor, options), ...params)\r\n    }\r\n  }\r\n  return api(options, ...params)\r\n}\r\n\r\nconst promiseInterceptor = {\r\n  returnValue (res) {\r\n    if (!isPromise(res)) {\r\n      return res\r\n    }\r\n    return new Promise((resolve, reject) => {\r\n      res.then(res => {\r\n        if (!res) {\r\n          resolve(res);\r\n          return\r\n        }\r\n        if (res[0]) {\r\n          reject(res[0]);\r\n        } else {\r\n          resolve(res[1]);\r\n        }\r\n      });\r\n    })\r\n  }\r\n};\r\n\r\nconst SYNC_API_RE =\r\n  /^\\$|__f__|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|rpx2px|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/;\r\n\r\nconst CONTEXT_API_RE = /^create|Manager$/;\r\n\r\n// Context例外情况\r\nconst CONTEXT_API_RE_EXC = ['createBLEConnection'];\r\n\r\n// 同步例外情况\r\nconst ASYNC_API = ['createBLEConnection', 'createPushMessage'];\r\n\r\nconst CALLBACK_API_RE = /^on|^off/;\r\n\r\nfunction isContextApi (name) {\r\n  return CONTEXT_API_RE.test(name) && CONTEXT_API_RE_EXC.indexOf(name) === -1\r\n}\r\nfunction isSyncApi (name) {\r\n  return SYNC_API_RE.test(name) && ASYNC_API.indexOf(name) === -1\r\n}\r\n\r\nfunction isCallbackApi (name) {\r\n  return CALLBACK_API_RE.test(name) && name !== 'onPush'\r\n}\r\n\r\nfunction handlePromise (promise) {\r\n  return promise.then(data => {\r\n    return [null, data]\r\n  })\r\n    .catch(err => [err])\r\n}\r\n\r\nfunction shouldPromise (name) {\r\n  if (\r\n    isContextApi(name) ||\r\n    isSyncApi(name) ||\r\n    isCallbackApi(name)\r\n  ) {\r\n    return false\r\n  }\r\n  return true\r\n}\r\n\r\n/* eslint-disable no-extend-native */\r\nif (!Promise.prototype.finally) {\r\n  Promise.prototype.finally = function (callback) {\r\n    const promise = this.constructor;\r\n    return this.then(\r\n      value => promise.resolve(callback()).then(() => value),\r\n      reason => promise.resolve(callback()).then(() => {\r\n        throw reason\r\n      })\r\n    )\r\n  };\r\n}\r\n\r\nfunction promisify (name, api) {\r\n  if (!shouldPromise(name) || !isFn(api)) {\r\n    return api\r\n  }\r\n  return function promiseApi (options = {}, ...params) {\r\n    if (isFn(options.success) || isFn(options.fail) || isFn(options.complete)) {\r\n      return wrapperReturnValue(name, invokeApi(name, api, options, ...params))\r\n    }\r\n    return wrapperReturnValue(name, handlePromise(new Promise((resolve, reject) => {\r\n      invokeApi(name, api, Object.assign({}, options, {\r\n        success: resolve,\r\n        fail: reject\r\n      }), ...params);\r\n    })))\r\n  }\r\n}\r\n\r\nconst EPS = 1e-4;\r\nconst BASE_DEVICE_WIDTH = 750;\r\nlet isIOS = false;\r\nlet deviceWidth = 0;\r\nlet deviceDPR = 0;\r\n\r\nfunction checkDeviceWidth() {\r\n  let windowWidth, pixelRatio, platform;\r\n\r\n  {\r\n    const windowInfo = typeof wx.getWindowInfo === 'function' && wx.getWindowInfo() ? wx.getWindowInfo() : wx.getSystemInfoSync();\r\n    const deviceInfo = typeof wx.getDeviceInfo === 'function' && wx.getDeviceInfo() ? wx.getDeviceInfo() : wx.getSystemInfoSync();\r\n\r\n    windowWidth = windowInfo.windowWidth;\r\n    pixelRatio = windowInfo.pixelRatio;\r\n    platform = deviceInfo.platform;\r\n  }\r\n\r\n  deviceWidth = windowWidth;\r\n  deviceDPR = pixelRatio;\r\n  isIOS = platform === 'ios';\r\n}\r\n\r\nfunction upx2px(number, newDeviceWidth) {\r\n  if (deviceWidth === 0) {\r\n    checkDeviceWidth();\r\n  }\r\n\r\n  number = Number(number);\r\n  if (number === 0) {\r\n    return 0\r\n  }\r\n  let result = (number / BASE_DEVICE_WIDTH) * (newDeviceWidth || deviceWidth);\r\n  if (result < 0) {\r\n    result = -result;\r\n  }\r\n  result = Math.floor(result + EPS);\r\n  if (result === 0) {\r\n    if (deviceDPR === 1 || !isIOS) {\r\n      result = 1;\r\n    } else {\r\n      result = 0.5;\r\n    }\r\n  }\r\n  return number < 0 ? -result : result\r\n}\r\n\r\nconst LOCALE_ZH_HANS = 'zh-Hans';\r\nconst LOCALE_ZH_HANT = 'zh-Hant';\r\nconst LOCALE_EN = 'en';\r\nconst LOCALE_FR = 'fr';\r\nconst LOCALE_ES = 'es';\r\n\r\nconst messages = {};\r\n\r\nfunction getLocaleLanguage () {\r\n  let localeLanguage = '';\r\n  {\r\n    const appBaseInfo = typeof wx.getAppBaseInfo === 'function' && wx.getAppBaseInfo() ? wx.getAppBaseInfo() : wx.getSystemInfoSync();\r\n    const language =\r\n      appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\r\n    localeLanguage = normalizeLocale(language) || LOCALE_EN;\r\n  }\r\n  return localeLanguage\r\n}\r\n\r\nlet locale;\r\n\r\n{\r\n  locale = getLocaleLanguage();\r\n}\r\n\r\nfunction initI18nMessages () {\r\n  if (!isEnableLocale()) {\r\n    return\r\n  }\r\n  const localeKeys = Object.keys(__uniConfig.locales);\r\n  if (localeKeys.length) {\r\n    localeKeys.forEach((locale) => {\r\n      const curMessages = messages[locale];\r\n      const userMessages = __uniConfig.locales[locale];\r\n      if (curMessages) {\r\n        Object.assign(curMessages, userMessages);\r\n      } else {\r\n        messages[locale] = userMessages;\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\ninitI18nMessages();\r\n\r\nconst i18n = initVueI18n(\r\n  locale,\r\n   {}\r\n);\r\nconst t = i18n.t;\r\nconst i18nMixin = (i18n.mixin = {\r\n  beforeCreate () {\r\n    const unwatch = i18n.i18n.watchLocale(() => {\r\n      this.$forceUpdate();\r\n    });\r\n    this.$once('hook:beforeDestroy', function () {\r\n      unwatch();\r\n    });\r\n  },\r\n  methods: {\r\n    $$t (key, values) {\r\n      return t(key, values)\r\n    }\r\n  }\r\n});\r\nconst setLocale = i18n.setLocale;\r\nconst getLocale = i18n.getLocale;\r\n\r\nfunction initAppLocale (Vue, appVm, locale) {\r\n  const state = Vue.observable({\r\n    locale: locale || i18n.getLocale()\r\n  });\r\n  const localeWatchers = [];\r\n  appVm.$watchLocale = fn => {\r\n    localeWatchers.push(fn);\r\n  };\r\n  Object.defineProperty(appVm, '$locale', {\r\n    get () {\r\n      return state.locale\r\n    },\r\n    set (v) {\r\n      state.locale = v;\r\n      localeWatchers.forEach(watch => watch(v));\r\n    }\r\n  });\r\n}\r\n\r\nfunction isEnableLocale () {\r\n  return typeof __uniConfig !== 'undefined' && __uniConfig.locales && !!Object.keys(__uniConfig.locales).length\r\n}\r\n\r\nfunction include (str, parts) {\r\n  return !!parts.find((part) => str.indexOf(part) !== -1)\r\n}\r\n\r\nfunction startsWith (str, parts) {\r\n  return parts.find((part) => str.indexOf(part) === 0)\r\n}\r\n\r\nfunction normalizeLocale (locale, messages) {\r\n  if (!locale) {\r\n    return\r\n  }\r\n  locale = locale.trim().replace(/_/g, '-');\r\n  if (messages && messages[locale]) {\r\n    return locale\r\n  }\r\n  locale = locale.toLowerCase();\r\n  if (locale === 'chinese') {\r\n    // 支付宝\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  if (locale.indexOf('zh') === 0) {\r\n    if (locale.indexOf('-hans') > -1) {\r\n      return LOCALE_ZH_HANS\r\n    }\r\n    if (locale.indexOf('-hant') > -1) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\r\n      return LOCALE_ZH_HANT\r\n    }\r\n    return LOCALE_ZH_HANS\r\n  }\r\n  const lang = startsWith(locale, [LOCALE_EN, LOCALE_FR, LOCALE_ES]);\r\n  if (lang) {\r\n    return lang\r\n  }\r\n}\r\n// export function initI18n() {\r\n//   const localeKeys = Object.keys(__uniConfig.locales || {})\r\n//   if (localeKeys.length) {\r\n//     localeKeys.forEach((locale) =>\r\n//       i18n.add(locale, __uniConfig.locales[locale])\r\n//     )\r\n//   }\r\n// }\r\n\r\nfunction getLocale$1 () {\r\n  // 优先使用 $locale\r\n  if (isFn(getApp)) {\r\n    const app = getApp({\r\n      allowDefault: true\r\n    });\r\n    if (app && app.$vm) {\r\n      return app.$vm.$locale\r\n    }\r\n  }\r\n  return getLocaleLanguage()\r\n}\r\n\r\nfunction setLocale$1 (locale) {\r\n  const app = isFn(getApp) ? getApp() : false;\r\n  if (!app) {\r\n    return false\r\n  }\r\n  const oldLocale = app.$vm.$locale;\r\n  if (oldLocale !== locale) {\r\n    app.$vm.$locale = locale;\r\n    onLocaleChangeCallbacks.forEach((fn) => fn({\r\n      locale\r\n    }));\r\n    return true\r\n  }\r\n  return false\r\n}\r\n\r\nconst onLocaleChangeCallbacks = [];\r\nfunction onLocaleChange (fn) {\r\n  if (onLocaleChangeCallbacks.indexOf(fn) === -1) {\r\n    onLocaleChangeCallbacks.push(fn);\r\n  }\r\n}\r\n\r\nif (typeof global !== 'undefined') {\r\n  global.getLocale = getLocale$1;\r\n}\r\n\r\nconst interceptors = {\r\n  promiseInterceptor\r\n};\r\n\r\nvar baseApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  upx2px: upx2px,\r\n  rpx2px: upx2px,\r\n  getLocale: getLocale$1,\r\n  setLocale: setLocale$1,\r\n  onLocaleChange: onLocaleChange,\r\n  addInterceptor: addInterceptor,\r\n  removeInterceptor: removeInterceptor,\r\n  interceptors: interceptors\r\n});\r\n\r\nfunction findExistsPageIndex (url) {\r\n  const pages = getCurrentPages();\r\n  let len = pages.length;\r\n  while (len--) {\r\n    const page = pages[len];\r\n    if (page.$page && page.$page.fullPath === url) {\r\n      return len\r\n    }\r\n  }\r\n  return -1\r\n}\r\n\r\nvar redirectTo = {\r\n  name (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.delta) {\r\n      return 'navigateBack'\r\n    }\r\n    return 'redirectTo'\r\n  },\r\n  args (fromArgs) {\r\n    if (fromArgs.exists === 'back' && fromArgs.url) {\r\n      const existsPageIndex = findExistsPageIndex(fromArgs.url);\r\n      if (existsPageIndex !== -1) {\r\n        const delta = getCurrentPages().length - 1 - existsPageIndex;\r\n        if (delta > 0) {\r\n          fromArgs.delta = delta;\r\n        }\r\n      }\r\n    }\r\n  }\r\n};\r\n\r\nvar previewImage = {\r\n  args (fromArgs) {\r\n    let currentIndex = parseInt(fromArgs.current);\r\n    if (isNaN(currentIndex)) {\r\n      return\r\n    }\r\n    const urls = fromArgs.urls;\r\n    if (!Array.isArray(urls)) {\r\n      return\r\n    }\r\n    const len = urls.length;\r\n    if (!len) {\r\n      return\r\n    }\r\n    if (currentIndex < 0) {\r\n      currentIndex = 0;\r\n    } else if (currentIndex >= len) {\r\n      currentIndex = len - 1;\r\n    }\r\n    if (currentIndex > 0) {\r\n      fromArgs.current = urls[currentIndex];\r\n      fromArgs.urls = urls.filter(\r\n        (item, index) => index < currentIndex ? item !== urls[currentIndex] : true\r\n      );\r\n    } else {\r\n      fromArgs.current = urls[0];\r\n    }\r\n    return {\r\n      indicator: false,\r\n      loop: false\r\n    }\r\n  }\r\n};\r\n\r\nconst UUID_KEY = '__DC_STAT_UUID';\r\nlet deviceId;\r\nfunction useDeviceId (result) {\r\n  deviceId = deviceId || wx.getStorageSync(UUID_KEY);\r\n  if (!deviceId) {\r\n    deviceId = Date.now() + '' + Math.floor(Math.random() * 1e7);\r\n    wx.setStorage({\r\n      key: UUID_KEY,\r\n      data: deviceId\r\n    });\r\n  }\r\n  result.deviceId = deviceId;\r\n}\r\n\r\nfunction addSafeAreaInsets (result) {\r\n  if (result.safeArea) {\r\n    const safeArea = result.safeArea;\r\n    result.safeAreaInsets = {\r\n      top: safeArea.top,\r\n      left: safeArea.left,\r\n      right: result.windowWidth - safeArea.right,\r\n      bottom: result.screenHeight - safeArea.bottom\r\n    };\r\n  }\r\n}\r\n\r\nfunction getOSInfo (system, platform) {\r\n  let osName = '';\r\n  let osVersion = '';\r\n\r\n  if (\r\n    platform &&\r\n    ( \"mp-weixin\" === 'mp-baidu')\r\n  ) {\r\n    osName = platform;\r\n    osVersion = system;\r\n  } else {\r\n    osName = system.split(' ')[0] || platform;\r\n    osVersion = system.split(' ')[1] || '';\r\n  }\r\n\r\n  osName = osName.toLocaleLowerCase();\r\n  switch (osName) {\r\n    case 'harmony': // alipay\r\n    case 'ohos': // weixin\r\n    case 'openharmony': // feishu\r\n      osName = 'harmonyos';\r\n      break\r\n    case 'iphone os': // alipay\r\n      osName = 'ios';\r\n      break\r\n    case 'mac': // weixin qq\r\n    case 'darwin': // feishu\r\n      osName = 'macos';\r\n      break\r\n    case 'windows_nt': // feishu\r\n      osName = 'windows';\r\n      break\r\n  }\r\n\r\n  return {\r\n    osName,\r\n    osVersion\r\n  }\r\n}\r\n\r\nfunction populateParameters (result) {\r\n  const {\r\n    brand = '', model = '', system = '',\r\n    language = '', theme, version,\r\n    platform, fontSizeSetting,\r\n    SDKVersion, pixelRatio, deviceOrientation\r\n  } = result;\r\n  // const isQuickApp = \"mp-weixin\".indexOf('quickapp-webview') !== -1\r\n\r\n  const extraParam = {};\r\n\r\n  // osName osVersion\r\n  const { osName, osVersion } = getOSInfo(system, platform);\r\n  let hostVersion = version;\r\n\r\n  // deviceType\r\n  const deviceType = getGetDeviceType(result, model);\r\n\r\n  // deviceModel\r\n  const deviceBrand = getDeviceBrand(brand);\r\n\r\n  // hostName\r\n  const _hostName = getHostName(result);\r\n\r\n  // deviceOrientation\r\n  let _deviceOrientation = deviceOrientation; // 仅 微信 百度 支持\r\n\r\n  // devicePixelRatio\r\n  let _devicePixelRatio = pixelRatio;\r\n\r\n  // SDKVersion\r\n  let _SDKVersion = SDKVersion;\r\n\r\n  // hostLanguage\r\n  const hostLanguage = (language || '').replace(/_/g, '-');\r\n\r\n  // wx.getAccountInfoSync\r\n\r\n  const parameters = {\r\n    appId: process.env.UNI_APP_ID,\r\n    appName: process.env.UNI_APP_NAME,\r\n    appVersion: process.env.UNI_APP_VERSION_NAME,\r\n    appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n    appLanguage: getAppLanguage(hostLanguage),\r\n    uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniRuntimeVersion: process.env.UNI_COMPILER_VERSION,\r\n    uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n    deviceBrand,\r\n    deviceModel: model,\r\n    deviceType,\r\n    devicePixelRatio: _devicePixelRatio,\r\n    deviceOrientation: _deviceOrientation,\r\n    osName: osName.toLocaleLowerCase(),\r\n    osVersion,\r\n    hostTheme: theme,\r\n    hostVersion,\r\n    hostLanguage,\r\n    hostName: _hostName,\r\n    hostSDKVersion: _SDKVersion,\r\n    hostFontSizeSetting: fontSizeSetting,\r\n    windowTop: 0,\r\n    windowBottom: 0,\r\n    // TODO\r\n    osLanguage: undefined,\r\n    osTheme: undefined,\r\n    ua: undefined,\r\n    hostPackageName: undefined,\r\n    browserName: undefined,\r\n    browserVersion: undefined,\r\n    isUniAppX: false\r\n  };\r\n\r\n  Object.assign(result, parameters, extraParam);\r\n}\r\n\r\nfunction getGetDeviceType (result, model) {\r\n  let deviceType = result.deviceType || 'phone';\r\n  {\r\n    const deviceTypeMaps = {\r\n      ipad: 'pad',\r\n      windows: 'pc',\r\n      mac: 'pc'\r\n    };\r\n    const deviceTypeMapsKeys = Object.keys(deviceTypeMaps);\r\n    const _model = model.toLocaleLowerCase();\r\n    for (let index = 0; index < deviceTypeMapsKeys.length; index++) {\r\n      const _m = deviceTypeMapsKeys[index];\r\n      if (_model.indexOf(_m) !== -1) {\r\n        deviceType = deviceTypeMaps[_m];\r\n        break\r\n      }\r\n    }\r\n  }\r\n  return deviceType\r\n}\r\n\r\nfunction getDeviceBrand (brand) {\r\n  let deviceBrand = brand;\r\n  if (deviceBrand) {\r\n    deviceBrand = brand.toLocaleLowerCase();\r\n  }\r\n  return deviceBrand\r\n}\r\n\r\nfunction getAppLanguage (defaultLanguage) {\r\n  return getLocale$1\r\n    ? getLocale$1()\r\n    : defaultLanguage\r\n}\r\n\r\nfunction getHostName (result) {\r\n  const _platform =\r\n     'WeChat'\r\n      ;\r\n  let _hostName = result.hostName || _platform; // mp-jd\r\n  {\r\n    if (result.environment) {\r\n      _hostName = result.environment;\r\n    } else if (result.host && result.host.env) {\r\n      _hostName = result.host.env;\r\n    }\r\n  }\r\n\r\n  return _hostName\r\n}\r\n\r\nvar getSystemInfo = {\r\n  returnValue: function (result) {\r\n    useDeviceId(result);\r\n    addSafeAreaInsets(result);\r\n    populateParameters(result);\r\n  }\r\n};\r\n\r\nvar showActionSheet = {\r\n  args (fromArgs) {\r\n    if (typeof fromArgs === 'object') {\r\n      fromArgs.alertText = fromArgs.title;\r\n    }\r\n  }\r\n};\r\n\r\nvar getAppBaseInfo = {\r\n  returnValue: function (result) {\r\n    const { version, language, SDKVersion, theme } = result;\r\n\r\n    const _hostName = getHostName(result);\r\n\r\n    const hostLanguage = (language || '').replace('_', '-');\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      appId: process.env.UNI_APP_ID,\r\n      appName: process.env.UNI_APP_NAME,\r\n      appVersion: process.env.UNI_APP_VERSION_NAME,\r\n      appVersionCode: process.env.UNI_APP_VERSION_CODE,\r\n      appLanguage: getAppLanguage(hostLanguage),\r\n      hostVersion: version,\r\n      hostLanguage,\r\n      hostName: _hostName,\r\n      hostSDKVersion: SDKVersion,\r\n      hostTheme: theme,\r\n      isUniAppX: false,\r\n      uniPlatform: process.env.UNI_SUB_PLATFORM || process.env.UNI_PLATFORM,\r\n      uniCompileVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniCompilerVersion: process.env.UNI_COMPILER_VERSION,\r\n      uniRuntimeVersion: process.env.UNI_COMPILER_VERSION\r\n    }));\r\n  }\r\n};\r\n\r\nvar getDeviceInfo = {\r\n  returnValue: function (result) {\r\n    const { brand, model, system = '', platform = '' } = result;\r\n    const deviceType = getGetDeviceType(result, model);\r\n    const deviceBrand = getDeviceBrand(brand);\r\n    useDeviceId(result);\r\n\r\n    const { osName, osVersion } = getOSInfo(system, platform);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      deviceType,\r\n      deviceBrand,\r\n      deviceModel: model,\r\n      osName,\r\n      osVersion\r\n    }));\r\n  }\r\n};\r\n\r\nvar getWindowInfo = {\r\n  returnValue: function (result) {\r\n    addSafeAreaInsets(result);\r\n\r\n    result = sortObject(Object.assign(result, {\r\n      windowTop: 0,\r\n      windowBottom: 0\r\n    }));\r\n  }\r\n};\r\n\r\nvar getAppAuthorizeSetting = {\r\n  returnValue: function (result) {\r\n    const { locationReducedAccuracy } = result;\r\n\r\n    result.locationAccuracy = 'unsupported';\r\n    if (locationReducedAccuracy === true) {\r\n      result.locationAccuracy = 'reduced';\r\n    } else if (locationReducedAccuracy === false) {\r\n      result.locationAccuracy = 'full';\r\n    }\r\n  }\r\n};\r\n\r\n// import navigateTo from 'uni-helpers/navigate-to'\r\n\r\nconst compressImage = {\r\n  args (fromArgs) {\r\n    // https://developers.weixin.qq.com/community/develop/doc/000c08940c865011298e0a43256800?highLine=compressHeight\r\n    if (fromArgs.compressedHeight && !fromArgs.compressHeight) {\r\n      fromArgs.compressHeight = fromArgs.compressedHeight;\r\n    }\r\n    if (fromArgs.compressedWidth && !fromArgs.compressWidth) {\r\n      fromArgs.compressWidth = fromArgs.compressedWidth;\r\n    }\r\n  }\r\n};\r\n\r\nconst protocols = {\r\n  redirectTo,\r\n  // navigateTo,  // 由于在微信开发者工具的页面参数，会显示__id__参数，因此暂时关闭mp-weixin对于navigateTo的AOP\r\n  previewImage,\r\n  getSystemInfo,\r\n  getSystemInfoSync: getSystemInfo,\r\n  showActionSheet,\r\n  getAppBaseInfo,\r\n  getDeviceInfo,\r\n  getWindowInfo,\r\n  getAppAuthorizeSetting,\r\n  compressImage\r\n};\r\nconst todos = [\r\n  'vibrate',\r\n  'preloadPage',\r\n  'unPreloadPage',\r\n  'loadSubPackage'\r\n];\r\nconst canIUses = [];\r\n\r\nconst CALLBACKS = ['success', 'fail', 'cancel', 'complete'];\r\n\r\nfunction processCallback (methodName, method, returnValue) {\r\n  return function (res) {\r\n    return method(processReturnValue(methodName, res, returnValue))\r\n  }\r\n}\r\n\r\nfunction processArgs (methodName, fromArgs, argsOption = {}, returnValue = {}, keepFromArgs = false) {\r\n  if (isPlainObject(fromArgs)) { // 一般 api 的参数解析\r\n    const toArgs = keepFromArgs === true ? fromArgs : {}; // returnValue 为 false 时，说明是格式化返回值，直接在返回值对象上修改赋值\r\n    if (isFn(argsOption)) {\r\n      argsOption = argsOption(fromArgs, toArgs) || {};\r\n    }\r\n    for (const key in fromArgs) {\r\n      if (hasOwn(argsOption, key)) {\r\n        let keyOption = argsOption[key];\r\n        if (isFn(keyOption)) {\r\n          keyOption = keyOption(fromArgs[key], fromArgs, toArgs);\r\n        }\r\n        if (!keyOption) { // 不支持的参数\r\n          console.warn(`The '${methodName}' method of platform '微信小程序' does not support option '${key}'`);\r\n        } else if (isStr(keyOption)) { // 重写参数 key\r\n          toArgs[keyOption] = fromArgs[key];\r\n        } else if (isPlainObject(keyOption)) { // {name:newName,value:value}可重新指定参数 key:value\r\n          toArgs[keyOption.name ? keyOption.name : key] = keyOption.value;\r\n        }\r\n      } else if (CALLBACKS.indexOf(key) !== -1) {\r\n        if (isFn(fromArgs[key])) {\r\n          toArgs[key] = processCallback(methodName, fromArgs[key], returnValue);\r\n        }\r\n      } else {\r\n        if (!keepFromArgs) {\r\n          toArgs[key] = fromArgs[key];\r\n        }\r\n      }\r\n    }\r\n    return toArgs\r\n  } else if (isFn(fromArgs)) {\r\n    fromArgs = processCallback(methodName, fromArgs, returnValue);\r\n  }\r\n  return fromArgs\r\n}\r\n\r\nfunction processReturnValue (methodName, res, returnValue, keepReturnValue = false) {\r\n  if (isFn(protocols.returnValue)) { // 处理通用 returnValue\r\n    res = protocols.returnValue(methodName, res);\r\n  }\r\n  return processArgs(methodName, res, returnValue, {}, keepReturnValue)\r\n}\r\n\r\nfunction wrapper (methodName, method) {\r\n  if (hasOwn(protocols, methodName)) {\r\n    const protocol = protocols[methodName];\r\n    if (!protocol) { // 暂不支持的 api\r\n      return function () {\r\n        console.error(`Platform '微信小程序' does not support '${methodName}'.`);\r\n      }\r\n    }\r\n    return function (arg1, arg2) { // 目前 api 最多两个参数\r\n      let options = protocol;\r\n      if (isFn(protocol)) {\r\n        options = protocol(arg1);\r\n      }\r\n\r\n      arg1 = processArgs(methodName, arg1, options.args, options.returnValue);\r\n\r\n      const args = [arg1];\r\n      if (typeof arg2 !== 'undefined') {\r\n        args.push(arg2);\r\n      }\r\n      if (isFn(options.name)) {\r\n        methodName = options.name(arg1);\r\n      } else if (isStr(options.name)) {\r\n        methodName = options.name;\r\n      }\r\n      const returnValue = wx[methodName].apply(wx, args);\r\n      if (isSyncApi(methodName)) { // 同步 api\r\n        return processReturnValue(methodName, returnValue, options.returnValue, isContextApi(methodName))\r\n      }\r\n      return returnValue\r\n    }\r\n  }\r\n  return method\r\n}\r\n\r\nconst todoApis = Object.create(null);\r\n\r\nconst TODOS = [\r\n  'onTabBarMidButtonTap',\r\n  'subscribePush',\r\n  'unsubscribePush',\r\n  'onPush',\r\n  'offPush',\r\n  'share'\r\n];\r\n\r\nfunction createTodoApi (name) {\r\n  return function todoApi ({\r\n    fail,\r\n    complete\r\n  }) {\r\n    const res = {\r\n      errMsg: `${name}:fail method '${name}' not supported`\r\n    };\r\n    isFn(fail) && fail(res);\r\n    isFn(complete) && complete(res);\r\n  }\r\n}\r\n\r\nTODOS.forEach(function (name) {\r\n  todoApis[name] = createTodoApi(name);\r\n});\r\n\r\nvar providers = {\r\n  oauth: ['weixin'],\r\n  share: ['weixin'],\r\n  payment: ['wxpay'],\r\n  push: ['weixin']\r\n};\r\n\r\nfunction getProvider ({\r\n  service,\r\n  success,\r\n  fail,\r\n  complete\r\n}) {\r\n  let res = false;\r\n  if (providers[service]) {\r\n    res = {\r\n      errMsg: 'getProvider:ok',\r\n      service,\r\n      provider: providers[service]\r\n    };\r\n    isFn(success) && success(res);\r\n  } else {\r\n    res = {\r\n      errMsg: 'getProvider:fail service not found'\r\n    };\r\n    isFn(fail) && fail(res);\r\n  }\r\n  isFn(complete) && complete(res);\r\n}\r\n\r\nvar extraApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  getProvider: getProvider\r\n});\r\n\r\nconst getEmitter = (function () {\r\n  let Emitter;\r\n  return function getUniEmitter () {\r\n    if (!Emitter) {\r\n      Emitter = new Vue();\r\n    }\r\n    return Emitter\r\n  }\r\n})();\r\n\r\nfunction apply (ctx, method, args) {\r\n  return ctx[method].apply(ctx, args)\r\n}\r\n\r\nfunction $on () {\r\n  return apply(getEmitter(), '$on', [...arguments])\r\n}\r\nfunction $off () {\r\n  return apply(getEmitter(), '$off', [...arguments])\r\n}\r\nfunction $once () {\r\n  return apply(getEmitter(), '$once', [...arguments])\r\n}\r\nfunction $emit () {\r\n  return apply(getEmitter(), '$emit', [...arguments])\r\n}\r\n\r\nvar eventApi = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  $on: $on,\r\n  $off: $off,\r\n  $once: $once,\r\n  $emit: $emit\r\n});\r\n\r\n/**\r\n * 框架内 try-catch\r\n */\r\n/**\r\n * 开发者 try-catch\r\n */\r\nfunction tryCatch (fn) {\r\n  return function () {\r\n    try {\r\n      return fn.apply(fn, arguments)\r\n    } catch (e) {\r\n      // TODO\r\n      console.error(e);\r\n    }\r\n  }\r\n}\r\n\r\nfunction getApiCallbacks (params) {\r\n  const apiCallbacks = {};\r\n  for (const name in params) {\r\n    const param = params[name];\r\n    if (isFn(param)) {\r\n      apiCallbacks[name] = tryCatch(param);\r\n      delete params[name];\r\n    }\r\n  }\r\n  return apiCallbacks\r\n}\r\n\r\nlet cid;\r\nlet cidErrMsg;\r\nlet enabled;\r\n\r\nfunction normalizePushMessage (message) {\r\n  try {\r\n    return JSON.parse(message)\r\n  } catch (e) {}\r\n  return message\r\n}\r\n\r\nfunction invokePushCallback (\r\n  args\r\n) {\r\n  if (args.type === 'enabled') {\r\n    enabled = true;\r\n  } else if (args.type === 'clientId') {\r\n    cid = args.cid;\r\n    cidErrMsg = args.errMsg;\r\n    invokeGetPushCidCallbacks(cid, args.errMsg);\r\n  } else if (args.type === 'pushMsg') {\r\n    const message = {\r\n      type: 'receive',\r\n      data: normalizePushMessage(args.message)\r\n    };\r\n    for (let i = 0; i < onPushMessageCallbacks.length; i++) {\r\n      const callback = onPushMessageCallbacks[i];\r\n      callback(message);\r\n      // 该消息已被阻止\r\n      if (message.stopped) {\r\n        break\r\n      }\r\n    }\r\n  } else if (args.type === 'click') {\r\n    onPushMessageCallbacks.forEach((callback) => {\r\n      callback({\r\n        type: 'click',\r\n        data: normalizePushMessage(args.message)\r\n      });\r\n    });\r\n  }\r\n}\r\n\r\nconst getPushCidCallbacks = [];\r\n\r\nfunction invokeGetPushCidCallbacks (cid, errMsg) {\r\n  getPushCidCallbacks.forEach((callback) => {\r\n    callback(cid, errMsg);\r\n  });\r\n  getPushCidCallbacks.length = 0;\r\n}\r\n\r\nfunction getPushClientId (args) {\r\n  if (!isPlainObject(args)) {\r\n    args = {};\r\n  }\r\n  const {\r\n    success,\r\n    fail,\r\n    complete\r\n  } = getApiCallbacks(args);\r\n  const hasSuccess = isFn(success);\r\n  const hasFail = isFn(fail);\r\n  const hasComplete = isFn(complete);\r\n\r\n  Promise.resolve().then(() => {\r\n    if (typeof enabled === 'undefined') {\r\n      enabled = false;\r\n      cid = '';\r\n      cidErrMsg = 'uniPush is not enabled';\r\n    }\r\n    getPushCidCallbacks.push((cid, errMsg) => {\r\n      let res;\r\n      if (cid) {\r\n        res = {\r\n          errMsg: 'getPushClientId:ok',\r\n          cid\r\n        };\r\n        hasSuccess && success(res);\r\n      } else {\r\n        res = {\r\n          errMsg: 'getPushClientId:fail' + (errMsg ? ' ' + errMsg : '')\r\n        };\r\n        hasFail && fail(res);\r\n      }\r\n      hasComplete && complete(res);\r\n    });\r\n    if (typeof cid !== 'undefined') {\r\n      invokeGetPushCidCallbacks(cid, cidErrMsg);\r\n    }\r\n  });\r\n}\r\n\r\nconst onPushMessageCallbacks = [];\r\n// 不使用 defineOnApi 实现，是因为 defineOnApi 依赖 UniServiceJSBridge ，该对象目前在小程序上未提供，故简单实现\r\nconst onPushMessage = (fn) => {\r\n  if (onPushMessageCallbacks.indexOf(fn) === -1) {\r\n    onPushMessageCallbacks.push(fn);\r\n  }\r\n};\r\n\r\nconst offPushMessage = (fn) => {\r\n  if (!fn) {\r\n    onPushMessageCallbacks.length = 0;\r\n  } else {\r\n    const index = onPushMessageCallbacks.indexOf(fn);\r\n    if (index > -1) {\r\n      onPushMessageCallbacks.splice(index, 1);\r\n    }\r\n  }\r\n};\r\n\r\nfunction __f__ (\r\n  type,\r\n  ...args\r\n) {\r\n  console[type].apply(console, args);\r\n}\r\n\r\nlet baseInfo = wx.getAppBaseInfo && wx.getAppBaseInfo();\r\nif (!baseInfo) {\r\n  baseInfo = wx.getSystemInfoSync();\r\n}\r\nconst host = baseInfo ? baseInfo.host : null;\r\nconst shareVideoMessage =\r\n  host && host.env === 'SAAASDK' ? wx.miniapp.shareVideoMessage : wx.shareVideoMessage;\r\n\r\nvar api = /*#__PURE__*/Object.freeze({\r\n  __proto__: null,\r\n  shareVideoMessage: shareVideoMessage,\r\n  getPushClientId: getPushClientId,\r\n  onPushMessage: onPushMessage,\r\n  offPushMessage: offPushMessage,\r\n  invokePushCallback: invokePushCallback,\r\n  __f__: __f__\r\n});\r\n\r\nconst mocks = ['__route__', '__wxExparserNodeId__', '__wxWebviewId__'];\r\n\r\nfunction findVmByVueId (vm, vuePid) {\r\n  const $children = vm.$children;\r\n  // 优先查找直属(反向查找:https://github.com/dcloudio/uni-app/issues/1200)\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    const childVm = $children[i];\r\n    if (childVm.$scope._$vueId === vuePid) {\r\n      return childVm\r\n    }\r\n  }\r\n  // 反向递归查找\r\n  let parentVm;\r\n  for (let i = $children.length - 1; i >= 0; i--) {\r\n    parentVm = findVmByVueId($children[i], vuePid);\r\n    if (parentVm) {\r\n      return parentVm\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehavior (options) {\r\n  return Behavior(options)\r\n}\r\n\r\nfunction isPage () {\r\n  return !!this.route\r\n}\r\n\r\nfunction initRelation (detail) {\r\n  this.triggerEvent('__l', detail);\r\n}\r\n\r\nfunction selectAllComponents (mpInstance, selector, $refs) {\r\n  const components = mpInstance.selectAllComponents(selector) || [];\r\n  components.forEach(component => {\r\n    const ref = component.dataset.ref;\r\n    $refs[ref] = component.$vm || toSkip(component);\r\n    {\r\n      if (component.dataset.vueGeneric === 'scoped') {\r\n        component.selectAllComponents('.scoped-ref').forEach(scopedComponent => {\r\n          selectAllComponents(scopedComponent, selector, $refs);\r\n        });\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction syncRefs (refs, newRefs) {\r\n  const oldKeys = new Set(...Object.keys(refs));\r\n  const newKeys = Object.keys(newRefs);\r\n  newKeys.forEach(key => {\r\n    const oldValue = refs[key];\r\n    const newValue = newRefs[key];\r\n    if (Array.isArray(oldValue) && Array.isArray(newValue) && oldValue.length === newValue.length && newValue.every(value => oldValue.includes(value))) {\r\n      return\r\n    }\r\n    refs[key] = newValue;\r\n    oldKeys.delete(key);\r\n  });\r\n  oldKeys.forEach(key => {\r\n    delete refs[key];\r\n  });\r\n  return refs\r\n}\r\n\r\nfunction initRefs (vm) {\r\n  const mpInstance = vm.$scope;\r\n  const refs = {};\r\n  Object.defineProperty(vm, '$refs', {\r\n    get () {\r\n      const $refs = {};\r\n      selectAllComponents(mpInstance, '.vue-ref', $refs);\r\n      // TODO 暂不考虑 for 中的 scoped\r\n      const forComponents = mpInstance.selectAllComponents('.vue-ref-in-for') || [];\r\n      forComponents.forEach(component => {\r\n        const ref = component.dataset.ref;\r\n        if (!$refs[ref]) {\r\n          $refs[ref] = [];\r\n        }\r\n        $refs[ref].push(component.$vm || toSkip(component));\r\n      });\r\n      return syncRefs(refs, $refs)\r\n    }\r\n  });\r\n}\r\n\r\nfunction handleLink (event) {\r\n  const {\r\n    vuePid,\r\n    vueOptions\r\n  } = event.detail || event.value; // detail 是微信,value 是百度(dipatch)\r\n\r\n  let parentVm;\r\n\r\n  if (vuePid) {\r\n    parentVm = findVmByVueId(this.$vm, vuePid);\r\n  }\r\n\r\n  if (!parentVm) {\r\n    parentVm = this.$vm;\r\n  }\r\n\r\n  vueOptions.parent = parentVm;\r\n}\r\n\r\nfunction markMPComponent (component) {\r\n  // 在 Vue 中标记为小程序组件\r\n  const IS_MP = '__v_isMPComponent';\r\n  Object.defineProperty(component, IS_MP, {\r\n    configurable: true,\r\n    enumerable: false,\r\n    value: true\r\n  });\r\n  return component\r\n}\r\n\r\nfunction toSkip (obj) {\r\n  const OB = '__ob__';\r\n  const SKIP = '__v_skip';\r\n  if (isObject(obj) && Object.isExtensible(obj)) {\r\n    // 避免被 @vue/composition-api 观测\r\n    Object.defineProperty(obj, OB, {\r\n      configurable: true,\r\n      enumerable: false,\r\n      value: {\r\n        [SKIP]: true\r\n      }\r\n    });\r\n  }\r\n  return obj\r\n}\r\n\r\nconst WORKLET_RE = /_(.*)_worklet_factory_/;\r\nfunction initWorkletMethods (mpMethods, vueMethods) {\r\n  if (vueMethods) {\r\n    Object.keys(vueMethods).forEach((name) => {\r\n      const matches = name.match(WORKLET_RE);\r\n      if (matches) {\r\n        const workletName = matches[1];\r\n        mpMethods[name] = vueMethods[name];\r\n        mpMethods[workletName] = vueMethods[workletName];\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nconst MPPage = Page;\r\nconst MPComponent = Component;\r\n\r\nconst customizeRE = /:/g;\r\n\r\nconst customize = cached((str) => {\r\n  return camelize(str.replace(customizeRE, '-'))\r\n});\r\n\r\nfunction initTriggerEvent (mpInstance) {\r\n  const oldTriggerEvent = mpInstance.triggerEvent;\r\n  const newTriggerEvent = function (event, ...args) {\r\n    // 事件名统一转驼峰格式，仅处理：当前组件为 vue 组件、当前组件为 vue 组件子组件\r\n    if (this.$vm || (this.dataset && this.dataset.comType)) {\r\n      event = customize(event);\r\n    } else {\r\n      // 针对微信/QQ小程序单独补充驼峰格式事件，以兼容历史项目\r\n      const newEvent = customize(event);\r\n      if (newEvent !== event) {\r\n        oldTriggerEvent.apply(this, [newEvent, ...args]);\r\n      }\r\n    }\r\n    return oldTriggerEvent.apply(this, [event, ...args])\r\n  };\r\n  try {\r\n    // 京东小程序 triggerEvent 为只读\r\n    mpInstance.triggerEvent = newTriggerEvent;\r\n  } catch (error) {\r\n    mpInstance._triggerEvent = newTriggerEvent;\r\n  }\r\n}\r\n\r\nfunction initHook (name, options, isComponent) {\r\n  const oldHook = options[name];\r\n  options[name] = function (...args) {\r\n    markMPComponent(this);\r\n    initTriggerEvent(this);\r\n    if (oldHook) {\r\n      return oldHook.apply(this, args)\r\n    }\r\n  };\r\n}\r\nif (!MPPage.__$wrappered) {\r\n  MPPage.__$wrappered = true;\r\n  Page = function (options = {}) {\r\n    initHook('onLoad', options);\r\n    return MPPage(options)\r\n  };\r\n  Page.after = MPPage.after;\r\n\r\n  Component = function (options = {}) {\r\n    initHook('created', options);\r\n    return MPComponent(options)\r\n  };\r\n}\r\n\r\nconst PAGE_EVENT_HOOKS = [\r\n  'onPullDownRefresh',\r\n  'onReachBottom',\r\n  'onAddToFavorites',\r\n  'onShareTimeline',\r\n  'onShareAppMessage',\r\n  'onPageScroll',\r\n  'onResize',\r\n  'onTabItemTap'\r\n];\r\n\r\nfunction initMocks (vm, mocks) {\r\n  const mpInstance = vm.$mp[vm.mpType];\r\n  mocks.forEach(mock => {\r\n    if (hasOwn(mpInstance, mock)) {\r\n      vm[mock] = mpInstance[mock];\r\n    }\r\n  });\r\n}\r\n\r\nfunction hasHook (hook, vueOptions) {\r\n  if (!vueOptions) {\r\n    return true\r\n  }\r\n\r\n  if (Vue.options && Array.isArray(Vue.options[hook])) {\r\n    return true\r\n  }\r\n\r\n  vueOptions = vueOptions.default || vueOptions;\r\n\r\n  if (isFn(vueOptions)) {\r\n    if (isFn(vueOptions.extendOptions[hook])) {\r\n      return true\r\n    }\r\n    if (vueOptions.super &&\r\n      vueOptions.super.options &&\r\n      Array.isArray(vueOptions.super.options[hook])) {\r\n      return true\r\n    }\r\n    return false\r\n  }\r\n\r\n  if (isFn(vueOptions[hook]) || Array.isArray(vueOptions[hook])) {\r\n    return true\r\n  }\r\n  const mixins = vueOptions.mixins;\r\n  if (Array.isArray(mixins)) {\r\n    return !!mixins.find(mixin => hasHook(hook, mixin))\r\n  }\r\n}\r\n\r\nfunction initHooks (mpOptions, hooks, vueOptions) {\r\n  hooks.forEach(hook => {\r\n    if (hasHook(hook, vueOptions)) {\r\n      mpOptions[hook] = function (args) {\r\n        return this.$vm && this.$vm.__call_hook(hook, args)\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\nfunction initUnknownHooks (mpOptions, vueOptions, excludes = []) {\r\n  findHooks(vueOptions).forEach((hook) => initHook$1(mpOptions, hook, excludes));\r\n}\r\n\r\nfunction findHooks (vueOptions, hooks = []) {\r\n  if (vueOptions) {\r\n    Object.keys(vueOptions).forEach((name) => {\r\n      if (name.indexOf('on') === 0 && isFn(vueOptions[name])) {\r\n        hooks.push(name);\r\n      }\r\n    });\r\n  }\r\n  return hooks\r\n}\r\n\r\nfunction initHook$1 (mpOptions, hook, excludes) {\r\n  if (excludes.indexOf(hook) === -1 && !hasOwn(mpOptions, hook)) {\r\n    mpOptions[hook] = function (args) {\r\n      return this.$vm && this.$vm.__call_hook(hook, args)\r\n    };\r\n  }\r\n}\r\n\r\nfunction initVueComponent (Vue, vueOptions) {\r\n  vueOptions = vueOptions.default || vueOptions;\r\n  let VueComponent;\r\n  if (isFn(vueOptions)) {\r\n    VueComponent = vueOptions;\r\n  } else {\r\n    VueComponent = Vue.extend(vueOptions);\r\n  }\r\n  vueOptions = VueComponent.options;\r\n  return [VueComponent, vueOptions]\r\n}\r\n\r\nfunction initSlots (vm, vueSlots) {\r\n  if (Array.isArray(vueSlots) && vueSlots.length) {\r\n    const $slots = Object.create(null);\r\n    vueSlots.forEach(slotName => {\r\n      $slots[slotName] = true;\r\n    });\r\n    vm.$scopedSlots = vm.$slots = $slots;\r\n  }\r\n}\r\n\r\nfunction initVueIds (vueIds, mpInstance) {\r\n  vueIds = (vueIds || '').split(',');\r\n  const len = vueIds.length;\r\n\r\n  if (len === 1) {\r\n    mpInstance._$vueId = vueIds[0];\r\n  } else if (len === 2) {\r\n    mpInstance._$vueId = vueIds[0];\r\n    mpInstance._$vuePid = vueIds[1];\r\n  }\r\n}\r\n\r\nfunction initData (vueOptions, context) {\r\n  let data = vueOptions.data || {};\r\n  const methods = vueOptions.methods || {};\r\n\r\n  if (typeof data === 'function') {\r\n    try {\r\n      data = data.call(context); // 支持 Vue.prototype 上挂的数据\r\n    } catch (e) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.warn('根据 Vue 的 data 函数初始化小程序 data 失败，请尽量确保 data 函数中不访问 vm 对象，否则可能影响首次数据渲染速度。', data);\r\n      }\r\n    }\r\n  } else {\r\n    try {\r\n      // 对 data 格式化\r\n      data = JSON.parse(JSON.stringify(data));\r\n    } catch (e) { }\r\n  }\r\n\r\n  if (!isPlainObject(data)) {\r\n    data = {};\r\n  }\r\n\r\n  Object.keys(methods).forEach(methodName => {\r\n    if (context.__lifecycle_hooks__.indexOf(methodName) === -1 && !hasOwn(data, methodName)) {\r\n      data[methodName] = methods[methodName];\r\n    }\r\n  });\r\n\r\n  return data\r\n}\r\n\r\nconst PROP_TYPES = [String, Number, Boolean, Object, Array, null];\r\n\r\nfunction createObserver (name) {\r\n  return function observer (newVal, oldVal) {\r\n    if (this.$vm) {\r\n      this.$vm[name] = newVal; // 为了触发其他非 render watcher\r\n    }\r\n  }\r\n}\r\n\r\nfunction initBehaviors (vueOptions, initBehavior) {\r\n  const vueBehaviors = vueOptions.behaviors;\r\n  const vueExtends = vueOptions.extends;\r\n  const vueMixins = vueOptions.mixins;\r\n\r\n  let vueProps = vueOptions.props;\r\n\r\n  if (!vueProps) {\r\n    vueOptions.props = vueProps = [];\r\n  }\r\n\r\n  const behaviors = [];\r\n  if (Array.isArray(vueBehaviors)) {\r\n    vueBehaviors.forEach(behavior => {\r\n      behaviors.push(behavior.replace('uni://', `${\"wx\"}://`));\r\n      if (behavior === 'uni://form-field') {\r\n        if (Array.isArray(vueProps)) {\r\n          vueProps.push('name');\r\n          vueProps.push('value');\r\n        } else {\r\n          vueProps.name = {\r\n            type: String,\r\n            default: ''\r\n          };\r\n          vueProps.value = {\r\n            type: [String, Number, Boolean, Array, Object, Date],\r\n            default: ''\r\n          };\r\n        }\r\n      }\r\n    });\r\n  }\r\n  if (isPlainObject(vueExtends) && vueExtends.props) {\r\n    behaviors.push(\r\n      initBehavior({\r\n        properties: initProperties(vueExtends.props, true)\r\n      })\r\n    );\r\n  }\r\n  if (Array.isArray(vueMixins)) {\r\n    vueMixins.forEach(vueMixin => {\r\n      if (isPlainObject(vueMixin) && vueMixin.props) {\r\n        behaviors.push(\r\n          initBehavior({\r\n            properties: initProperties(vueMixin.props, true)\r\n          })\r\n        );\r\n      }\r\n    });\r\n  }\r\n  return behaviors\r\n}\r\n\r\nfunction parsePropType (key, type, defaultValue, file) {\r\n  // [String]=>String\r\n  if (Array.isArray(type) && type.length === 1) {\r\n    return type[0]\r\n  }\r\n  return type\r\n}\r\n\r\nfunction initProperties (props, isBehavior = false, file = '', options) {\r\n  const properties = {};\r\n  if (!isBehavior) {\r\n    properties.vueId = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    {\r\n      if ( options.virtualHost) {\r\n        properties.virtualHostStyle = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n        properties.virtualHostClass = {\r\n          type: null,\r\n          value: ''\r\n        };\r\n      }\r\n    }\r\n    // scopedSlotsCompiler auto\r\n    properties.scopedSlotsCompiler = {\r\n      type: String,\r\n      value: ''\r\n    };\r\n    properties.vueSlots = { // 小程序不能直接定义 $slots 的 props，所以通过 vueSlots 转换到 $slots\r\n      type: null,\r\n      value: [],\r\n      observer: function (newVal, oldVal) {\r\n        const $slots = Object.create(null);\r\n        newVal.forEach(slotName => {\r\n          $slots[slotName] = true;\r\n        });\r\n        this.setData({\r\n          $slots\r\n        });\r\n      }\r\n    };\r\n  }\r\n  if (Array.isArray(props)) { // ['title']\r\n    props.forEach(key => {\r\n      properties[key] = {\r\n        type: null,\r\n        observer: createObserver(key)\r\n      };\r\n    });\r\n  } else if (isPlainObject(props)) { // {title:{type:String,default:''},content:String}\r\n    Object.keys(props).forEach(key => {\r\n      const opts = props[key];\r\n      if (isPlainObject(opts)) { // title:{type:String,default:''}\r\n        let value = opts.default;\r\n        if (isFn(value)) {\r\n          value = value();\r\n        }\r\n\r\n        opts.type = parsePropType(key, opts.type);\r\n\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(opts.type) !== -1 ? opts.type : null,\r\n          value,\r\n          observer: createObserver(key)\r\n        };\r\n      } else { // content:String\r\n        const type = parsePropType(key, opts);\r\n        properties[key] = {\r\n          type: PROP_TYPES.indexOf(type) !== -1 ? type : null,\r\n          observer: createObserver(key)\r\n        };\r\n      }\r\n    });\r\n  }\r\n  return properties\r\n}\r\n\r\nfunction wrapper$1 (event) {\r\n  // TODO 又得兼容 mpvue 的 mp 对象\r\n  try {\r\n    event.mp = JSON.parse(JSON.stringify(event));\r\n  } catch (e) { }\r\n\r\n  event.stopPropagation = noop;\r\n  event.preventDefault = noop;\r\n\r\n  event.target = event.target || {};\r\n\r\n  if (!hasOwn(event, 'detail')) {\r\n    event.detail = {};\r\n  }\r\n\r\n  if (hasOwn(event, 'markerId')) {\r\n    event.detail = typeof event.detail === 'object' ? event.detail : {};\r\n    event.detail.markerId = event.markerId;\r\n  }\r\n\r\n  if (isPlainObject(event.detail)) {\r\n    event.target = Object.assign({}, event.target, event.detail);\r\n  }\r\n\r\n  return event\r\n}\r\n\r\nfunction getExtraValue (vm, dataPathsArray) {\r\n  let context = vm;\r\n  dataPathsArray.forEach(dataPathArray => {\r\n    const dataPath = dataPathArray[0];\r\n    const value = dataPathArray[2];\r\n    if (dataPath || typeof value !== 'undefined') { // ['','',index,'disable']\r\n      const propPath = dataPathArray[1];\r\n      const valuePath = dataPathArray[3];\r\n\r\n      let vFor;\r\n      if (Number.isInteger(dataPath)) {\r\n        vFor = dataPath;\r\n      } else if (!dataPath) {\r\n        vFor = context;\r\n      } else if (typeof dataPath === 'string' && dataPath) {\r\n        if (dataPath.indexOf('#s#') === 0) {\r\n          vFor = dataPath.substr(3);\r\n        } else {\r\n          vFor = vm.__get_value(dataPath, context);\r\n        }\r\n      }\r\n\r\n      if (Number.isInteger(vFor)) {\r\n        context = value;\r\n      } else if (!propPath) {\r\n        context = vFor[value];\r\n      } else {\r\n        if (Array.isArray(vFor)) {\r\n          context = vFor.find(vForItem => {\r\n            return vm.__get_value(propPath, vForItem) === value\r\n          });\r\n        } else if (isPlainObject(vFor)) {\r\n          context = Object.keys(vFor).find(vForKey => {\r\n            return vm.__get_value(propPath, vFor[vForKey]) === value\r\n          });\r\n        } else {\r\n          console.error('v-for 暂不支持循环数据：', vFor);\r\n        }\r\n      }\r\n\r\n      if (valuePath) {\r\n        context = vm.__get_value(valuePath, context);\r\n      }\r\n    }\r\n  });\r\n  return context\r\n}\r\n\r\nfunction processEventExtra (vm, extra, event, __args__) {\r\n  const extraObj = {};\r\n\r\n  if (Array.isArray(extra) && extra.length) {\r\n    /**\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *[\r\n     *    ['data.items', 'data.id', item.data.id],\r\n     *    ['metas', 'id', meta.id]\r\n     *],\r\n     *'test'\r\n     */\r\n    extra.forEach((dataPath, index) => {\r\n      if (typeof dataPath === 'string') {\r\n        if (!dataPath) { // model,prop.sync\r\n          extraObj['$' + index] = vm;\r\n        } else {\r\n          if (dataPath === '$event') { // $event\r\n            extraObj['$' + index] = event;\r\n          } else if (dataPath === 'arguments') {\r\n            extraObj['$' + index] = event.detail ? event.detail.__args__ || __args__ : __args__;\r\n          } else if (dataPath.indexOf('$event.') === 0) { // $event.target.value\r\n            extraObj['$' + index] = vm.__get_value(dataPath.replace('$event.', ''), event);\r\n          } else {\r\n            extraObj['$' + index] = vm.__get_value(dataPath);\r\n          }\r\n        }\r\n      } else {\r\n        extraObj['$' + index] = getExtraValue(vm, dataPath);\r\n      }\r\n    });\r\n  }\r\n\r\n  return extraObj\r\n}\r\n\r\nfunction getObjByArray (arr) {\r\n  const obj = {};\r\n  for (let i = 1; i < arr.length; i++) {\r\n    const element = arr[i];\r\n    obj[element[0]] = element[1];\r\n  }\r\n  return obj\r\n}\r\n\r\nfunction processEventArgs (vm, event, args = [], extra = [], isCustom, methodName) {\r\n  let isCustomMPEvent = false; // wxcomponent 组件，传递原始 event 对象\r\n\r\n  // fixed 用户直接触发 mpInstance.triggerEvent\r\n  const __args__ = isPlainObject(event.detail)\r\n    ? event.detail.__args__ || [event.detail]\r\n    : [event.detail];\r\n\r\n  if (isCustom) { // 自定义事件\r\n    isCustomMPEvent = event.currentTarget &&\r\n      event.currentTarget.dataset &&\r\n      event.currentTarget.dataset.comType === 'wx';\r\n    if (!args.length) { // 无参数，直接传入 event 或 detail 数组\r\n      if (isCustomMPEvent) {\r\n        return [event]\r\n      }\r\n      return __args__\r\n    }\r\n  }\r\n\r\n  const extraObj = processEventExtra(vm, extra, event, __args__);\r\n\r\n  const ret = [];\r\n  args.forEach(arg => {\r\n    if (arg === '$event') {\r\n      if (methodName === '__set_model' && !isCustom) { // input v-model value\r\n        ret.push(event.target.value);\r\n      } else {\r\n        if (isCustom && !isCustomMPEvent) {\r\n          ret.push(__args__[0]);\r\n        } else { // wxcomponent 组件或内置组件\r\n          ret.push(event);\r\n        }\r\n      }\r\n    } else {\r\n      if (Array.isArray(arg) && arg[0] === 'o') {\r\n        ret.push(getObjByArray(arg));\r\n      } else if (typeof arg === 'string' && hasOwn(extraObj, arg)) {\r\n        ret.push(extraObj[arg]);\r\n      } else {\r\n        ret.push(arg);\r\n      }\r\n    }\r\n  });\r\n\r\n  return ret\r\n}\r\n\r\nconst ONCE = '~';\r\nconst CUSTOM = '^';\r\n\r\nfunction isMatchEventType (eventType, optType) {\r\n  return (eventType === optType) ||\r\n    (\r\n      optType === 'regionchange' &&\r\n      (\r\n        eventType === 'begin' ||\r\n        eventType === 'end'\r\n      )\r\n    )\r\n}\r\n\r\nfunction getContextVm (vm) {\r\n  let $parent = vm.$parent;\r\n  // 父组件是 scoped slots 或者其他自定义组件时继续查找\r\n  while ($parent && $parent.$parent && ($parent.$options.generic || $parent.$parent.$options.generic || $parent.$scope._$vuePid)) {\r\n    $parent = $parent.$parent;\r\n  }\r\n  return $parent && $parent.$parent\r\n}\r\n\r\nfunction handleEvent (event) {\r\n  event = wrapper$1(event);\r\n\r\n  // [['tap',[['handle',[1,2,a]],['handle1',[1,2,a]]]]]\r\n  const dataset = (event.currentTarget || event.target).dataset;\r\n  if (!dataset) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n  const eventOpts = dataset.eventOpts || dataset['event-opts']; // 支付宝 web-view 组件 dataset 非驼峰\r\n  if (!eventOpts) {\r\n    return console.warn('事件信息不存在')\r\n  }\r\n\r\n  // [['handle',[1,2,a]],['handle1',[1,2,a]]]\r\n  const eventType = event.type;\r\n\r\n  const ret = [];\r\n\r\n  eventOpts.forEach(eventOpt => {\r\n    let type = eventOpt[0];\r\n    const eventsArray = eventOpt[1];\r\n\r\n    const isCustom = type.charAt(0) === CUSTOM;\r\n    type = isCustom ? type.slice(1) : type;\r\n    const isOnce = type.charAt(0) === ONCE;\r\n    type = isOnce ? type.slice(1) : type;\r\n\r\n    if (eventsArray && isMatchEventType(eventType, type)) {\r\n      eventsArray.forEach(eventArray => {\r\n        const methodName = eventArray[0];\r\n        if (methodName) {\r\n          let handlerCtx = this.$vm;\r\n          if (handlerCtx.$options.generic) { // mp-weixin,mp-toutiao 抽象节点模拟 scoped slots\r\n            handlerCtx = getContextVm(handlerCtx) || handlerCtx;\r\n          }\r\n          if (methodName === '$emit') {\r\n            handlerCtx.$emit.apply(handlerCtx,\r\n              processEventArgs(\r\n                this.$vm,\r\n                event,\r\n                eventArray[1],\r\n                eventArray[2],\r\n                isCustom,\r\n                methodName\r\n              ));\r\n            return\r\n          }\r\n          const handler = handlerCtx[methodName];\r\n          if (!isFn(handler)) {\r\n            const type = this.$vm.mpType === 'page' ? 'Page' : 'Component';\r\n            const path = this.route || this.is;\r\n            throw new Error(`${type} \"${path}\" does not have a method \"${methodName}\"`)\r\n          }\r\n          if (isOnce) {\r\n            if (handler.once) {\r\n              return\r\n            }\r\n            handler.once = true;\r\n          }\r\n          let params = processEventArgs(\r\n            this.$vm,\r\n            event,\r\n            eventArray[1],\r\n            eventArray[2],\r\n            isCustom,\r\n            methodName\r\n          );\r\n          params = Array.isArray(params) ? params : [];\r\n          // 参数尾部增加原始事件对象用于复杂表达式内获取额外数据\r\n          if (/=\\s*\\S+\\.eventParams\\s*\\|\\|\\s*\\S+\\[['\"]event-params['\"]\\]/.test(handler.toString())) {\r\n            // eslint-disable-next-line no-sparse-arrays\r\n            params = params.concat([, , , , , , , , , , event]);\r\n          }\r\n          ret.push(handler.apply(handlerCtx, params));\r\n        }\r\n      });\r\n    }\r\n  });\r\n\r\n  if (\r\n    eventType === 'input' &&\r\n    ret.length === 1 &&\r\n    typeof ret[0] !== 'undefined'\r\n  ) {\r\n    return ret[0]\r\n  }\r\n}\r\n\r\nconst eventChannels = {};\r\n\r\nfunction getEventChannel (id) {\r\n  const eventChannel = eventChannels[id];\r\n  delete eventChannels[id];\r\n  return eventChannel\r\n}\r\n\r\nconst hooks = [\r\n  'onShow',\r\n  'onHide',\r\n  'onError',\r\n  'onPageNotFound',\r\n  'onThemeChange',\r\n  'onUnhandledRejection'\r\n];\r\n\r\nfunction initEventChannel () {\r\n  Vue.prototype.getOpenerEventChannel = function () {\r\n    // 微信小程序使用自身getOpenerEventChannel\r\n    {\r\n      return this.$scope.getOpenerEventChannel()\r\n    }\r\n  };\r\n  const callHook = Vue.prototype.__call_hook;\r\n  Vue.prototype.__call_hook = function (hook, args) {\r\n    if (hook === 'onLoad' && args && args.__id__) {\r\n      this.__eventChannel__ = getEventChannel(args.__id__);\r\n      delete args.__id__;\r\n    }\r\n    return callHook.call(this, hook, args)\r\n  };\r\n}\r\n\r\nfunction initScopedSlotsParams () {\r\n  const center = {};\r\n  const parents = {};\r\n\r\n  function currentId (fn) {\r\n    const vueIds = this.$options.propsData.vueId;\r\n    if (vueIds) {\r\n      const vueId = vueIds.split(',')[0];\r\n      fn(vueId);\r\n    }\r\n  }\r\n\r\n  Vue.prototype.$hasSSP = function (vueId) {\r\n    const slot = center[vueId];\r\n    if (!slot) {\r\n      parents[vueId] = this;\r\n      this.$on('hook:destroyed', () => {\r\n        delete parents[vueId];\r\n      });\r\n    }\r\n    return slot\r\n  };\r\n\r\n  Vue.prototype.$getSSP = function (vueId, name, needAll) {\r\n    const slot = center[vueId];\r\n    if (slot) {\r\n      const params = slot[name] || [];\r\n      if (needAll) {\r\n        return params\r\n      }\r\n      return params[0]\r\n    }\r\n  };\r\n\r\n  Vue.prototype.$setSSP = function (name, value) {\r\n    let index = 0;\r\n    currentId.call(this, vueId => {\r\n      const slot = center[vueId];\r\n      const params = slot[name] = slot[name] || [];\r\n      params.push(value);\r\n      index = params.length - 1;\r\n    });\r\n    return index\r\n  };\r\n\r\n  Vue.prototype.$initSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      center[vueId] = {};\r\n    });\r\n  };\r\n\r\n  Vue.prototype.$callSSP = function () {\r\n    currentId.call(this, vueId => {\r\n      if (parents[vueId]) {\r\n        parents[vueId].$forceUpdate();\r\n      }\r\n    });\r\n  };\r\n\r\n  Vue.mixin({\r\n    destroyed () {\r\n      const propsData = this.$options.propsData;\r\n      const vueId = propsData && propsData.vueId;\r\n      if (vueId) {\r\n        delete center[vueId];\r\n        delete parents[vueId];\r\n      }\r\n    }\r\n  });\r\n}\r\n\r\nfunction parseBaseApp (vm, {\r\n  mocks,\r\n  initRefs\r\n}) {\r\n  initEventChannel();\r\n  {\r\n    initScopedSlotsParams();\r\n  }\r\n  if (vm.$options.store) {\r\n    Vue.prototype.$store = vm.$options.store;\r\n  }\r\n  uniIdMixin(Vue);\r\n\r\n  Vue.prototype.mpHost = \"mp-weixin\";\r\n\r\n  Vue.mixin({\r\n    beforeCreate () {\r\n      if (!this.$options.mpType) {\r\n        return\r\n      }\r\n\r\n      this.mpType = this.$options.mpType;\r\n\r\n      this.$mp = {\r\n        data: {},\r\n        [this.mpType]: this.$options.mpInstance\r\n      };\r\n\r\n      this.$scope = this.$options.mpInstance;\r\n\r\n      delete this.$options.mpType;\r\n      delete this.$options.mpInstance;\r\n      if (\r\n        ( this.mpType === 'page') &&\r\n        typeof getApp === 'function'\r\n      ) { // hack vue-i18n\r\n        const app = getApp();\r\n        if (app.$vm && app.$vm.$i18n) {\r\n          this._i18n = app.$vm.$i18n;\r\n        }\r\n      }\r\n      if (this.mpType !== 'app') {\r\n        initRefs(this);\r\n        initMocks(this, mocks);\r\n      }\r\n    }\r\n  });\r\n\r\n  const appOptions = {\r\n    onLaunch (args) {\r\n      if (this.$vm) { // 已经初始化过了，主要是为了百度，百度 onShow 在 onLaunch 之前\r\n        return\r\n      }\r\n      {\r\n        if (wx.canIUse && !wx.canIUse('nextTick')) { // 事实 上2.2.3 即可，简单使用 2.3.0 的 nextTick 判断\r\n          console.error('当前微信基础库版本过低，请将 微信开发者工具-详情-项目设置-调试基础库版本 更换为`2.3.0`以上');\r\n        }\r\n      }\r\n\r\n      this.$vm = vm;\r\n\r\n      this.$vm.$mp = {\r\n        app: this\r\n      };\r\n\r\n      this.$vm.$scope = this;\r\n      // vm 上也挂载 globalData\r\n      this.$vm.globalData = this.globalData;\r\n\r\n      this.$vm._isMounted = true;\r\n      this.$vm.__call_hook('mounted', args);\r\n\r\n      this.$vm.__call_hook('onLaunch', args);\r\n    }\r\n  };\r\n\r\n  // 兼容旧版本 globalData\r\n  appOptions.globalData = vm.$options.globalData || {};\r\n  // 将 methods 中的方法挂在 getApp() 中\r\n  const methods = vm.$options.methods;\r\n  if (methods) {\r\n    Object.keys(methods).forEach(name => {\r\n      appOptions[name] = methods[name];\r\n    });\r\n  }\r\n\r\n  initAppLocale(Vue, vm, getLocaleLanguage$1());\r\n\r\n  initHooks(appOptions, hooks);\r\n  initUnknownHooks(appOptions, vm.$options);\r\n\r\n  return appOptions\r\n}\r\n\r\nfunction getLocaleLanguage$1 () {\r\n  let localeLanguage = '';\r\n  {\r\n    const appBaseInfo = wx.getAppBaseInfo();\r\n    const language =\r\n      appBaseInfo && appBaseInfo.language ? appBaseInfo.language : LOCALE_EN;\r\n    localeLanguage = normalizeLocale(language) || LOCALE_EN;\r\n  }\r\n  return localeLanguage\r\n}\r\n\r\nfunction parseApp (vm) {\r\n  return parseBaseApp(vm, {\r\n    mocks,\r\n    initRefs\r\n  })\r\n}\r\n\r\nfunction createApp (vm) {\r\n  App(parseApp(vm));\r\n  return vm\r\n}\r\n\r\nconst encodeReserveRE = /[!'()*]/g;\r\nconst encodeReserveReplacer = c => '%' + c.charCodeAt(0).toString(16);\r\nconst commaRE = /%2C/g;\r\n\r\n// fixed encodeURIComponent which is more conformant to RFC3986:\r\n// - escapes [!'()*]\r\n// - preserve commas\r\nconst encode = str => encodeURIComponent(str)\r\n  .replace(encodeReserveRE, encodeReserveReplacer)\r\n  .replace(commaRE, ',');\r\n\r\nfunction stringifyQuery (obj, encodeStr = encode) {\r\n  const res = obj ? Object.keys(obj).map(key => {\r\n    const val = obj[key];\r\n\r\n    if (val === undefined) {\r\n      return ''\r\n    }\r\n\r\n    if (val === null) {\r\n      return encodeStr(key)\r\n    }\r\n\r\n    if (Array.isArray(val)) {\r\n      const result = [];\r\n      val.forEach(val2 => {\r\n        if (val2 === undefined) {\r\n          return\r\n        }\r\n        if (val2 === null) {\r\n          result.push(encodeStr(key));\r\n        } else {\r\n          result.push(encodeStr(key) + '=' + encodeStr(val2));\r\n        }\r\n      });\r\n      return result.join('&')\r\n    }\r\n\r\n    return encodeStr(key) + '=' + encodeStr(val)\r\n  }).filter(x => x.length > 0).join('&') : null;\r\n  return res ? `?${res}` : ''\r\n}\r\n\r\nfunction parseBaseComponent (vueComponentOptions, {\r\n  isPage,\r\n  initRelation\r\n} = {}, needVueOptions) {\r\n  const [VueComponent, vueOptions] = initVueComponent(Vue, vueComponentOptions);\r\n\r\n  const options = {\r\n    multipleSlots: true,\r\n    // styleIsolation: 'apply-shared',\r\n    addGlobalClass: true,\r\n    ...(vueOptions.options || {})\r\n  };\r\n\r\n  {\r\n    // 微信 multipleSlots 部分情况有 bug，导致内容顺序错乱 如 u-list，提供覆盖选项\r\n    if (vueOptions['mp-weixin'] && vueOptions['mp-weixin'].options) {\r\n      Object.assign(options, vueOptions['mp-weixin'].options);\r\n    }\r\n  }\r\n\r\n  const componentOptions = {\r\n    options,\r\n    data: initData(vueOptions, Vue.prototype),\r\n    behaviors: initBehaviors(vueOptions, initBehavior),\r\n    properties: initProperties(vueOptions.props, false, vueOptions.__file, options),\r\n    lifetimes: {\r\n      attached () {\r\n        const properties = this.properties;\r\n\r\n        const options = {\r\n          mpType: isPage.call(this) ? 'page' : 'component',\r\n          mpInstance: this,\r\n          propsData: properties\r\n        };\r\n\r\n        initVueIds(properties.vueId, this);\r\n\r\n        // 处理父子关系\r\n        initRelation.call(this, {\r\n          vuePid: this._$vuePid,\r\n          vueOptions: options\r\n        });\r\n\r\n        // 初始化 vue 实例\r\n        this.$vm = new VueComponent(options);\r\n\r\n        // 处理$slots,$scopedSlots（暂不支持动态变化$slots）\r\n        initSlots(this.$vm, properties.vueSlots);\r\n\r\n        // 触发首次 setData\r\n        this.$vm.$mount();\r\n      },\r\n      ready () {\r\n        // 当组件 props 默认值为 true，初始化时传入 false 会导致 created,ready 触发, 但 attached 不触发\r\n        // https://developers.weixin.qq.com/community/develop/doc/00066ae2844cc0f8eb883e2a557800\r\n        if (this.$vm) {\r\n          this.$vm._isMounted = true;\r\n          this.$vm.__call_hook('mounted');\r\n          this.$vm.__call_hook('onReady');\r\n        }\r\n      },\r\n      detached () {\r\n        this.$vm && this.$vm.$destroy();\r\n      }\r\n    },\r\n    pageLifetimes: {\r\n      show (args) {\r\n        this.$vm && this.$vm.__call_hook('onPageShow', args);\r\n      },\r\n      hide () {\r\n        this.$vm && this.$vm.__call_hook('onPageHide');\r\n      },\r\n      resize (size) {\r\n        this.$vm && this.$vm.__call_hook('onPageResize', size);\r\n      }\r\n    },\r\n    methods: {\r\n      __l: handleLink,\r\n      __e: handleEvent\r\n    }\r\n  };\r\n  // externalClasses\r\n  if (vueOptions.externalClasses) {\r\n    componentOptions.externalClasses = vueOptions.externalClasses;\r\n  }\r\n\r\n  if (Array.isArray(vueOptions.wxsCallMethods)) {\r\n    vueOptions.wxsCallMethods.forEach(callMethod => {\r\n      componentOptions.methods[callMethod] = function (args) {\r\n        return this.$vm[callMethod](args)\r\n      };\r\n    });\r\n  }\r\n\r\n  if (needVueOptions) {\r\n    return [componentOptions, vueOptions, VueComponent]\r\n  }\r\n  if (isPage) {\r\n    return componentOptions\r\n  }\r\n  return [componentOptions, VueComponent]\r\n}\r\n\r\nfunction parseComponent (vueComponentOptions, needVueOptions) {\r\n  return parseBaseComponent(vueComponentOptions, {\r\n    isPage,\r\n    initRelation\r\n  }, needVueOptions)\r\n}\r\n\r\nconst hooks$1 = [\r\n  'onShow',\r\n  'onHide',\r\n  'onUnload'\r\n];\r\n\r\nhooks$1.push(...PAGE_EVENT_HOOKS);\r\n\r\nfunction parseBasePage (vuePageOptions) {\r\n  const [pageOptions, vueOptions] = parseComponent(vuePageOptions, true);\r\n\r\n  initHooks(pageOptions.methods, hooks$1, vueOptions);\r\n\r\n  pageOptions.methods.onLoad = function (query) {\r\n    this.options = query;\r\n    const copyQuery = Object.assign({}, query);\r\n    delete copyQuery.__id__;\r\n    this.$page = {\r\n      fullPath: '/' + (this.route || this.is) + stringifyQuery(copyQuery)\r\n    };\r\n    this.$vm.$mp.query = query; // 兼容 mpvue\r\n    this.$vm.__call_hook('onLoad', query);\r\n  };\r\n  {\r\n    initUnknownHooks(pageOptions.methods, vuePageOptions, ['onReady']);\r\n  }\r\n  {\r\n    initWorkletMethods(pageOptions.methods, vueOptions.methods);\r\n  }\r\n\r\n  return pageOptions\r\n}\r\n\r\nfunction parsePage (vuePageOptions) {\r\n  return parseBasePage(vuePageOptions)\r\n}\r\n\r\nfunction createPage (vuePageOptions) {\r\n  {\r\n    return Component(parsePage(vuePageOptions))\r\n  }\r\n}\r\n\r\nfunction createComponent (vueOptions) {\r\n  {\r\n    return Component(parseComponent(vueOptions))\r\n  }\r\n}\r\n\r\nfunction createSubpackageApp (vm) {\r\n  const appOptions = parseApp(vm);\r\n  const app = getApp({\r\n    allowDefault: true\r\n  });\r\n  vm.$scope = app;\r\n  const globalData = app.globalData;\r\n  if (globalData) {\r\n    Object.keys(appOptions.globalData).forEach(name => {\r\n      if (!hasOwn(globalData, name)) {\r\n        globalData[name] = appOptions.globalData[name];\r\n      }\r\n    });\r\n  }\r\n  Object.keys(appOptions).forEach(name => {\r\n    if (!hasOwn(app, name)) {\r\n      app[name] = appOptions[name];\r\n    }\r\n  });\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\nfunction createPlugin (vm) {\r\n  const appOptions = parseApp(vm);\r\n  if (isFn(appOptions.onShow) && wx.onAppShow) {\r\n    wx.onAppShow((...args) => {\r\n      vm.__call_hook('onShow', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onHide) && wx.onAppHide) {\r\n    wx.onAppHide((...args) => {\r\n      vm.__call_hook('onHide', args);\r\n    });\r\n  }\r\n  if (isFn(appOptions.onLaunch)) {\r\n    const args = wx.getLaunchOptionsSync && wx.getLaunchOptionsSync();\r\n    vm.__call_hook('onLaunch', args);\r\n  }\r\n  return vm\r\n}\r\n\r\ntodos.forEach(todoApi => {\r\n  protocols[todoApi] = false;\r\n});\r\n\r\ncanIUses.forEach(canIUseApi => {\r\n  const apiName = protocols[canIUseApi] && protocols[canIUseApi].name ? protocols[canIUseApi].name\r\n    : canIUseApi;\r\n  if (!wx.canIUse(apiName)) {\r\n    protocols[canIUseApi] = false;\r\n  }\r\n});\r\n\r\nlet uni = {};\r\n\r\nif (typeof Proxy !== 'undefined' && \"mp-weixin\" !== 'app-plus') {\r\n  uni = new Proxy({}, {\r\n    get (target, name) {\r\n      if (hasOwn(target, name)) {\r\n        return target[name]\r\n      }\r\n      if (baseApi[name]) {\r\n        return baseApi[name]\r\n      }\r\n      if (api[name]) {\r\n        return promisify(name, api[name])\r\n      }\r\n      {\r\n        if (extraApi[name]) {\r\n          return promisify(name, extraApi[name])\r\n        }\r\n        if (todoApis[name]) {\r\n          return promisify(name, todoApis[name])\r\n        }\r\n      }\r\n      if (eventApi[name]) {\r\n        return eventApi[name]\r\n      }\r\n      return promisify(name, wrapper(name, wx[name]))\r\n    },\r\n    set (target, name, value) {\r\n      target[name] = value;\r\n      return true\r\n    }\r\n  });\r\n} else {\r\n  Object.keys(baseApi).forEach(name => {\r\n    uni[name] = baseApi[name];\r\n  });\r\n\r\n  {\r\n    Object.keys(todoApis).forEach(name => {\r\n      uni[name] = promisify(name, todoApis[name]);\r\n    });\r\n    Object.keys(extraApi).forEach(name => {\r\n      uni[name] = promisify(name, extraApi[name]);\r\n    });\r\n  }\r\n\r\n  Object.keys(eventApi).forEach(name => {\r\n    uni[name] = eventApi[name];\r\n  });\r\n\r\n  Object.keys(api).forEach(name => {\r\n    uni[name] = promisify(name, api[name]);\r\n  });\r\n\r\n  Object.keys(wx).forEach(name => {\r\n    if (hasOwn(wx, name) || hasOwn(protocols, name)) {\r\n      uni[name] = promisify(name, wrapper(name, wx[name]));\r\n    }\r\n  });\r\n}\r\n\r\nwx.createApp = createApp;\r\nwx.createPage = createPage;\r\nwx.createComponent = createComponent;\r\nwx.createSubpackageApp = createSubpackageApp;\r\nwx.createPlugin = createPlugin;\r\n\r\nvar uni$1 = uni;\r\n\r\nexport default uni$1;\r\nexport { createApp, createComponent, createPage, createPlugin, createSubpackageApp };\r\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "function _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayWithHoles = require(\"./arrayWithHoles.js\");\nvar iterableToArrayLimit = require(\"./iterableToArrayLimit.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableRest = require(\"./nonIterableRest.js\");\nfunction _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || unsupportedIterableToArray(arr, i) || nonIterableRest();\n}\nmodule.exports = _slicedToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nmodule.exports = _arrayWithHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nmodule.exports = _iterableToArrayLimit, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return arrayLikeToArray(o, minLen);\n}\nmodule.exports = _unsupportedIterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nmodule.exports = _arrayLikeToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableRest, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var setPrototypeOf = require(\"./setPrototypeOf.js\");\nvar isNativeReflectConstruct = require(\"./isNativeReflectConstruct.js\");\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nmodule.exports = _construct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (module.exports = _isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports)();\n}\nmodule.exports = _isNativeReflectConstruct, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayWithoutHoles = require(\"./arrayWithoutHoles.js\");\nvar iterableToArray = require(\"./iterableToArray.js\");\nvar unsupportedIterableToArray = require(\"./unsupportedIterableToArray.js\");\nvar nonIterableSpread = require(\"./nonIterableSpread.js\");\nfunction _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || unsupportedIterableToArray(arr) || nonIterableSpread();\n}\nmodule.exports = _toConsumableArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var arrayLikeToArray = require(\"./arrayLikeToArray.js\");\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return arrayLikeToArray(arr);\n}\nmodule.exports = _arrayWithoutHoles, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nmodule.exports = _iterableToArray, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nmodule.exports = _nonIterableSpread, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "const isObject = (val) => val !== null && typeof val === 'object';\nconst defaultDelimiters = ['{', '}'];\nclass BaseFormatter {\n    constructor() {\n        this._caches = Object.create(null);\n    }\n    interpolate(message, values, delimiters = defaultDelimiters) {\n        if (!values) {\n            return [message];\n        }\n        let tokens = this._caches[message];\n        if (!tokens) {\n            tokens = parse(message, delimiters);\n            this._caches[message] = tokens;\n        }\n        return compile(tokens, values);\n    }\n}\nconst RE_TOKEN_LIST_VALUE = /^(?:\\d)+/;\nconst RE_TOKEN_NAMED_VALUE = /^(?:\\w)+/;\nfunction parse(format, [startDelimiter, endDelimiter]) {\n    const tokens = [];\n    let position = 0;\n    let text = '';\n    while (position < format.length) {\n        let char = format[position++];\n        if (char === startDelimiter) {\n            if (text) {\n                tokens.push({ type: 'text', value: text });\n            }\n            text = '';\n            let sub = '';\n            char = format[position++];\n            while (char !== undefined && char !== endDelimiter) {\n                sub += char;\n                char = format[position++];\n            }\n            const isClosed = char === endDelimiter;\n            const type = RE_TOKEN_LIST_VALUE.test(sub)\n                ? 'list'\n                : isClosed && RE_TOKEN_NAMED_VALUE.test(sub)\n                    ? 'named'\n                    : 'unknown';\n            tokens.push({ value: sub, type });\n        }\n        //  else if (char === '%') {\n        //   // when found rails i18n syntax, skip text capture\n        //   if (format[position] !== '{') {\n        //     text += char\n        //   }\n        // }\n        else {\n            text += char;\n        }\n    }\n    text && tokens.push({ type: 'text', value: text });\n    return tokens;\n}\nfunction compile(tokens, values) {\n    const compiled = [];\n    let index = 0;\n    const mode = Array.isArray(values)\n        ? 'list'\n        : isObject(values)\n            ? 'named'\n            : 'unknown';\n    if (mode === 'unknown') {\n        return compiled;\n    }\n    while (index < tokens.length) {\n        const token = tokens[index];\n        switch (token.type) {\n            case 'text':\n                compiled.push(token.value);\n                break;\n            case 'list':\n                compiled.push(values[parseInt(token.value, 10)]);\n                break;\n            case 'named':\n                if (mode === 'named') {\n                    compiled.push(values[token.value]);\n                }\n                else {\n                    if (process.env.NODE_ENV !== 'production') {\n                        console.warn(`Type of token '${token.type}' and format of value '${mode}' don't match!`);\n                    }\n                }\n                break;\n            case 'unknown':\n                if (process.env.NODE_ENV !== 'production') {\n                    console.warn(`Detect 'unknown' type of token!`);\n                }\n                break;\n        }\n        index++;\n    }\n    return compiled;\n}\n\nconst LOCALE_ZH_HANS = 'zh-Hans';\nconst LOCALE_ZH_HANT = 'zh-Hant';\nconst LOCALE_EN = 'en';\nconst LOCALE_FR = 'fr';\nconst LOCALE_ES = 'es';\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nconst hasOwn = (val, key) => hasOwnProperty.call(val, key);\nconst defaultFormatter = new BaseFormatter();\nfunction include(str, parts) {\n    return !!parts.find((part) => str.indexOf(part) !== -1);\n}\nfunction startsWith(str, parts) {\n    return parts.find((part) => str.indexOf(part) === 0);\n}\nfunction normalizeLocale(locale, messages) {\n    if (!locale) {\n        return;\n    }\n    locale = locale.trim().replace(/_/g, '-');\n    if (messages && messages[locale]) {\n        return locale;\n    }\n    locale = locale.toLowerCase();\n    if (locale === 'chinese') {\n        // 支付宝\n        return LOCALE_ZH_HANS;\n    }\n    if (locale.indexOf('zh') === 0) {\n        if (locale.indexOf('-hans') > -1) {\n            return LOCALE_ZH_HANS;\n        }\n        if (locale.indexOf('-hant') > -1) {\n            return LOCALE_ZH_HANT;\n        }\n        if (include(locale, ['-tw', '-hk', '-mo', '-cht'])) {\n            return LOCALE_ZH_HANT;\n        }\n        return LOCALE_ZH_HANS;\n    }\n    let locales = [LOCALE_EN, LOCALE_FR, LOCALE_ES];\n    if (messages && Object.keys(messages).length > 0) {\n        locales = Object.keys(messages);\n    }\n    const lang = startsWith(locale, locales);\n    if (lang) {\n        return lang;\n    }\n}\nclass I18n {\n    constructor({ locale, fallbackLocale, messages, watcher, formater, }) {\n        this.locale = LOCALE_EN;\n        this.fallbackLocale = LOCALE_EN;\n        this.message = {};\n        this.messages = {};\n        this.watchers = [];\n        if (fallbackLocale) {\n            this.fallbackLocale = fallbackLocale;\n        }\n        this.formater = formater || defaultFormatter;\n        this.messages = messages || {};\n        this.setLocale(locale || LOCALE_EN);\n        if (watcher) {\n            this.watchLocale(watcher);\n        }\n    }\n    setLocale(locale) {\n        const oldLocale = this.locale;\n        this.locale = normalizeLocale(locale, this.messages) || this.fallbackLocale;\n        if (!this.messages[this.locale]) {\n            // 可能初始化时不存在\n            this.messages[this.locale] = {};\n        }\n        this.message = this.messages[this.locale];\n        // 仅发生变化时，通知\n        if (oldLocale !== this.locale) {\n            this.watchers.forEach((watcher) => {\n                watcher(this.locale, oldLocale);\n            });\n        }\n    }\n    getLocale() {\n        return this.locale;\n    }\n    watchLocale(fn) {\n        const index = this.watchers.push(fn) - 1;\n        return () => {\n            this.watchers.splice(index, 1);\n        };\n    }\n    add(locale, message, override = true) {\n        const curMessages = this.messages[locale];\n        if (curMessages) {\n            if (override) {\n                Object.assign(curMessages, message);\n            }\n            else {\n                Object.keys(message).forEach((key) => {\n                    if (!hasOwn(curMessages, key)) {\n                        curMessages[key] = message[key];\n                    }\n                });\n            }\n        }\n        else {\n            this.messages[locale] = message;\n        }\n    }\n    f(message, values, delimiters) {\n        return this.formater.interpolate(message, values, delimiters).join('');\n    }\n    t(key, locale, values) {\n        let message = this.message;\n        if (typeof locale === 'string') {\n            locale = normalizeLocale(locale, this.messages);\n            locale && (message = this.messages[locale]);\n        }\n        else {\n            values = locale;\n        }\n        if (!hasOwn(message, key)) {\n            console.warn(`Cannot translate the value of keypath ${key}. Use the value of keypath as default.`);\n            return key;\n        }\n        return this.formater.interpolate(message[key], values).join('');\n    }\n}\n\nfunction watchAppLocale(appVm, i18n) {\n    // 需要保证 watch 的触发在组件渲染之前\n    if (appVm.$watchLocale) {\n        // vue2\n        appVm.$watchLocale((newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n    else {\n        appVm.$watch(() => appVm.$locale, (newLocale) => {\n            i18n.setLocale(newLocale);\n        });\n    }\n}\nfunction getDefaultLocale() {\n    if (typeof uni !== 'undefined' && uni.getLocale) {\n        return uni.getLocale();\n    }\n    // 小程序平台，uni 和 uni-i18n 互相引用，导致访问不到 uni，故在 global 上挂了 getLocale\n    if (typeof global !== 'undefined' && global.getLocale) {\n        return global.getLocale();\n    }\n    return LOCALE_EN;\n}\nfunction initVueI18n(locale, messages = {}, fallbackLocale, watcher) {\n    // 兼容旧版本入参\n    if (typeof locale !== 'string') {\n        [locale, messages] = [\n            messages,\n            locale,\n        ];\n    }\n    if (typeof locale !== 'string') {\n        // 因为小程序平台，uni-i18n 和 uni 互相引用，导致此时访问 uni 时，为 undefined\n        locale = getDefaultLocale();\n    }\n    if (typeof fallbackLocale !== 'string') {\n        fallbackLocale =\n            (typeof __uniConfig !== 'undefined' && __uniConfig.fallbackLocale) ||\n                LOCALE_EN;\n    }\n    const i18n = new I18n({\n        locale,\n        fallbackLocale,\n        messages,\n        watcher,\n    });\n    let t = (key, values) => {\n        if (typeof getApp !== 'function') {\n            // app view\n            /* eslint-disable no-func-assign */\n            t = function (key, values) {\n                return i18n.t(key, values);\n            };\n        }\n        else {\n            let isWatchedAppLocale = false;\n            t = function (key, values) {\n                const appVm = getApp().$vm;\n                // 可能$vm还不存在，比如在支付宝小程序中，组件定义较早，在props的default里使用了t()函数（如uni-goods-nav），此时app还未初始化\n                // options: {\n                // \ttype: Array,\n                // \tdefault () {\n                // \t\treturn [{\n                // \t\t\ticon: 'shop',\n                // \t\t\ttext: t(\"uni-goods-nav.options.shop\"),\n                // \t\t}, {\n                // \t\t\ticon: 'cart',\n                // \t\t\ttext: t(\"uni-goods-nav.options.cart\")\n                // \t\t}]\n                // \t}\n                // },\n                if (appVm) {\n                    // 触发响应式\n                    appVm.$locale;\n                    if (!isWatchedAppLocale) {\n                        isWatchedAppLocale = true;\n                        watchAppLocale(appVm, i18n);\n                    }\n                }\n                return i18n.t(key, values);\n            };\n        }\n        return t(key, values);\n    };\n    return {\n        i18n,\n        f(message, values, delimiters) {\n            return i18n.f(message, values, delimiters);\n        },\n        t(key, values) {\n            return t(key, values);\n        },\n        add(locale, message, override = true) {\n            return i18n.add(locale, message, override);\n        },\n        watch(fn) {\n            return i18n.watchLocale(fn);\n        },\n        getLocale() {\n            return i18n.getLocale();\n        },\n        setLocale(newLocale) {\n            return i18n.setLocale(newLocale);\n        },\n    };\n}\n\nconst isString = (val) => typeof val === 'string';\nlet formater;\nfunction hasI18nJson(jsonObj, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    return walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                return true;\n            }\n        }\n        else {\n            return hasI18nJson(value, delimiters);\n        }\n    });\n}\nfunction parseI18nJson(jsonObj, values, delimiters) {\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        const value = jsonObj[key];\n        if (isString(value)) {\n            if (isI18nStr(value, delimiters)) {\n                jsonObj[key] = compileStr(value, values, delimiters);\n            }\n        }\n        else {\n            parseI18nJson(value, values, delimiters);\n        }\n    });\n    return jsonObj;\n}\nfunction compileI18nJsonStr(jsonStr, { locale, locales, delimiters, }) {\n    if (!isI18nStr(jsonStr, delimiters)) {\n        return jsonStr;\n    }\n    if (!formater) {\n        formater = new BaseFormatter();\n    }\n    const localeValues = [];\n    Object.keys(locales).forEach((name) => {\n        if (name !== locale) {\n            localeValues.push({\n                locale: name,\n                values: locales[name],\n            });\n        }\n    });\n    localeValues.unshift({ locale, values: locales[locale] });\n    try {\n        return JSON.stringify(compileJsonObj(JSON.parse(jsonStr), localeValues, delimiters), null, 2);\n    }\n    catch (e) { }\n    return jsonStr;\n}\nfunction isI18nStr(value, delimiters) {\n    return value.indexOf(delimiters[0]) > -1;\n}\nfunction compileStr(value, values, delimiters) {\n    return formater.interpolate(value, values, delimiters).join('');\n}\nfunction compileValue(jsonObj, key, localeValues, delimiters) {\n    const value = jsonObj[key];\n    if (isString(value)) {\n        // 存在国际化\n        if (isI18nStr(value, delimiters)) {\n            jsonObj[key] = compileStr(value, localeValues[0].values, delimiters);\n            if (localeValues.length > 1) {\n                // 格式化国际化语言\n                const valueLocales = (jsonObj[key + 'Locales'] = {});\n                localeValues.forEach((localValue) => {\n                    valueLocales[localValue.locale] = compileStr(value, localValue.values, delimiters);\n                });\n            }\n        }\n    }\n    else {\n        compileJsonObj(value, localeValues, delimiters);\n    }\n}\nfunction compileJsonObj(jsonObj, localeValues, delimiters) {\n    walkJsonObj(jsonObj, (jsonObj, key) => {\n        compileValue(jsonObj, key, localeValues, delimiters);\n    });\n    return jsonObj;\n}\nfunction walkJsonObj(jsonObj, walk) {\n    if (Array.isArray(jsonObj)) {\n        for (let i = 0; i < jsonObj.length; i++) {\n            if (walk(jsonObj, i)) {\n                return true;\n            }\n        }\n    }\n    else if (isObject(jsonObj)) {\n        for (const key in jsonObj) {\n            if (walk(jsonObj, key)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\nfunction resolveLocale(locales) {\n    return (locale) => {\n        if (!locale) {\n            return locale;\n        }\n        locale = normalizeLocale(locale) || locale;\n        return resolveLocaleChain(locale).find((locale) => locales.indexOf(locale) > -1);\n    };\n}\nfunction resolveLocaleChain(locale) {\n    const chain = [];\n    const tokens = locale.split('-');\n    while (tokens.length) {\n        chain.push(tokens.join('-'));\n        tokens.pop();\n    }\n    return chain;\n}\n\nexport { BaseFormatter as Formatter, I18n, LOCALE_EN, LOCALE_ES, LOCALE_FR, LOCALE_ZH_HANS, LOCALE_ZH_HANT, compileI18nJsonStr, hasI18nJson, initVueI18n, isI18nStr, isString, normalizeLocale, parseI18nJson, resolveLocale };\n", "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nmodule.exports = _classCallCheck, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nmodule.exports = _createClass, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "/*!\n * Vue.js v2.6.11\n * (c) 2014-2024 Evan You\n * Released under the MIT License.\n */\n/*  */\n\nvar emptyObject = Object.freeze({});\n\n// These helpers produce better VM code in JS engines due to their\n// explicitness and function inlining.\nfunction isUndef (v) {\n  return v === undefined || v === null\n}\n\nfunction isDef (v) {\n  return v !== undefined && v !== null\n}\n\nfunction isTrue (v) {\n  return v === true\n}\n\nfunction isFalse (v) {\n  return v === false\n}\n\n/**\n * Check if value is primitive.\n */\nfunction isPrimitive (value) {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    // $flow-disable-line\n    typeof value === 'symbol' ||\n    typeof value === 'boolean'\n  )\n}\n\n/**\n * Quick object check - this is primarily used to tell\n * Objects from primitive values when we know the value\n * is a JSON-compliant type.\n */\nfunction isObject (obj) {\n  return obj !== null && typeof obj === 'object'\n}\n\n/**\n * Get the raw type string of a value, e.g., [object Object].\n */\nvar _toString = Object.prototype.toString;\n\nfunction toRawType (value) {\n  return _toString.call(value).slice(8, -1)\n}\n\n/**\n * Strict object type check. Only returns true\n * for plain JavaScript objects.\n */\nfunction isPlainObject (obj) {\n  return _toString.call(obj) === '[object Object]'\n}\n\nfunction isRegExp (v) {\n  return _toString.call(v) === '[object RegExp]'\n}\n\n/**\n * Check if val is a valid array index.\n */\nfunction isValidArrayIndex (val) {\n  var n = parseFloat(String(val));\n  return n >= 0 && Math.floor(n) === n && isFinite(val)\n}\n\nfunction isPromise (val) {\n  return (\n    isDef(val) &&\n    typeof val.then === 'function' &&\n    typeof val.catch === 'function'\n  )\n}\n\n/**\n * Convert a value to a string that is actually rendered.\n */\nfunction toString (val) {\n  return val == null\n    ? ''\n    : Array.isArray(val) || (isPlainObject(val) && val.toString === _toString)\n      ? JSON.stringify(val, null, 2)\n      : String(val)\n}\n\n/**\n * Convert an input value to a number for persistence.\n * If the conversion fails, return original string.\n */\nfunction toNumber (val) {\n  var n = parseFloat(val);\n  return isNaN(n) ? val : n\n}\n\n/**\n * Make a map and return a function for checking if a key\n * is in that map.\n */\nfunction makeMap (\n  str,\n  expectsLowerCase\n) {\n  var map = Object.create(null);\n  var list = str.split(',');\n  for (var i = 0; i < list.length; i++) {\n    map[list[i]] = true;\n  }\n  return expectsLowerCase\n    ? function (val) { return map[val.toLowerCase()]; }\n    : function (val) { return map[val]; }\n}\n\n/**\n * Check if a tag is a built-in tag.\n */\nvar isBuiltInTag = makeMap('slot,component', true);\n\n/**\n * Check if an attribute is a reserved attribute.\n */\nvar isReservedAttribute = makeMap('key,ref,slot,slot-scope,is');\n\n/**\n * Remove an item from an array.\n */\nfunction remove (arr, item) {\n  if (arr.length) {\n    var index = arr.indexOf(item);\n    if (index > -1) {\n      return arr.splice(index, 1)\n    }\n  }\n}\n\n/**\n * Check whether an object has the property.\n */\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn (obj, key) {\n  return hasOwnProperty.call(obj, key)\n}\n\n/**\n * Create a cached version of a pure function.\n */\nfunction cached (fn) {\n  var cache = Object.create(null);\n  return (function cachedFn (str) {\n    var hit = cache[str];\n    return hit || (cache[str] = fn(str))\n  })\n}\n\n/**\n * Camelize a hyphen-delimited string.\n */\nvar camelizeRE = /-(\\w)/g;\nvar camelize = cached(function (str) {\n  return str.replace(camelizeRE, function (_, c) { return c ? c.toUpperCase() : ''; })\n});\n\n/**\n * Capitalize a string.\n */\nvar capitalize = cached(function (str) {\n  return str.charAt(0).toUpperCase() + str.slice(1)\n});\n\n/**\n * Hyphenate a camelCase string.\n */\nvar hyphenateRE = /\\B([A-Z])/g;\nvar hyphenate = cached(function (str) {\n  return str.replace(hyphenateRE, '-$1').toLowerCase()\n});\n\n/**\n * Simple bind polyfill for environments that do not support it,\n * e.g., PhantomJS 1.x. Technically, we don't need this anymore\n * since native bind is now performant enough in most browsers.\n * But removing it would mean breaking code that was able to run in\n * PhantomJS 1.x, so this must be kept for backward compatibility.\n */\n\n/* istanbul ignore next */\nfunction polyfillBind (fn, ctx) {\n  function boundFn (a) {\n    var l = arguments.length;\n    return l\n      ? l > 1\n        ? fn.apply(ctx, arguments)\n        : fn.call(ctx, a)\n      : fn.call(ctx)\n  }\n\n  boundFn._length = fn.length;\n  return boundFn\n}\n\nfunction nativeBind (fn, ctx) {\n  return fn.bind(ctx)\n}\n\nvar bind = Function.prototype.bind\n  ? nativeBind\n  : polyfillBind;\n\n/**\n * Convert an Array-like object to a real Array.\n */\nfunction toArray (list, start) {\n  start = start || 0;\n  var i = list.length - start;\n  var ret = new Array(i);\n  while (i--) {\n    ret[i] = list[i + start];\n  }\n  return ret\n}\n\n/**\n * Mix properties into target object.\n */\nfunction extend (to, _from) {\n  for (var key in _from) {\n    to[key] = _from[key];\n  }\n  return to\n}\n\n/**\n * Merge an Array of Objects into a single Object.\n */\nfunction toObject (arr) {\n  var res = {};\n  for (var i = 0; i < arr.length; i++) {\n    if (arr[i]) {\n      extend(res, arr[i]);\n    }\n  }\n  return res\n}\n\n/* eslint-disable no-unused-vars */\n\n/**\n * Perform no operation.\n * Stubbing args to make Flow happy without leaving useless transpiled code\n * with ...rest (https://flow.org/blog/2017/05/07/Strict-Function-Call-Arity/).\n */\nfunction noop (a, b, c) {}\n\n/**\n * Always return false.\n */\nvar no = function (a, b, c) { return false; };\n\n/* eslint-enable no-unused-vars */\n\n/**\n * Return the same value.\n */\nvar identity = function (_) { return _; };\n\n/**\n * Check if two values are loosely equal - that is,\n * if they are plain objects, do they have the same shape?\n */\nfunction looseEqual (a, b) {\n  if (a === b) { return true }\n  var isObjectA = isObject(a);\n  var isObjectB = isObject(b);\n  if (isObjectA && isObjectB) {\n    try {\n      var isArrayA = Array.isArray(a);\n      var isArrayB = Array.isArray(b);\n      if (isArrayA && isArrayB) {\n        return a.length === b.length && a.every(function (e, i) {\n          return looseEqual(e, b[i])\n        })\n      } else if (a instanceof Date && b instanceof Date) {\n        return a.getTime() === b.getTime()\n      } else if (!isArrayA && !isArrayB) {\n        var keysA = Object.keys(a);\n        var keysB = Object.keys(b);\n        return keysA.length === keysB.length && keysA.every(function (key) {\n          return looseEqual(a[key], b[key])\n        })\n      } else {\n        /* istanbul ignore next */\n        return false\n      }\n    } catch (e) {\n      /* istanbul ignore next */\n      return false\n    }\n  } else if (!isObjectA && !isObjectB) {\n    return String(a) === String(b)\n  } else {\n    return false\n  }\n}\n\n/**\n * Return the first index at which a loosely equal value can be\n * found in the array (if value is a plain object, the array must\n * contain an object of the same shape), or -1 if it is not present.\n */\nfunction looseIndexOf (arr, val) {\n  for (var i = 0; i < arr.length; i++) {\n    if (looseEqual(arr[i], val)) { return i }\n  }\n  return -1\n}\n\n/**\n * Ensure a function is called only once.\n */\nfunction once (fn) {\n  var called = false;\n  return function () {\n    if (!called) {\n      called = true;\n      fn.apply(this, arguments);\n    }\n  }\n}\n\nvar ASSET_TYPES = [\n  'component',\n  'directive',\n  'filter'\n];\n\nvar LIFECYCLE_HOOKS = [\n  'beforeCreate',\n  'created',\n  'beforeMount',\n  'mounted',\n  'beforeUpdate',\n  'updated',\n  'beforeDestroy',\n  'destroyed',\n  'activated',\n  'deactivated',\n  'errorCaptured',\n  'serverPrefetch'\n];\n\n/*  */\n\n\n\nvar config = ({\n  /**\n   * Option merge strategies (used in core/util/options)\n   */\n  // $flow-disable-line\n  optionMergeStrategies: Object.create(null),\n\n  /**\n   * Whether to suppress warnings.\n   */\n  silent: false,\n\n  /**\n   * Show production mode tip message on boot?\n   */\n  productionTip: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to enable devtools\n   */\n  devtools: process.env.NODE_ENV !== 'production',\n\n  /**\n   * Whether to record perf\n   */\n  performance: false,\n\n  /**\n   * Error handler for watcher errors\n   */\n  errorHandler: null,\n\n  /**\n   * Warn handler for watcher warns\n   */\n  warnHandler: null,\n\n  /**\n   * Ignore certain custom elements\n   */\n  ignoredElements: [],\n\n  /**\n   * Custom user key aliases for v-on\n   */\n  // $flow-disable-line\n  keyCodes: Object.create(null),\n\n  /**\n   * Check if a tag is reserved so that it cannot be registered as a\n   * component. This is platform-dependent and may be overwritten.\n   */\n  isReservedTag: no,\n\n  /**\n   * Check if an attribute is reserved so that it cannot be used as a component\n   * prop. This is platform-dependent and may be overwritten.\n   */\n  isReservedAttr: no,\n\n  /**\n   * Check if a tag is an unknown element.\n   * Platform-dependent.\n   */\n  isUnknownElement: no,\n\n  /**\n   * Get the namespace of an element\n   */\n  getTagNamespace: noop,\n\n  /**\n   * Parse the real tag name for the specific platform.\n   */\n  parsePlatformTagName: identity,\n\n  /**\n   * Check if an attribute must be bound using property, e.g. value\n   * Platform-dependent.\n   */\n  mustUseProp: no,\n\n  /**\n   * Perform updates asynchronously. Intended to be used by Vue Test Utils\n   * This will significantly reduce performance if set to false.\n   */\n  async: true,\n\n  /**\n   * Exposed for legacy reasons\n   */\n  _lifecycleHooks: LIFECYCLE_HOOKS\n});\n\n/*  */\n\n/**\n * unicode letters used for parsing html tags, component names and property paths.\n * using https://www.w3.org/TR/html53/semantics-scripting.html#potentialcustomelementname\n * skipping \\u10000-\\uEFFFF due to it freezing up PhantomJS\n */\nvar unicodeRegExp = /a-zA-Z\\u00B7\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u203F-\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD/;\n\n/**\n * Check if a string starts with $ or _\n */\nfunction isReserved (str) {\n  var c = (str + '').charCodeAt(0);\n  return c === 0x24 || c === 0x5F\n}\n\n/**\n * Define a property.\n */\nfunction def (obj, key, val, enumerable) {\n  Object.defineProperty(obj, key, {\n    value: val,\n    enumerable: !!enumerable,\n    writable: true,\n    configurable: true\n  });\n}\n\n/**\n * Parse simple path.\n */\nvar bailRE = new RegExp((\"[^\" + (unicodeRegExp.source) + \".$_\\\\d]\"));\nfunction parsePath (path) {\n  if (bailRE.test(path)) {\n    return\n  }\n  var segments = path.split('.');\n  return function (obj) {\n    for (var i = 0; i < segments.length; i++) {\n      if (!obj) { return }\n      obj = obj[segments[i]];\n    }\n    return obj\n  }\n}\n\n/*  */\n\n// can we use __proto__?\nvar hasProto = '__proto__' in {};\n\n// Browser environment sniffing\nvar inBrowser = typeof window !== 'undefined';\nvar inWeex = typeof WXEnvironment !== 'undefined' && !!WXEnvironment.platform;\nvar weexPlatform = inWeex && WXEnvironment.platform.toLowerCase();\nvar UA = inBrowser && window.navigator && window.navigator.userAgent.toLowerCase();\nvar isIE = UA && /msie|trident/.test(UA);\nvar isIE9 = UA && UA.indexOf('msie 9.0') > 0;\nvar isEdge = UA && UA.indexOf('edge/') > 0;\nvar isAndroid = (UA && UA.indexOf('android') > 0) || (weexPlatform === 'android');\nvar isIOS = (UA && /iphone|ipad|ipod|ios/.test(UA)) || (weexPlatform === 'ios');\nvar isChrome = UA && /chrome\\/\\d+/.test(UA) && !isEdge;\nvar isPhantomJS = UA && /phantomjs/.test(UA);\nvar isFF = UA && UA.match(/firefox\\/(\\d+)/);\n\n// Firefox has a \"watch\" function on Object.prototype...\nvar nativeWatch = ({}).watch;\nif (inBrowser) {\n  try {\n    var opts = {};\n    Object.defineProperty(opts, 'passive', ({\n      get: function get () {\n      }\n    })); // https://github.com/facebook/flow/issues/285\n    window.addEventListener('test-passive', null, opts);\n  } catch (e) {}\n}\n\n// this needs to be lazy-evaled because vue may be required before\n// vue-server-renderer can set VUE_ENV\nvar _isServer;\nvar isServerRendering = function () {\n  if (_isServer === undefined) {\n    /* istanbul ignore if */\n    if (!inBrowser && !inWeex && typeof global !== 'undefined') {\n      // detect presence of vue-server-renderer and avoid\n      // Webpack shimming the process\n      _isServer = global['process'] && global['process'].env.VUE_ENV === 'server';\n    } else {\n      _isServer = false;\n    }\n  }\n  return _isServer\n};\n\n// detect devtools\nvar devtools = inBrowser && window.__VUE_DEVTOOLS_GLOBAL_HOOK__;\n\n/* istanbul ignore next */\nfunction isNative (Ctor) {\n  return typeof Ctor === 'function' && /native code/.test(Ctor.toString())\n}\n\nvar hasSymbol =\n  typeof Symbol !== 'undefined' && isNative(Symbol) &&\n  typeof Reflect !== 'undefined' && isNative(Reflect.ownKeys);\n\nvar _Set;\n/* istanbul ignore if */ // $flow-disable-line\nif (typeof Set !== 'undefined' && isNative(Set)) {\n  // use native Set when available.\n  _Set = Set;\n} else {\n  // a non-standard Set polyfill that only works with primitive keys.\n  _Set = /*@__PURE__*/(function () {\n    function Set () {\n      this.set = Object.create(null);\n    }\n    Set.prototype.has = function has (key) {\n      return this.set[key] === true\n    };\n    Set.prototype.add = function add (key) {\n      this.set[key] = true;\n    };\n    Set.prototype.clear = function clear () {\n      this.set = Object.create(null);\n    };\n\n    return Set;\n  }());\n}\n\n/*  */\n\nvar warn = noop;\nvar tip = noop;\nvar generateComponentTrace = (noop); // work around flow check\nvar formatComponentName = (noop);\n\nif (process.env.NODE_ENV !== 'production') {\n  var hasConsole = typeof console !== 'undefined';\n  var classifyRE = /(?:^|[-_])(\\w)/g;\n  var classify = function (str) { return str\n    .replace(classifyRE, function (c) { return c.toUpperCase(); })\n    .replace(/[-_]/g, ''); };\n\n  warn = function (msg, vm) {\n    var trace = vm ? generateComponentTrace(vm) : '';\n\n    if (config.warnHandler) {\n      config.warnHandler.call(null, msg, vm, trace);\n    } else if (hasConsole && (!config.silent)) {\n      console.error((\"[Vue warn]: \" + msg + trace));\n    }\n  };\n\n  tip = function (msg, vm) {\n    if (hasConsole && (!config.silent)) {\n      console.warn(\"[Vue tip]: \" + msg + (\n        vm ? generateComponentTrace(vm) : ''\n      ));\n    }\n  };\n\n  formatComponentName = function (vm, includeFile) {\n    if (vm.$root === vm) {\n      if (vm.$options && vm.$options.__file) { // fixed by xxxxxx\n        return ('') + vm.$options.__file\n      }\n      return '<Root>'\n    }\n    var options = typeof vm === 'function' && vm.cid != null\n      ? vm.options\n      : vm._isVue\n        ? vm.$options || vm.constructor.options\n        : vm;\n    var name = options.name || options._componentTag;\n    var file = options.__file;\n    if (!name && file) {\n      var match = file.match(/([^/\\\\]+)\\.vue$/);\n      name = match && match[1];\n    }\n\n    return (\n      (name ? (\"<\" + (classify(name)) + \">\") : \"<Anonymous>\") +\n      (file && includeFile !== false ? (\" at \" + file) : '')\n    )\n  };\n\n  var repeat = function (str, n) {\n    var res = '';\n    while (n) {\n      if (n % 2 === 1) { res += str; }\n      if (n > 1) { str += str; }\n      n >>= 1;\n    }\n    return res\n  };\n\n  generateComponentTrace = function (vm) {\n    if (vm._isVue && vm.$parent) {\n      var tree = [];\n      var currentRecursiveSequence = 0;\n      while (vm && vm.$options.name !== 'PageBody') {\n        if (tree.length > 0) {\n          var last = tree[tree.length - 1];\n          if (last.constructor === vm.constructor) {\n            currentRecursiveSequence++;\n            vm = vm.$parent;\n            continue\n          } else if (currentRecursiveSequence > 0) {\n            tree[tree.length - 1] = [last, currentRecursiveSequence];\n            currentRecursiveSequence = 0;\n          }\n        }\n        !vm.$options.isReserved && tree.push(vm);\n        vm = vm.$parent;\n      }\n      return '\\n\\nfound in\\n\\n' + tree\n        .map(function (vm, i) { return (\"\" + (i === 0 ? '---> ' : repeat(' ', 5 + i * 2)) + (Array.isArray(vm)\n            ? ((formatComponentName(vm[0])) + \"... (\" + (vm[1]) + \" recursive calls)\")\n            : formatComponentName(vm))); })\n        .join('\\n')\n    } else {\n      return (\"\\n\\n(found in \" + (formatComponentName(vm)) + \")\")\n    }\n  };\n}\n\n/*  */\n\nvar uid = 0;\n\n/**\n * A dep is an observable that can have multiple\n * directives subscribing to it.\n */\nvar Dep = function Dep () {\n  this.id = uid++;\n  this.subs = [];\n};\n\nDep.prototype.addSub = function addSub (sub) {\n  this.subs.push(sub);\n};\n\nDep.prototype.removeSub = function removeSub (sub) {\n  remove(this.subs, sub);\n};\n\nDep.prototype.depend = function depend () {\n  if (Dep.SharedObject.target) {\n    Dep.SharedObject.target.addDep(this);\n  }\n};\n\nDep.prototype.notify = function notify () {\n  // stabilize the subscriber list first\n  var subs = this.subs.slice();\n  if (process.env.NODE_ENV !== 'production' && !config.async) {\n    // subs aren't sorted in scheduler if not running async\n    // we need to sort them now to make sure they fire in correct\n    // order\n    subs.sort(function (a, b) { return a.id - b.id; });\n  }\n  for (var i = 0, l = subs.length; i < l; i++) {\n    subs[i].update();\n  }\n};\n\n// The current target watcher being evaluated.\n// This is globally unique because only one watcher\n// can be evaluated at a time.\n// fixed by xxxxxx (nvue shared vuex)\n/* eslint-disable no-undef */\nDep.SharedObject = {};\nDep.SharedObject.target = null;\nDep.SharedObject.targetStack = [];\n\nfunction pushTarget (target) {\n  Dep.SharedObject.targetStack.push(target);\n  Dep.SharedObject.target = target;\n  Dep.target = target;\n}\n\nfunction popTarget () {\n  Dep.SharedObject.targetStack.pop();\n  Dep.SharedObject.target = Dep.SharedObject.targetStack[Dep.SharedObject.targetStack.length - 1];\n  Dep.target = Dep.SharedObject.target;\n}\n\n/*  */\n\nvar VNode = function VNode (\n  tag,\n  data,\n  children,\n  text,\n  elm,\n  context,\n  componentOptions,\n  asyncFactory\n) {\n  this.tag = tag;\n  this.data = data;\n  this.children = children;\n  this.text = text;\n  this.elm = elm;\n  this.ns = undefined;\n  this.context = context;\n  this.fnContext = undefined;\n  this.fnOptions = undefined;\n  this.fnScopeId = undefined;\n  this.key = data && data.key;\n  this.componentOptions = componentOptions;\n  this.componentInstance = undefined;\n  this.parent = undefined;\n  this.raw = false;\n  this.isStatic = false;\n  this.isRootInsert = true;\n  this.isComment = false;\n  this.isCloned = false;\n  this.isOnce = false;\n  this.asyncFactory = asyncFactory;\n  this.asyncMeta = undefined;\n  this.isAsyncPlaceholder = false;\n};\n\nvar prototypeAccessors = { child: { configurable: true } };\n\n// DEPRECATED: alias for componentInstance for backwards compat.\n/* istanbul ignore next */\nprototypeAccessors.child.get = function () {\n  return this.componentInstance\n};\n\nObject.defineProperties( VNode.prototype, prototypeAccessors );\n\nvar createEmptyVNode = function (text) {\n  if ( text === void 0 ) text = '';\n\n  var node = new VNode();\n  node.text = text;\n  node.isComment = true;\n  return node\n};\n\nfunction createTextVNode (val) {\n  return new VNode(undefined, undefined, undefined, String(val))\n}\n\n// optimized shallow clone\n// used for static nodes and slot nodes because they may be reused across\n// multiple renders, cloning them avoids errors when DOM manipulations rely\n// on their elm reference.\nfunction cloneVNode (vnode) {\n  var cloned = new VNode(\n    vnode.tag,\n    vnode.data,\n    // #7975\n    // clone children array to avoid mutating original in case of cloning\n    // a child.\n    vnode.children && vnode.children.slice(),\n    vnode.text,\n    vnode.elm,\n    vnode.context,\n    vnode.componentOptions,\n    vnode.asyncFactory\n  );\n  cloned.ns = vnode.ns;\n  cloned.isStatic = vnode.isStatic;\n  cloned.key = vnode.key;\n  cloned.isComment = vnode.isComment;\n  cloned.fnContext = vnode.fnContext;\n  cloned.fnOptions = vnode.fnOptions;\n  cloned.fnScopeId = vnode.fnScopeId;\n  cloned.asyncMeta = vnode.asyncMeta;\n  cloned.isCloned = true;\n  return cloned\n}\n\n/*\n * not type checking this file because flow doesn't play well with\n * dynamically accessing methods on Array prototype\n */\n\nvar arrayProto = Array.prototype;\nvar arrayMethods = Object.create(arrayProto);\n\nvar methodsToPatch = [\n  'push',\n  'pop',\n  'shift',\n  'unshift',\n  'splice',\n  'sort',\n  'reverse'\n];\n\n/**\n * Intercept mutating methods and emit events\n */\nmethodsToPatch.forEach(function (method) {\n  // cache original method\n  var original = arrayProto[method];\n  def(arrayMethods, method, function mutator () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    var result = original.apply(this, args);\n    var ob = this.__ob__;\n    var inserted;\n    switch (method) {\n      case 'push':\n      case 'unshift':\n        inserted = args;\n        break\n      case 'splice':\n        inserted = args.slice(2);\n        break\n    }\n    if (inserted) { ob.observeArray(inserted); }\n    // notify change\n    ob.dep.notify();\n    return result\n  });\n});\n\n/*  */\n\nvar arrayKeys = Object.getOwnPropertyNames(arrayMethods);\n\n/**\n * In some cases we may want to disable observation inside a component's\n * update computation.\n */\nvar shouldObserve = true;\n\nfunction toggleObserving (value) {\n  shouldObserve = value;\n}\n\n/**\n * Observer class that is attached to each observed\n * object. Once attached, the observer converts the target\n * object's property keys into getter/setters that\n * collect dependencies and dispatch updates.\n */\nvar Observer = function Observer (value) {\n  this.value = value;\n  this.dep = new Dep();\n  this.vmCount = 0;\n  def(value, '__ob__', this);\n  if (Array.isArray(value)) {\n    if (hasProto) {\n      {// fixed by xxxxxx 微信小程序使用 plugins 之后，数组方法被直接挂载到了数组对象上，需要执行 copyAugment 逻辑\n        if(value.push !== value.__proto__.push){\n          copyAugment(value, arrayMethods, arrayKeys);\n        } else {\n          protoAugment(value, arrayMethods);\n        }\n      }\n    } else {\n      copyAugment(value, arrayMethods, arrayKeys);\n    }\n    this.observeArray(value);\n  } else {\n    this.walk(value);\n  }\n};\n\n/**\n * Walk through all properties and convert them into\n * getter/setters. This method should only be called when\n * value type is Object.\n */\nObserver.prototype.walk = function walk (obj) {\n  var keys = Object.keys(obj);\n  for (var i = 0; i < keys.length; i++) {\n    defineReactive$$1(obj, keys[i]);\n  }\n};\n\n/**\n * Observe a list of Array items.\n */\nObserver.prototype.observeArray = function observeArray (items) {\n  for (var i = 0, l = items.length; i < l; i++) {\n    observe(items[i]);\n  }\n};\n\n// helpers\n\n/**\n * Augment a target Object or Array by intercepting\n * the prototype chain using __proto__\n */\nfunction protoAugment (target, src) {\n  /* eslint-disable no-proto */\n  target.__proto__ = src;\n  /* eslint-enable no-proto */\n}\n\n/**\n * Augment a target Object or Array by defining\n * hidden properties.\n */\n/* istanbul ignore next */\nfunction copyAugment (target, src, keys) {\n  for (var i = 0, l = keys.length; i < l; i++) {\n    var key = keys[i];\n    def(target, key, src[key]);\n  }\n}\n\n/**\n * Attempt to create an observer instance for a value,\n * returns the new observer if successfully observed,\n * or the existing observer if the value already has one.\n */\nfunction observe (value, asRootData) {\n  if (!isObject(value) || value instanceof VNode) {\n    return\n  }\n  var ob;\n  if (hasOwn(value, '__ob__') && value.__ob__ instanceof Observer) {\n    ob = value.__ob__;\n  } else if (\n    shouldObserve &&\n    !isServerRendering() &&\n    (Array.isArray(value) || isPlainObject(value)) &&\n    Object.isExtensible(value) &&\n    !value._isVue &&\n    !value.__v_isMPComponent\n  ) {\n    ob = new Observer(value);\n  }\n  if (asRootData && ob) {\n    ob.vmCount++;\n  }\n  return ob\n}\n\n/**\n * Define a reactive property on an Object.\n */\nfunction defineReactive$$1 (\n  obj,\n  key,\n  val,\n  customSetter,\n  shallow\n) {\n  var dep = new Dep();\n\n  var property = Object.getOwnPropertyDescriptor(obj, key);\n  if (property && property.configurable === false) {\n    return\n  }\n\n  // cater for pre-defined getter/setters\n  var getter = property && property.get;\n  var setter = property && property.set;\n  if ((!getter || setter) && arguments.length === 2) {\n    val = obj[key];\n  }\n\n  var childOb = !shallow && observe(val);\n  Object.defineProperty(obj, key, {\n    enumerable: true,\n    configurable: true,\n    get: function reactiveGetter () {\n      var value = getter ? getter.call(obj) : val;\n      if (Dep.SharedObject.target) { // fixed by xxxxxx\n        dep.depend();\n        if (childOb) {\n          childOb.dep.depend();\n          if (Array.isArray(value)) {\n            dependArray(value);\n          }\n        }\n      }\n      return value\n    },\n    set: function reactiveSetter (newVal) {\n      var value = getter ? getter.call(obj) : val;\n      /* eslint-disable no-self-compare */\n      if (newVal === value || (newVal !== newVal && value !== value)) {\n        return\n      }\n      /* eslint-enable no-self-compare */\n      if (process.env.NODE_ENV !== 'production' && customSetter) {\n        customSetter();\n      }\n      // #7981: for accessor properties without setter\n      if (getter && !setter) { return }\n      if (setter) {\n        setter.call(obj, newVal);\n      } else {\n        val = newVal;\n      }\n      childOb = !shallow && observe(newVal);\n      dep.notify();\n    }\n  });\n}\n\n/**\n * Set a property on an object. Adds the new property and\n * triggers change notification if the property doesn't\n * already exist.\n */\nfunction set (target, key, val) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot set reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.length = Math.max(target.length, key);\n    target.splice(key, 1, val);\n    return val\n  }\n  if (key in target && !(key in Object.prototype)) {\n    target[key] = val;\n    return val\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid adding reactive properties to a Vue instance or its root $data ' +\n      'at runtime - declare it upfront in the data option.'\n    );\n    return val\n  }\n  if (!ob) {\n    target[key] = val;\n    return val\n  }\n  defineReactive$$1(ob.value, key, val);\n  ob.dep.notify();\n  return val\n}\n\n/**\n * Delete a property and trigger change if necessary.\n */\nfunction del (target, key) {\n  if (process.env.NODE_ENV !== 'production' &&\n    (isUndef(target) || isPrimitive(target))\n  ) {\n    warn((\"Cannot delete reactive property on undefined, null, or primitive value: \" + ((target))));\n  }\n  if (Array.isArray(target) && isValidArrayIndex(key)) {\n    target.splice(key, 1);\n    return\n  }\n  var ob = (target).__ob__;\n  if (target._isVue || (ob && ob.vmCount)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      'Avoid deleting properties on a Vue instance or its root $data ' +\n      '- just set it to null.'\n    );\n    return\n  }\n  if (!hasOwn(target, key)) {\n    return\n  }\n  delete target[key];\n  if (!ob) {\n    return\n  }\n  ob.dep.notify();\n}\n\n/**\n * Collect dependencies on array elements when the array is touched, since\n * we cannot intercept array element access like property getters.\n */\nfunction dependArray (value) {\n  for (var e = (void 0), i = 0, l = value.length; i < l; i++) {\n    e = value[i];\n    e && e.__ob__ && e.__ob__.dep.depend();\n    if (Array.isArray(e)) {\n      dependArray(e);\n    }\n  }\n}\n\n/*  */\n\n/**\n * Option overwriting strategies are functions that handle\n * how to merge a parent option value and a child option\n * value into the final value.\n */\nvar strats = config.optionMergeStrategies;\n\n/**\n * Options with restrictions\n */\nif (process.env.NODE_ENV !== 'production') {\n  strats.el = strats.propsData = function (parent, child, vm, key) {\n    if (!vm) {\n      warn(\n        \"option \\\"\" + key + \"\\\" can only be used during instance \" +\n        'creation with the `new` keyword.'\n      );\n    }\n    return defaultStrat(parent, child)\n  };\n}\n\n/**\n * Helper that recursively merges two data objects together.\n */\nfunction mergeData (to, from) {\n  if (!from) { return to }\n  var key, toVal, fromVal;\n\n  var keys = hasSymbol\n    ? Reflect.ownKeys(from)\n    : Object.keys(from);\n\n  for (var i = 0; i < keys.length; i++) {\n    key = keys[i];\n    // in case the object is already observed...\n    if (key === '__ob__') { continue }\n    toVal = to[key];\n    fromVal = from[key];\n    if (!hasOwn(to, key)) {\n      set(to, key, fromVal);\n    } else if (\n      toVal !== fromVal &&\n      isPlainObject(toVal) &&\n      isPlainObject(fromVal)\n    ) {\n      mergeData(toVal, fromVal);\n    }\n  }\n  return to\n}\n\n/**\n * Data\n */\nfunction mergeDataOrFn (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    // in a Vue.extend merge, both should be functions\n    if (!childVal) {\n      return parentVal\n    }\n    if (!parentVal) {\n      return childVal\n    }\n    // when parentVal & childVal are both present,\n    // we need to return a function that returns the\n    // merged result of both functions... no need to\n    // check if parentVal is a function here because\n    // it has to be a function to pass previous merges.\n    return function mergedDataFn () {\n      return mergeData(\n        typeof childVal === 'function' ? childVal.call(this, this) : childVal,\n        typeof parentVal === 'function' ? parentVal.call(this, this) : parentVal\n      )\n    }\n  } else {\n    return function mergedInstanceDataFn () {\n      // instance merge\n      var instanceData = typeof childVal === 'function'\n        ? childVal.call(vm, vm)\n        : childVal;\n      var defaultData = typeof parentVal === 'function'\n        ? parentVal.call(vm, vm)\n        : parentVal;\n      if (instanceData) {\n        return mergeData(instanceData, defaultData)\n      } else {\n        return defaultData\n      }\n    }\n  }\n}\n\nstrats.data = function (\n  parentVal,\n  childVal,\n  vm\n) {\n  if (!vm) {\n    if (childVal && typeof childVal !== 'function') {\n      process.env.NODE_ENV !== 'production' && warn(\n        'The \"data\" option should be a function ' +\n        'that returns a per-instance value in component ' +\n        'definitions.',\n        vm\n      );\n\n      return parentVal\n    }\n    return mergeDataOrFn(parentVal, childVal)\n  }\n\n  return mergeDataOrFn(parentVal, childVal, vm)\n};\n\n/**\n * Hooks and props are merged as arrays.\n */\nfunction mergeHook (\n  parentVal,\n  childVal\n) {\n  var res = childVal\n    ? parentVal\n      ? parentVal.concat(childVal)\n      : Array.isArray(childVal)\n        ? childVal\n        : [childVal]\n    : parentVal;\n  return res\n    ? dedupeHooks(res)\n    : res\n}\n\nfunction dedupeHooks (hooks) {\n  var res = [];\n  for (var i = 0; i < hooks.length; i++) {\n    if (res.indexOf(hooks[i]) === -1) {\n      res.push(hooks[i]);\n    }\n  }\n  return res\n}\n\nLIFECYCLE_HOOKS.forEach(function (hook) {\n  strats[hook] = mergeHook;\n});\n\n/**\n * Assets\n *\n * When a vm is present (instance creation), we need to do\n * a three-way merge between constructor options, instance\n * options and parent options.\n */\nfunction mergeAssets (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  var res = Object.create(parentVal || null);\n  if (childVal) {\n    process.env.NODE_ENV !== 'production' && assertObjectType(key, childVal, vm);\n    return extend(res, childVal)\n  } else {\n    return res\n  }\n}\n\nASSET_TYPES.forEach(function (type) {\n  strats[type + 's'] = mergeAssets;\n});\n\n/**\n * Watchers.\n *\n * Watchers hashes should not overwrite one\n * another, so we merge them as arrays.\n */\nstrats.watch = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  // work around Firefox's Object.prototype.watch...\n  if (parentVal === nativeWatch) { parentVal = undefined; }\n  if (childVal === nativeWatch) { childVal = undefined; }\n  /* istanbul ignore if */\n  if (!childVal) { return Object.create(parentVal || null) }\n  if (process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = {};\n  extend(ret, parentVal);\n  for (var key$1 in childVal) {\n    var parent = ret[key$1];\n    var child = childVal[key$1];\n    if (parent && !Array.isArray(parent)) {\n      parent = [parent];\n    }\n    ret[key$1] = parent\n      ? parent.concat(child)\n      : Array.isArray(child) ? child : [child];\n  }\n  return ret\n};\n\n/**\n * Other object hashes.\n */\nstrats.props =\nstrats.methods =\nstrats.inject =\nstrats.computed = function (\n  parentVal,\n  childVal,\n  vm,\n  key\n) {\n  if (childVal && process.env.NODE_ENV !== 'production') {\n    assertObjectType(key, childVal, vm);\n  }\n  if (!parentVal) { return childVal }\n  var ret = Object.create(null);\n  extend(ret, parentVal);\n  if (childVal) { extend(ret, childVal); }\n  return ret\n};\nstrats.provide = mergeDataOrFn;\n\n/**\n * Default strategy.\n */\nvar defaultStrat = function (parentVal, childVal) {\n  return childVal === undefined\n    ? parentVal\n    : childVal\n};\n\n/**\n * Validate component names\n */\nfunction checkComponents (options) {\n  for (var key in options.components) {\n    validateComponentName(key);\n  }\n}\n\nfunction validateComponentName (name) {\n  if (!new RegExp((\"^[a-zA-Z][\\\\-\\\\.0-9_\" + (unicodeRegExp.source) + \"]*$\")).test(name)) {\n    warn(\n      'Invalid component name: \"' + name + '\". Component names ' +\n      'should conform to valid custom element name in html5 specification.'\n    );\n  }\n  if (isBuiltInTag(name) || config.isReservedTag(name)) {\n    warn(\n      'Do not use built-in or reserved HTML elements as component ' +\n      'id: ' + name\n    );\n  }\n}\n\n/**\n * Ensure all props option syntax are normalized into the\n * Object-based format.\n */\nfunction normalizeProps (options, vm) {\n  var props = options.props;\n  if (!props) { return }\n  var res = {};\n  var i, val, name;\n  if (Array.isArray(props)) {\n    i = props.length;\n    while (i--) {\n      val = props[i];\n      if (typeof val === 'string') {\n        name = camelize(val);\n        res[name] = { type: null };\n      } else if (process.env.NODE_ENV !== 'production') {\n        warn('props must be strings when using array syntax.');\n      }\n    }\n  } else if (isPlainObject(props)) {\n    for (var key in props) {\n      val = props[key];\n      name = camelize(key);\n      res[name] = isPlainObject(val)\n        ? val\n        : { type: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"props\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(props)) + \".\",\n      vm\n    );\n  }\n  options.props = res;\n}\n\n/**\n * Normalize all injections into Object-based format\n */\nfunction normalizeInject (options, vm) {\n  var inject = options.inject;\n  if (!inject) { return }\n  var normalized = options.inject = {};\n  if (Array.isArray(inject)) {\n    for (var i = 0; i < inject.length; i++) {\n      normalized[inject[i]] = { from: inject[i] };\n    }\n  } else if (isPlainObject(inject)) {\n    for (var key in inject) {\n      var val = inject[key];\n      normalized[key] = isPlainObject(val)\n        ? extend({ from: key }, val)\n        : { from: val };\n    }\n  } else if (process.env.NODE_ENV !== 'production') {\n    warn(\n      \"Invalid value for option \\\"inject\\\": expected an Array or an Object, \" +\n      \"but got \" + (toRawType(inject)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Normalize raw function directives into object format.\n */\nfunction normalizeDirectives (options) {\n  var dirs = options.directives;\n  if (dirs) {\n    for (var key in dirs) {\n      var def$$1 = dirs[key];\n      if (typeof def$$1 === 'function') {\n        dirs[key] = { bind: def$$1, update: def$$1 };\n      }\n    }\n  }\n}\n\nfunction assertObjectType (name, value, vm) {\n  if (!isPlainObject(value)) {\n    warn(\n      \"Invalid value for option \\\"\" + name + \"\\\": expected an Object, \" +\n      \"but got \" + (toRawType(value)) + \".\",\n      vm\n    );\n  }\n}\n\n/**\n * Merge two option objects into a new one.\n * Core utility used in both instantiation and inheritance.\n */\nfunction mergeOptions (\n  parent,\n  child,\n  vm\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    checkComponents(child);\n  }\n\n  if (typeof child === 'function') {\n    child = child.options;\n  }\n\n  normalizeProps(child, vm);\n  normalizeInject(child, vm);\n  normalizeDirectives(child);\n\n  // Apply extends and mixins on the child options,\n  // but only if it is a raw options object that isn't\n  // the result of another mergeOptions call.\n  // Only merged options has the _base property.\n  if (!child._base) {\n    if (child.extends) {\n      parent = mergeOptions(parent, child.extends, vm);\n    }\n    if (child.mixins) {\n      for (var i = 0, l = child.mixins.length; i < l; i++) {\n        parent = mergeOptions(parent, child.mixins[i], vm);\n      }\n    }\n  }\n\n  var options = {};\n  var key;\n  for (key in parent) {\n    mergeField(key);\n  }\n  for (key in child) {\n    if (!hasOwn(parent, key)) {\n      mergeField(key);\n    }\n  }\n  function mergeField (key) {\n    var strat = strats[key] || defaultStrat;\n    options[key] = strat(parent[key], child[key], vm, key);\n  }\n  return options\n}\n\n/**\n * Resolve an asset.\n * This function is used because child instances need access\n * to assets defined in its ancestor chain.\n */\nfunction resolveAsset (\n  options,\n  type,\n  id,\n  warnMissing\n) {\n  /* istanbul ignore if */\n  if (typeof id !== 'string') {\n    return\n  }\n  var assets = options[type];\n  // check local registration variations first\n  if (hasOwn(assets, id)) { return assets[id] }\n  var camelizedId = camelize(id);\n  if (hasOwn(assets, camelizedId)) { return assets[camelizedId] }\n  var PascalCaseId = capitalize(camelizedId);\n  if (hasOwn(assets, PascalCaseId)) { return assets[PascalCaseId] }\n  // fallback to prototype chain\n  var res = assets[id] || assets[camelizedId] || assets[PascalCaseId];\n  if (process.env.NODE_ENV !== 'production' && warnMissing && !res) {\n    warn(\n      'Failed to resolve ' + type.slice(0, -1) + ': ' + id,\n      options\n    );\n  }\n  return res\n}\n\n/*  */\n\n\n\nfunction validateProp (\n  key,\n  propOptions,\n  propsData,\n  vm\n) {\n  var prop = propOptions[key];\n  var absent = !hasOwn(propsData, key);\n  var value = propsData[key];\n  // boolean casting\n  var booleanIndex = getTypeIndex(Boolean, prop.type);\n  if (booleanIndex > -1) {\n    if (absent && !hasOwn(prop, 'default')) {\n      value = false;\n    } else if (value === '' || value === hyphenate(key)) {\n      // only cast empty string / same name to boolean if\n      // boolean has higher priority\n      var stringIndex = getTypeIndex(String, prop.type);\n      if (stringIndex < 0 || booleanIndex < stringIndex) {\n        value = true;\n      }\n    }\n  }\n  // check default value\n  if (value === undefined) {\n    value = getPropDefaultValue(vm, prop, key);\n    // since the default value is a fresh copy,\n    // make sure to observe it.\n    var prevShouldObserve = shouldObserve;\n    toggleObserving(true);\n    observe(value);\n    toggleObserving(prevShouldObserve);\n  }\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    // skip validation for weex recycle-list child component props\n    !(false)\n  ) {\n    assertProp(prop, key, value, vm, absent);\n  }\n  return value\n}\n\n/**\n * Get the default value of a prop.\n */\nfunction getPropDefaultValue (vm, prop, key) {\n  // no default, return undefined\n  if (!hasOwn(prop, 'default')) {\n    return undefined\n  }\n  var def = prop.default;\n  // warn against non-factory defaults for Object & Array\n  if (process.env.NODE_ENV !== 'production' && isObject(def)) {\n    warn(\n      'Invalid default value for prop \"' + key + '\": ' +\n      'Props with type Object/Array must use a factory function ' +\n      'to return the default value.',\n      vm\n    );\n  }\n  // the raw prop value was also undefined from previous render,\n  // return previous default value to avoid unnecessary watcher trigger\n  if (vm && vm.$options.propsData &&\n    vm.$options.propsData[key] === undefined &&\n    vm._props[key] !== undefined\n  ) {\n    return vm._props[key]\n  }\n  // call factory function for non-Function types\n  // a value is Function if its prototype is function even across different execution context\n  return typeof def === 'function' && getType(prop.type) !== 'Function'\n    ? def.call(vm)\n    : def\n}\n\n/**\n * Assert whether a prop is valid.\n */\nfunction assertProp (\n  prop,\n  name,\n  value,\n  vm,\n  absent\n) {\n  if (prop.required && absent) {\n    warn(\n      'Missing required prop: \"' + name + '\"',\n      vm\n    );\n    return\n  }\n  if (value == null && !prop.required) {\n    return\n  }\n  var type = prop.type;\n  var valid = !type || type === true;\n  var expectedTypes = [];\n  if (type) {\n    if (!Array.isArray(type)) {\n      type = [type];\n    }\n    for (var i = 0; i < type.length && !valid; i++) {\n      var assertedType = assertType(value, type[i]);\n      expectedTypes.push(assertedType.expectedType || '');\n      valid = assertedType.valid;\n    }\n  }\n\n  if (!valid) {\n    warn(\n      getInvalidTypeMessage(name, value, expectedTypes),\n      vm\n    );\n    return\n  }\n  var validator = prop.validator;\n  if (validator) {\n    if (!validator(value)) {\n      warn(\n        'Invalid prop: custom validator check failed for prop \"' + name + '\".',\n        vm\n      );\n    }\n  }\n}\n\nvar simpleCheckRE = /^(String|Number|Boolean|Function|Symbol)$/;\n\nfunction assertType (value, type) {\n  var valid;\n  var expectedType = getType(type);\n  if (simpleCheckRE.test(expectedType)) {\n    var t = typeof value;\n    valid = t === expectedType.toLowerCase();\n    // for primitive wrapper objects\n    if (!valid && t === 'object') {\n      valid = value instanceof type;\n    }\n  } else if (expectedType === 'Object') {\n    valid = isPlainObject(value);\n  } else if (expectedType === 'Array') {\n    valid = Array.isArray(value);\n  } else {\n    valid = value instanceof type;\n  }\n  return {\n    valid: valid,\n    expectedType: expectedType\n  }\n}\n\n/**\n * Use function string name to check built-in types,\n * because a simple equality check will fail when running\n * across different vms / iframes.\n */\nfunction getType (fn) {\n  var match = fn && fn.toString().match(/^\\s*function (\\w+)/);\n  return match ? match[1] : ''\n}\n\nfunction isSameType (a, b) {\n  return getType(a) === getType(b)\n}\n\nfunction getTypeIndex (type, expectedTypes) {\n  if (!Array.isArray(expectedTypes)) {\n    return isSameType(expectedTypes, type) ? 0 : -1\n  }\n  for (var i = 0, len = expectedTypes.length; i < len; i++) {\n    if (isSameType(expectedTypes[i], type)) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction getInvalidTypeMessage (name, value, expectedTypes) {\n  var message = \"Invalid prop: type check failed for prop \\\"\" + name + \"\\\".\" +\n    \" Expected \" + (expectedTypes.map(capitalize).join(', '));\n  var expectedType = expectedTypes[0];\n  var receivedType = toRawType(value);\n  var expectedValue = styleValue(value, expectedType);\n  var receivedValue = styleValue(value, receivedType);\n  // check if we need to specify expected value\n  if (expectedTypes.length === 1 &&\n      isExplicable(expectedType) &&\n      !isBoolean(expectedType, receivedType)) {\n    message += \" with value \" + expectedValue;\n  }\n  message += \", got \" + receivedType + \" \";\n  // check if we need to specify received value\n  if (isExplicable(receivedType)) {\n    message += \"with value \" + receivedValue + \".\";\n  }\n  return message\n}\n\nfunction styleValue (value, type) {\n  if (type === 'String') {\n    return (\"\\\"\" + value + \"\\\"\")\n  } else if (type === 'Number') {\n    return (\"\" + (Number(value)))\n  } else {\n    return (\"\" + value)\n  }\n}\n\nfunction isExplicable (value) {\n  var explicitTypes = ['string', 'number', 'boolean'];\n  return explicitTypes.some(function (elem) { return value.toLowerCase() === elem; })\n}\n\nfunction isBoolean () {\n  var args = [], len = arguments.length;\n  while ( len-- ) args[ len ] = arguments[ len ];\n\n  return args.some(function (elem) { return elem.toLowerCase() === 'boolean'; })\n}\n\n/*  */\n\nfunction handleError (err, vm, info) {\n  // Deactivate deps tracking while processing error handler to avoid possible infinite rendering.\n  // See: https://github.com/vuejs/vuex/issues/1505\n  pushTarget();\n  try {\n    if (vm) {\n      var cur = vm;\n      while ((cur = cur.$parent)) {\n        var hooks = cur.$options.errorCaptured;\n        if (hooks) {\n          for (var i = 0; i < hooks.length; i++) {\n            try {\n              var capture = hooks[i].call(cur, err, vm, info) === false;\n              if (capture) { return }\n            } catch (e) {\n              globalHandleError(e, cur, 'errorCaptured hook');\n            }\n          }\n        }\n      }\n    }\n    globalHandleError(err, vm, info);\n  } finally {\n    popTarget();\n  }\n}\n\nfunction invokeWithErrorHandling (\n  handler,\n  context,\n  args,\n  vm,\n  info\n) {\n  var res;\n  try {\n    res = args ? handler.apply(context, args) : handler.call(context);\n    if (res && !res._isVue && isPromise(res) && !res._handled) {\n      res.catch(function (e) { return handleError(e, vm, info + \" (Promise/async)\"); });\n      // issue #9511\n      // avoid catch triggering multiple times when nested calls\n      res._handled = true;\n    }\n  } catch (e) {\n    handleError(e, vm, info);\n  }\n  return res\n}\n\nfunction globalHandleError (err, vm, info) {\n  if (config.errorHandler) {\n    try {\n      return config.errorHandler.call(null, err, vm, info)\n    } catch (e) {\n      // if the user intentionally throws the original error in the handler,\n      // do not log it twice\n      if (e !== err) {\n        logError(e, null, 'config.errorHandler');\n      }\n    }\n  }\n  logError(err, vm, info);\n}\n\nfunction logError (err, vm, info) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\n  }\n  /* istanbul ignore else */\n  if ((inBrowser || inWeex) && typeof console !== 'undefined') {\n    console.error(err);\n  } else {\n    throw err\n  }\n}\n\n/*  */\n\nvar callbacks = [];\nvar pending = false;\n\nfunction flushCallbacks () {\n  pending = false;\n  var copies = callbacks.slice(0);\n  callbacks.length = 0;\n  for (var i = 0; i < copies.length; i++) {\n    copies[i]();\n  }\n}\n\n// Here we have async deferring wrappers using microtasks.\n// In 2.5 we used (macro) tasks (in combination with microtasks).\n// However, it has subtle problems when state is changed right before repaint\n// (e.g. #6813, out-in transitions).\n// Also, using (macro) tasks in event handler would cause some weird behaviors\n// that cannot be circumvented (e.g. #7109, #7153, #7546, #7834, #8109).\n// So we now use microtasks everywhere, again.\n// A major drawback of this tradeoff is that there are some scenarios\n// where microtasks have too high a priority and fire in between supposedly\n// sequential events (e.g. #4521, #6690, which have workarounds)\n// or even between bubbling of the same event (#6566).\nvar timerFunc;\n\n// The nextTick behavior leverages the microtask queue, which can be accessed\n// via either native Promise.then or MutationObserver.\n// MutationObserver has wider support, however it is seriously bugged in\n// UIWebView in iOS >= 9.3.3 when triggered in touch event handlers. It\n// completely stops working after triggering a few times... so, if native\n// Promise is available, we will use it:\n/* istanbul ignore next, $flow-disable-line */\nif (typeof Promise !== 'undefined' && isNative(Promise)) {\n  var p = Promise.resolve();\n  timerFunc = function () {\n    p.then(flushCallbacks);\n    // In problematic UIWebViews, Promise.then doesn't completely break, but\n    // it can get stuck in a weird state where callbacks are pushed into the\n    // microtask queue but the queue isn't being flushed, until the browser\n    // needs to do some other work, e.g. handle a timer. Therefore we can\n    // \"force\" the microtask queue to be flushed by adding an empty timer.\n    if (isIOS) { setTimeout(noop); }\n  };\n} else if (!isIE && typeof MutationObserver !== 'undefined' && (\n  isNative(MutationObserver) ||\n  // PhantomJS and iOS 7.x\n  MutationObserver.toString() === '[object MutationObserverConstructor]'\n)) {\n  // Use MutationObserver where native Promise is not available,\n  // e.g. PhantomJS, iOS7, Android 4.4\n  // (#6466 MutationObserver is unreliable in IE11)\n  var counter = 1;\n  var observer = new MutationObserver(flushCallbacks);\n  var textNode = document.createTextNode(String(counter));\n  observer.observe(textNode, {\n    characterData: true\n  });\n  timerFunc = function () {\n    counter = (counter + 1) % 2;\n    textNode.data = String(counter);\n  };\n} else if (typeof setImmediate !== 'undefined' && isNative(setImmediate)) {\n  // Fallback to setImmediate.\n  // Technically it leverages the (macro) task queue,\n  // but it is still a better choice than setTimeout.\n  timerFunc = function () {\n    setImmediate(flushCallbacks);\n  };\n} else {\n  // Fallback to setTimeout.\n  timerFunc = function () {\n    setTimeout(flushCallbacks, 0);\n  };\n}\n\nfunction nextTick (cb, ctx) {\n  var _resolve;\n  callbacks.push(function () {\n    if (cb) {\n      try {\n        cb.call(ctx);\n      } catch (e) {\n        handleError(e, ctx, 'nextTick');\n      }\n    } else if (_resolve) {\n      _resolve(ctx);\n    }\n  });\n  if (!pending) {\n    pending = true;\n    timerFunc();\n  }\n  // $flow-disable-line\n  if (!cb && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve) {\n      _resolve = resolve;\n    })\n  }\n}\n\n/*  */\n\n/* not type checking this file because flow doesn't play well with Proxy */\n\nvar initProxy;\n\nif (process.env.NODE_ENV !== 'production') {\n  var allowedGlobals = makeMap(\n    'Infinity,undefined,NaN,isFinite,isNaN,' +\n    'parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,' +\n    'Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,' +\n    'require' // for Webpack/Browserify\n  );\n\n  var warnNonPresent = function (target, key) {\n    warn(\n      \"Property or method \\\"\" + key + \"\\\" is not defined on the instance but \" +\n      'referenced during render. Make sure that this property is reactive, ' +\n      'either in the data option, or for class-based components, by ' +\n      'initializing the property. ' +\n      'See: https://vuejs.org/v2/guide/reactivity.html#Declaring-Reactive-Properties.',\n      target\n    );\n  };\n\n  var warnReservedPrefix = function (target, key) {\n    warn(\n      \"Property \\\"\" + key + \"\\\" must be accessed with \\\"$data.\" + key + \"\\\" because \" +\n      'properties starting with \"$\" or \"_\" are not proxied in the Vue instance to ' +\n      'prevent conflicts with Vue internals. ' +\n      'See: https://vuejs.org/v2/api/#data',\n      target\n    );\n  };\n\n  var hasProxy =\n    typeof Proxy !== 'undefined' && isNative(Proxy);\n\n  if (hasProxy) {\n    var isBuiltInModifier = makeMap('stop,prevent,self,ctrl,shift,alt,meta,exact');\n    config.keyCodes = new Proxy(config.keyCodes, {\n      set: function set (target, key, value) {\n        if (isBuiltInModifier(key)) {\n          warn((\"Avoid overwriting built-in modifier in config.keyCodes: .\" + key));\n          return false\n        } else {\n          target[key] = value;\n          return true\n        }\n      }\n    });\n  }\n\n  var hasHandler = {\n    has: function has (target, key) {\n      var has = key in target;\n      var isAllowed = allowedGlobals(key) ||\n        (typeof key === 'string' && key.charAt(0) === '_' && !(key in target.$data));\n      if (!has && !isAllowed) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return has || !isAllowed\n    }\n  };\n\n  var getHandler = {\n    get: function get (target, key) {\n      if (typeof key === 'string' && !(key in target)) {\n        if (key in target.$data) { warnReservedPrefix(target, key); }\n        else { warnNonPresent(target, key); }\n      }\n      return target[key]\n    }\n  };\n\n  initProxy = function initProxy (vm) {\n    if (hasProxy) {\n      // determine which proxy handler to use\n      var options = vm.$options;\n      var handlers = options.render && options.render._withStripped\n        ? getHandler\n        : hasHandler;\n      vm._renderProxy = new Proxy(vm, handlers);\n    } else {\n      vm._renderProxy = vm;\n    }\n  };\n}\n\n/*  */\n\nvar seenObjects = new _Set();\n\n/**\n * Recursively traverse an object to evoke all converted\n * getters, so that every nested property inside the object\n * is collected as a \"deep\" dependency.\n */\nfunction traverse (val) {\n  _traverse(val, seenObjects);\n  seenObjects.clear();\n}\n\nfunction _traverse (val, seen) {\n  var i, keys;\n  var isA = Array.isArray(val);\n  if ((!isA && !isObject(val)) || Object.isFrozen(val) || val instanceof VNode) {\n    return\n  }\n  if (val.__ob__) {\n    var depId = val.__ob__.dep.id;\n    if (seen.has(depId)) {\n      return\n    }\n    seen.add(depId);\n  }\n  if (isA) {\n    i = val.length;\n    while (i--) { _traverse(val[i], seen); }\n  } else {\n    keys = Object.keys(val);\n    i = keys.length;\n    while (i--) { _traverse(val[keys[i]], seen); }\n  }\n}\n\nvar mark;\nvar measure;\n\nif (process.env.NODE_ENV !== 'production') {\n  var perf = inBrowser && window.performance;\n  /* istanbul ignore if */\n  if (\n    perf &&\n    perf.mark &&\n    perf.measure &&\n    perf.clearMarks &&\n    perf.clearMeasures\n  ) {\n    mark = function (tag) { return perf.mark(tag); };\n    measure = function (name, startTag, endTag) {\n      perf.measure(name, startTag, endTag);\n      perf.clearMarks(startTag);\n      perf.clearMarks(endTag);\n      // perf.clearMeasures(name)\n    };\n  }\n}\n\n/*  */\n\nvar normalizeEvent = cached(function (name) {\n  var passive = name.charAt(0) === '&';\n  name = passive ? name.slice(1) : name;\n  var once$$1 = name.charAt(0) === '~'; // Prefixed last, checked first\n  name = once$$1 ? name.slice(1) : name;\n  var capture = name.charAt(0) === '!';\n  name = capture ? name.slice(1) : name;\n  return {\n    name: name,\n    once: once$$1,\n    capture: capture,\n    passive: passive\n  }\n});\n\nfunction createFnInvoker (fns, vm) {\n  function invoker () {\n    var arguments$1 = arguments;\n\n    var fns = invoker.fns;\n    if (Array.isArray(fns)) {\n      var cloned = fns.slice();\n      for (var i = 0; i < cloned.length; i++) {\n        invokeWithErrorHandling(cloned[i], null, arguments$1, vm, \"v-on handler\");\n      }\n    } else {\n      // return handler return value for single handlers\n      return invokeWithErrorHandling(fns, null, arguments, vm, \"v-on handler\")\n    }\n  }\n  invoker.fns = fns;\n  return invoker\n}\n\nfunction updateListeners (\n  on,\n  oldOn,\n  add,\n  remove$$1,\n  createOnceHandler,\n  vm\n) {\n  var name, def$$1, cur, old, event;\n  for (name in on) {\n    def$$1 = cur = on[name];\n    old = oldOn[name];\n    event = normalizeEvent(name);\n    if (isUndef(cur)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Invalid handler for event \\\"\" + (event.name) + \"\\\": got \" + String(cur),\n        vm\n      );\n    } else if (isUndef(old)) {\n      if (isUndef(cur.fns)) {\n        cur = on[name] = createFnInvoker(cur, vm);\n      }\n      if (isTrue(event.once)) {\n        cur = on[name] = createOnceHandler(event.name, cur, event.capture);\n      }\n      add(event.name, cur, event.capture, event.passive, event.params);\n    } else if (cur !== old) {\n      old.fns = cur;\n      on[name] = old;\n    }\n  }\n  for (name in oldOn) {\n    if (isUndef(on[name])) {\n      event = normalizeEvent(name);\n      remove$$1(event.name, oldOn[name], event.capture);\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\n// fixed by xxxxxx (mp properties)\r\nfunction extractPropertiesFromVNodeData(data, Ctor, res, context) {\r\n  var propOptions = Ctor.options.mpOptions && Ctor.options.mpOptions.properties;\r\n  if (isUndef(propOptions)) {\r\n    return res\r\n  }\n  var externalClasses = Ctor.options.mpOptions.externalClasses || [];\r\n  var attrs = data.attrs;\n  var props = data.props;\r\n  if (isDef(attrs) || isDef(props)) {\r\n    for (var key in propOptions) {\r\n      var altKey = hyphenate(key);\n      var result = checkProp(res, props, key, altKey, true) ||\n          checkProp(res, attrs, key, altKey, false);\n      // externalClass\n      if (\n        result &&\n        res[key] &&\n        externalClasses.indexOf(altKey) !== -1 &&\n        context[camelize(res[key])]\n      ) {\n        // 赋值 externalClass 真正的值(模板里 externalClass 的值可能是字符串)\n        res[key] = context[camelize(res[key])];\n      }\r\n    }\r\n  }\r\n  return res\r\n}\n\nfunction extractPropsFromVNodeData (\n  data,\n  Ctor,\n  tag,\n  context// fixed by xxxxxx\n) {\n  // we are only extracting raw values here.\n  // validation and default values are handled in the child\n  // component itself.\n  var propOptions = Ctor.options.props;\n  if (isUndef(propOptions)) {\n    // fixed by xxxxxx\n    return extractPropertiesFromVNodeData(data, Ctor, {}, context)\n  }\n  var res = {};\n  var attrs = data.attrs;\n  var props = data.props;\n  if (isDef(attrs) || isDef(props)) {\n    for (var key in propOptions) {\n      var altKey = hyphenate(key);\n      if (process.env.NODE_ENV !== 'production') {\n        var keyInLowerCase = key.toLowerCase();\n        if (\n          key !== keyInLowerCase &&\n          attrs && hasOwn(attrs, keyInLowerCase)\n        ) {\n          tip(\n            \"Prop \\\"\" + keyInLowerCase + \"\\\" is passed to component \" +\n            (formatComponentName(tag || Ctor)) + \", but the declared prop name is\" +\n            \" \\\"\" + key + \"\\\". \" +\n            \"Note that HTML attributes are case-insensitive and camelCased \" +\n            \"props need to use their kebab-case equivalents when using in-DOM \" +\n            \"templates. You should probably use \\\"\" + altKey + \"\\\" instead of \\\"\" + key + \"\\\".\"\n          );\n        }\n      }\n      checkProp(res, props, key, altKey, true) ||\n      checkProp(res, attrs, key, altKey, false);\n    }\n  }\n  // fixed by xxxxxx\n  return extractPropertiesFromVNodeData(data, Ctor, res, context)\n}\n\nfunction checkProp (\n  res,\n  hash,\n  key,\n  altKey,\n  preserve\n) {\n  if (isDef(hash)) {\n    if (hasOwn(hash, key)) {\n      res[key] = hash[key];\n      if (!preserve) {\n        delete hash[key];\n      }\n      return true\n    } else if (hasOwn(hash, altKey)) {\n      res[key] = hash[altKey];\n      if (!preserve) {\n        delete hash[altKey];\n      }\n      return true\n    }\n  }\n  return false\n}\n\n/*  */\n\n// The template compiler attempts to minimize the need for normalization by\n// statically analyzing the template at compile time.\n//\n// For plain HTML markup, normalization can be completely skipped because the\n// generated render function is guaranteed to return Array<VNode>. There are\n// two cases where extra normalization is needed:\n\n// 1. When the children contains components - because a functional component\n// may return an Array instead of a single root. In this case, just a simple\n// normalization is needed - if any child is an Array, we flatten the whole\n// thing with Array.prototype.concat. It is guaranteed to be only 1-level deep\n// because functional components already normalize their own children.\nfunction simpleNormalizeChildren (children) {\n  for (var i = 0; i < children.length; i++) {\n    if (Array.isArray(children[i])) {\n      return Array.prototype.concat.apply([], children)\n    }\n  }\n  return children\n}\n\n// 2. When the children contains constructs that always generated nested Arrays,\n// e.g. <template>, <slot>, v-for, or when the children is provided by user\n// with hand-written render functions / JSX. In such cases a full normalization\n// is needed to cater to all possible types of children values.\nfunction normalizeChildren (children) {\n  return isPrimitive(children)\n    ? [createTextVNode(children)]\n    : Array.isArray(children)\n      ? normalizeArrayChildren(children)\n      : undefined\n}\n\nfunction isTextNode (node) {\n  return isDef(node) && isDef(node.text) && isFalse(node.isComment)\n}\n\nfunction normalizeArrayChildren (children, nestedIndex) {\n  var res = [];\n  var i, c, lastIndex, last;\n  for (i = 0; i < children.length; i++) {\n    c = children[i];\n    if (isUndef(c) || typeof c === 'boolean') { continue }\n    lastIndex = res.length - 1;\n    last = res[lastIndex];\n    //  nested\n    if (Array.isArray(c)) {\n      if (c.length > 0) {\n        c = normalizeArrayChildren(c, ((nestedIndex || '') + \"_\" + i));\n        // merge adjacent text nodes\n        if (isTextNode(c[0]) && isTextNode(last)) {\n          res[lastIndex] = createTextVNode(last.text + (c[0]).text);\n          c.shift();\n        }\n        res.push.apply(res, c);\n      }\n    } else if (isPrimitive(c)) {\n      if (isTextNode(last)) {\n        // merge adjacent text nodes\n        // this is necessary for SSR hydration because text nodes are\n        // essentially merged when rendered to HTML strings\n        res[lastIndex] = createTextVNode(last.text + c);\n      } else if (c !== '') {\n        // convert primitive to vnode\n        res.push(createTextVNode(c));\n      }\n    } else {\n      if (isTextNode(c) && isTextNode(last)) {\n        // merge adjacent text nodes\n        res[lastIndex] = createTextVNode(last.text + c.text);\n      } else {\n        // default key for nested array children (likely generated by v-for)\n        if (isTrue(children._isVList) &&\n          isDef(c.tag) &&\n          isUndef(c.key) &&\n          isDef(nestedIndex)) {\n          c.key = \"__vlist\" + nestedIndex + \"_\" + i + \"__\";\n        }\n        res.push(c);\n      }\n    }\n  }\n  return res\n}\n\n/*  */\n\nfunction initProvide (vm) {\n  var provide = vm.$options.provide;\n  if (provide) {\n    vm._provided = typeof provide === 'function'\n      ? provide.call(vm)\n      : provide;\n  }\n}\n\nfunction initInjections (vm) {\n  var result = resolveInject(vm.$options.inject, vm);\n  if (result) {\n    toggleObserving(false);\n    Object.keys(result).forEach(function (key) {\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production') {\n        defineReactive$$1(vm, key, result[key], function () {\n          warn(\n            \"Avoid mutating an injected value directly since the changes will be \" +\n            \"overwritten whenever the provided component re-renders. \" +\n            \"injection being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        });\n      } else {\n        defineReactive$$1(vm, key, result[key]);\n      }\n    });\n    toggleObserving(true);\n  }\n}\n\nfunction resolveInject (inject, vm) {\n  if (inject) {\n    // inject is :any because flow is not smart enough to figure out cached\n    var result = Object.create(null);\n    var keys = hasSymbol\n      ? Reflect.ownKeys(inject)\n      : Object.keys(inject);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n      // #6574 in case the inject object is observed...\n      if (key === '__ob__') { continue }\n      var provideKey = inject[key].from;\n      var source = vm;\n      while (source) {\n        if (source._provided && hasOwn(source._provided, provideKey)) {\n          result[key] = source._provided[provideKey];\n          break\n        }\n        source = source.$parent;\n      }\n      if (!source) {\n        if ('default' in inject[key]) {\n          var provideDefault = inject[key].default;\n          result[key] = typeof provideDefault === 'function'\n            ? provideDefault.call(vm)\n            : provideDefault;\n        } else if (process.env.NODE_ENV !== 'production') {\n          warn((\"Injection \\\"\" + key + \"\\\" not found\"), vm);\n        }\n      }\n    }\n    return result\n  }\n}\n\n/*  */\n\n\n\n/**\n * Runtime helper for resolving raw children VNodes into a slot object.\n */\nfunction resolveSlots (\n  children,\n  context\n) {\n  if (!children || !children.length) {\n    return {}\n  }\n  var slots = {};\n  for (var i = 0, l = children.length; i < l; i++) {\n    var child = children[i];\n    var data = child.data;\n    // remove slot attribute if the node is resolved as a Vue slot node\n    if (data && data.attrs && data.attrs.slot) {\n      delete data.attrs.slot;\n    }\n    // named slots should only be respected if the vnode was rendered in the\n    // same context.\n    if ((child.context === context || child.fnContext === context) &&\n      data && data.slot != null\n    ) {\n      var name = data.slot;\n      var slot = (slots[name] || (slots[name] = []));\n      if (child.tag === 'template') {\n        slot.push.apply(slot, child.children || []);\n      } else {\n        slot.push(child);\n      }\n    } else {\n      // fixed by xxxxxx 临时 hack 掉 uni-app 中的异步 name slot page\n      if(child.asyncMeta && child.asyncMeta.data && child.asyncMeta.data.slot === 'page'){\n        (slots['page'] || (slots['page'] = [])).push(child);\n      }else{\n        (slots.default || (slots.default = [])).push(child);\n      }\n    }\n  }\n  // ignore slots that contains only whitespace\n  for (var name$1 in slots) {\n    if (slots[name$1].every(isWhitespace)) {\n      delete slots[name$1];\n    }\n  }\n  return slots\n}\n\nfunction isWhitespace (node) {\n  return (node.isComment && !node.asyncFactory) || node.text === ' '\n}\n\n/*  */\n\nfunction normalizeScopedSlots (\n  slots,\n  normalSlots,\n  prevSlots\n) {\n  var res;\n  var hasNormalSlots = Object.keys(normalSlots).length > 0;\n  var isStable = slots ? !!slots.$stable : !hasNormalSlots;\n  var key = slots && slots.$key;\n  if (!slots) {\n    res = {};\n  } else if (slots._normalized) {\n    // fast path 1: child component re-render only, parent did not change\n    return slots._normalized\n  } else if (\n    isStable &&\n    prevSlots &&\n    prevSlots !== emptyObject &&\n    key === prevSlots.$key &&\n    !hasNormalSlots &&\n    !prevSlots.$hasNormal\n  ) {\n    // fast path 2: stable scoped slots w/ no normal slots to proxy,\n    // only need to normalize once\n    return prevSlots\n  } else {\n    res = {};\n    for (var key$1 in slots) {\n      if (slots[key$1] && key$1[0] !== '$') {\n        res[key$1] = normalizeScopedSlot(normalSlots, key$1, slots[key$1]);\n      }\n    }\n  }\n  // expose normal slots on scopedSlots\n  for (var key$2 in normalSlots) {\n    if (!(key$2 in res)) {\n      res[key$2] = proxyNormalSlot(normalSlots, key$2);\n    }\n  }\n  // avoriaz seems to mock a non-extensible $scopedSlots object\n  // and when that is passed down this would cause an error\n  if (slots && Object.isExtensible(slots)) {\n    (slots)._normalized = res;\n  }\n  def(res, '$stable', isStable);\n  def(res, '$key', key);\n  def(res, '$hasNormal', hasNormalSlots);\n  return res\n}\n\nfunction normalizeScopedSlot(normalSlots, key, fn) {\n  var normalized = function () {\n    var res = arguments.length ? fn.apply(null, arguments) : fn({});\n    res = res && typeof res === 'object' && !Array.isArray(res)\n      ? [res] // single vnode\n      : normalizeChildren(res);\n    return res && (\n      res.length === 0 ||\n      (res.length === 1 && res[0].isComment) // #9658\n    ) ? undefined\n      : res\n  };\n  // this is a slot using the new v-slot syntax without scope. although it is\n  // compiled as a scoped slot, render fn users would expect it to be present\n  // on this.$slots because the usage is semantically a normal slot.\n  if (fn.proxy) {\n    Object.defineProperty(normalSlots, key, {\n      get: normalized,\n      enumerable: true,\n      configurable: true\n    });\n  }\n  return normalized\n}\n\nfunction proxyNormalSlot(slots, key) {\n  return function () { return slots[key]; }\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering v-for lists.\n */\nfunction renderList (\n  val,\n  render\n) {\n  var ret, i, l, keys, key;\n  if (Array.isArray(val) || typeof val === 'string') {\n    ret = new Array(val.length);\n    for (i = 0, l = val.length; i < l; i++) {\n      ret[i] = render(val[i], i, i, i); // fixed by xxxxxx\n    }\n  } else if (typeof val === 'number') {\n    ret = new Array(val);\n    for (i = 0; i < val; i++) {\n      ret[i] = render(i + 1, i, i, i); // fixed by xxxxxx\n    }\n  } else if (isObject(val)) {\n    if (hasSymbol && val[Symbol.iterator]) {\n      ret = [];\n      var iterator = val[Symbol.iterator]();\n      var result = iterator.next();\n      while (!result.done) {\n        ret.push(render(result.value, ret.length, i, i++)); // fixed by xxxxxx\n        result = iterator.next();\n      }\n    } else {\n      keys = Object.keys(val);\n      ret = new Array(keys.length);\n      for (i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        ret[i] = render(val[key], key, i, i); // fixed by xxxxxx\n      }\n    }\n  }\n  if (!isDef(ret)) {\n    ret = [];\n  }\n  (ret)._isVList = true;\n  return ret\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering <slot>\n */\nfunction renderSlot (\n  name,\n  fallback,\n  props,\n  bindObject\n) {\n  var scopedSlotFn = this.$scopedSlots[name];\n  var nodes;\n  if (scopedSlotFn) { // scoped slot\n    props = props || {};\n    if (bindObject) {\n      if (process.env.NODE_ENV !== 'production' && !isObject(bindObject)) {\n        warn(\n          'slot v-bind without argument expects an Object',\n          this\n        );\n      }\n      props = extend(extend({}, bindObject), props);\n    }\n    // fixed by xxxxxx app-plus scopedSlot\n    nodes = scopedSlotFn(props, this, props._i) || fallback;\n  } else {\n    nodes = this.$slots[name] || fallback;\n  }\n\n  var target = props && props.slot;\n  if (target) {\n    return this.$createElement('template', { slot: target }, nodes)\n  } else {\n    return nodes\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for resolving filters\n */\nfunction resolveFilter (id) {\n  return resolveAsset(this.$options, 'filters', id, true) || identity\n}\n\n/*  */\n\nfunction isKeyNotMatch (expect, actual) {\n  if (Array.isArray(expect)) {\n    return expect.indexOf(actual) === -1\n  } else {\n    return expect !== actual\n  }\n}\n\n/**\n * Runtime helper for checking keyCodes from config.\n * exposed as Vue.prototype._k\n * passing in eventKeyName as last argument separately for backwards compat\n */\nfunction checkKeyCodes (\n  eventKeyCode,\n  key,\n  builtInKeyCode,\n  eventKeyName,\n  builtInKeyName\n) {\n  var mappedKeyCode = config.keyCodes[key] || builtInKeyCode;\n  if (builtInKeyName && eventKeyName && !config.keyCodes[key]) {\n    return isKeyNotMatch(builtInKeyName, eventKeyName)\n  } else if (mappedKeyCode) {\n    return isKeyNotMatch(mappedKeyCode, eventKeyCode)\n  } else if (eventKeyName) {\n    return hyphenate(eventKeyName) !== key\n  }\n}\n\n/*  */\n\n/**\n * Runtime helper for merging v-bind=\"object\" into a VNode's data.\n */\nfunction bindObjectProps (\n  data,\n  tag,\n  value,\n  asProp,\n  isSync\n) {\n  if (value) {\n    if (!isObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-bind without argument expects an Object or Array value',\n        this\n      );\n    } else {\n      if (Array.isArray(value)) {\n        value = toObject(value);\n      }\n      var hash;\n      var loop = function ( key ) {\n        if (\n          key === 'class' ||\n          key === 'style' ||\n          isReservedAttribute(key)\n        ) {\n          hash = data;\n        } else {\n          var type = data.attrs && data.attrs.type;\n          hash = asProp || config.mustUseProp(tag, type, key)\n            ? data.domProps || (data.domProps = {})\n            : data.attrs || (data.attrs = {});\n        }\n        var camelizedKey = camelize(key);\n        var hyphenatedKey = hyphenate(key);\n        if (!(camelizedKey in hash) && !(hyphenatedKey in hash)) {\n          hash[key] = value[key];\n\n          if (isSync) {\n            var on = data.on || (data.on = {});\n            on[(\"update:\" + key)] = function ($event) {\n              value[key] = $event;\n            };\n          }\n        }\n      };\n\n      for (var key in value) loop( key );\n    }\n  }\n  return data\n}\n\n/*  */\n\n/**\n * Runtime helper for rendering static trees.\n */\nfunction renderStatic (\n  index,\n  isInFor\n) {\n  var cached = this._staticTrees || (this._staticTrees = []);\n  var tree = cached[index];\n  // if has already-rendered static tree and not inside v-for,\n  // we can reuse the same tree.\n  if (tree && !isInFor) {\n    return tree\n  }\n  // otherwise, render a fresh tree.\n  tree = cached[index] = this.$options.staticRenderFns[index].call(\n    this._renderProxy,\n    null,\n    this // for render fns generated for functional component templates\n  );\n  markStatic(tree, (\"__static__\" + index), false);\n  return tree\n}\n\n/**\n * Runtime helper for v-once.\n * Effectively it means marking the node as static with a unique key.\n */\nfunction markOnce (\n  tree,\n  index,\n  key\n) {\n  markStatic(tree, (\"__once__\" + index + (key ? (\"_\" + key) : \"\")), true);\n  return tree\n}\n\nfunction markStatic (\n  tree,\n  key,\n  isOnce\n) {\n  if (Array.isArray(tree)) {\n    for (var i = 0; i < tree.length; i++) {\n      if (tree[i] && typeof tree[i] !== 'string') {\n        markStaticNode(tree[i], (key + \"_\" + i), isOnce);\n      }\n    }\n  } else {\n    markStaticNode(tree, key, isOnce);\n  }\n}\n\nfunction markStaticNode (node, key, isOnce) {\n  node.isStatic = true;\n  node.key = key;\n  node.isOnce = isOnce;\n}\n\n/*  */\n\nfunction bindObjectListeners (data, value) {\n  if (value) {\n    if (!isPlainObject(value)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        'v-on without argument expects an Object value',\n        this\n      );\n    } else {\n      var on = data.on = data.on ? extend({}, data.on) : {};\n      for (var key in value) {\n        var existing = on[key];\n        var ours = value[key];\n        on[key] = existing ? [].concat(existing, ours) : ours;\n      }\n    }\n  }\n  return data\n}\n\n/*  */\n\nfunction resolveScopedSlots (\n  fns, // see flow/vnode\n  res,\n  // the following are added in 2.6\n  hasDynamicKeys,\n  contentHashKey\n) {\n  res = res || { $stable: !hasDynamicKeys };\n  for (var i = 0; i < fns.length; i++) {\n    var slot = fns[i];\n    if (Array.isArray(slot)) {\n      resolveScopedSlots(slot, res, hasDynamicKeys);\n    } else if (slot) {\n      // marker for reverse proxying v-slot without scope on this.$slots\n      if (slot.proxy) {\n        slot.fn.proxy = true;\n      }\n      res[slot.key] = slot.fn;\n    }\n  }\n  if (contentHashKey) {\n    (res).$key = contentHashKey;\n  }\n  return res\n}\n\n/*  */\n\nfunction bindDynamicKeys (baseObj, values) {\n  for (var i = 0; i < values.length; i += 2) {\n    var key = values[i];\n    if (typeof key === 'string' && key) {\n      baseObj[values[i]] = values[i + 1];\n    } else if (process.env.NODE_ENV !== 'production' && key !== '' && key !== null) {\n      // null is a special value for explicitly removing a binding\n      warn(\n        (\"Invalid value for dynamic directive argument (expected string or null): \" + key),\n        this\n      );\n    }\n  }\n  return baseObj\n}\n\n// helper to dynamically append modifier runtime markers to event names.\n// ensure only append when value is already string, otherwise it will be cast\n// to string and cause the type check to miss.\nfunction prependModifier (value, symbol) {\n  return typeof value === 'string' ? symbol + value : value\n}\n\n/*  */\n\nfunction installRenderHelpers (target) {\n  target._o = markOnce;\n  target._n = toNumber;\n  target._s = toString;\n  target._l = renderList;\n  target._t = renderSlot;\n  target._q = looseEqual;\n  target._i = looseIndexOf;\n  target._m = renderStatic;\n  target._f = resolveFilter;\n  target._k = checkKeyCodes;\n  target._b = bindObjectProps;\n  target._v = createTextVNode;\n  target._e = createEmptyVNode;\n  target._u = resolveScopedSlots;\n  target._g = bindObjectListeners;\n  target._d = bindDynamicKeys;\n  target._p = prependModifier;\n}\n\n/*  */\n\nfunction FunctionalRenderContext (\n  data,\n  props,\n  children,\n  parent,\n  Ctor\n) {\n  var this$1 = this;\n\n  var options = Ctor.options;\n  // ensure the createElement function in functional components\n  // gets a unique context - this is necessary for correct named slot check\n  var contextVm;\n  if (hasOwn(parent, '_uid')) {\n    contextVm = Object.create(parent);\n    // $flow-disable-line\n    contextVm._original = parent;\n  } else {\n    // the context vm passed in is a functional context as well.\n    // in this case we want to make sure we are able to get a hold to the\n    // real context instance.\n    contextVm = parent;\n    // $flow-disable-line\n    parent = parent._original;\n  }\n  var isCompiled = isTrue(options._compiled);\n  var needNormalization = !isCompiled;\n\n  this.data = data;\n  this.props = props;\n  this.children = children;\n  this.parent = parent;\n  this.listeners = data.on || emptyObject;\n  this.injections = resolveInject(options.inject, parent);\n  this.slots = function () {\n    if (!this$1.$slots) {\n      normalizeScopedSlots(\n        data.scopedSlots,\n        this$1.$slots = resolveSlots(children, parent)\n      );\n    }\n    return this$1.$slots\n  };\n\n  Object.defineProperty(this, 'scopedSlots', ({\n    enumerable: true,\n    get: function get () {\n      return normalizeScopedSlots(data.scopedSlots, this.slots())\n    }\n  }));\n\n  // support for compiled functional template\n  if (isCompiled) {\n    // exposing $options for renderStatic()\n    this.$options = options;\n    // pre-resolve slots for renderSlot()\n    this.$slots = this.slots();\n    this.$scopedSlots = normalizeScopedSlots(data.scopedSlots, this.$slots);\n  }\n\n  if (options._scopeId) {\n    this._c = function (a, b, c, d) {\n      var vnode = createElement(contextVm, a, b, c, d, needNormalization);\n      if (vnode && !Array.isArray(vnode)) {\n        vnode.fnScopeId = options._scopeId;\n        vnode.fnContext = parent;\n      }\n      return vnode\n    };\n  } else {\n    this._c = function (a, b, c, d) { return createElement(contextVm, a, b, c, d, needNormalization); };\n  }\n}\n\ninstallRenderHelpers(FunctionalRenderContext.prototype);\n\nfunction createFunctionalComponent (\n  Ctor,\n  propsData,\n  data,\n  contextVm,\n  children\n) {\n  var options = Ctor.options;\n  var props = {};\n  var propOptions = options.props;\n  if (isDef(propOptions)) {\n    for (var key in propOptions) {\n      props[key] = validateProp(key, propOptions, propsData || emptyObject);\n    }\n  } else {\n    if (isDef(data.attrs)) { mergeProps(props, data.attrs); }\n    if (isDef(data.props)) { mergeProps(props, data.props); }\n  }\n\n  var renderContext = new FunctionalRenderContext(\n    data,\n    props,\n    children,\n    contextVm,\n    Ctor\n  );\n\n  var vnode = options.render.call(null, renderContext._c, renderContext);\n\n  if (vnode instanceof VNode) {\n    return cloneAndMarkFunctionalResult(vnode, data, renderContext.parent, options, renderContext)\n  } else if (Array.isArray(vnode)) {\n    var vnodes = normalizeChildren(vnode) || [];\n    var res = new Array(vnodes.length);\n    for (var i = 0; i < vnodes.length; i++) {\n      res[i] = cloneAndMarkFunctionalResult(vnodes[i], data, renderContext.parent, options, renderContext);\n    }\n    return res\n  }\n}\n\nfunction cloneAndMarkFunctionalResult (vnode, data, contextVm, options, renderContext) {\n  // #7817 clone node before setting fnContext, otherwise if the node is reused\n  // (e.g. it was from a cached normal slot) the fnContext causes named slots\n  // that should not be matched to match.\n  var clone = cloneVNode(vnode);\n  clone.fnContext = contextVm;\n  clone.fnOptions = options;\n  if (process.env.NODE_ENV !== 'production') {\n    (clone.devtoolsMeta = clone.devtoolsMeta || {}).renderContext = renderContext;\n  }\n  if (data.slot) {\n    (clone.data || (clone.data = {})).slot = data.slot;\n  }\n  return clone\n}\n\nfunction mergeProps (to, from) {\n  for (var key in from) {\n    to[camelize(key)] = from[key];\n  }\n}\n\n/*  */\n\n/*  */\n\n/*  */\n\n/*  */\n\n// inline hooks to be invoked on component VNodes during patch\nvar componentVNodeHooks = {\n  init: function init (vnode, hydrating) {\n    if (\n      vnode.componentInstance &&\n      !vnode.componentInstance._isDestroyed &&\n      vnode.data.keepAlive\n    ) {\n      // kept-alive components, treat as a patch\n      var mountedNode = vnode; // work around flow\n      componentVNodeHooks.prepatch(mountedNode, mountedNode);\n    } else {\n      var child = vnode.componentInstance = createComponentInstanceForVnode(\n        vnode,\n        activeInstance\n      );\n      child.$mount(hydrating ? vnode.elm : undefined, hydrating);\n    }\n  },\n\n  prepatch: function prepatch (oldVnode, vnode) {\n    var options = vnode.componentOptions;\n    var child = vnode.componentInstance = oldVnode.componentInstance;\n    updateChildComponent(\n      child,\n      options.propsData, // updated props\n      options.listeners, // updated listeners\n      vnode, // new parent vnode\n      options.children // new children\n    );\n  },\n\n  insert: function insert (vnode) {\n    var context = vnode.context;\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isMounted) {\n      callHook(componentInstance, 'onServiceCreated');\n      callHook(componentInstance, 'onServiceAttached');\n      componentInstance._isMounted = true;\n      callHook(componentInstance, 'mounted');\n    }\n    if (vnode.data.keepAlive) {\n      if (context._isMounted) {\n        // vue-router#1212\n        // During updates, a kept-alive component's child components may\n        // change, so directly walking the tree here may call activated hooks\n        // on incorrect children. Instead we push them into a queue which will\n        // be processed after the whole patch process ended.\n        queueActivatedComponent(componentInstance);\n      } else {\n        activateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  },\n\n  destroy: function destroy (vnode) {\n    var componentInstance = vnode.componentInstance;\n    if (!componentInstance._isDestroyed) {\n      if (!vnode.data.keepAlive) {\n        componentInstance.$destroy();\n      } else {\n        deactivateChildComponent(componentInstance, true /* direct */);\n      }\n    }\n  }\n};\n\nvar hooksToMerge = Object.keys(componentVNodeHooks);\n\nfunction createComponent (\n  Ctor,\n  data,\n  context,\n  children,\n  tag\n) {\n  if (isUndef(Ctor)) {\n    return\n  }\n\n  var baseCtor = context.$options._base;\n\n  // plain options object: turn it into a constructor\n  if (isObject(Ctor)) {\n    Ctor = baseCtor.extend(Ctor);\n  }\n\n  // if at this stage it's not a constructor or an async component factory,\n  // reject.\n  if (typeof Ctor !== 'function') {\n    if (process.env.NODE_ENV !== 'production') {\n      warn((\"Invalid Component definition: \" + (String(Ctor))), context);\n    }\n    return\n  }\n\n  // async component\n  var asyncFactory;\n  if (isUndef(Ctor.cid)) {\n    asyncFactory = Ctor;\n    Ctor = resolveAsyncComponent(asyncFactory, baseCtor);\n    if (Ctor === undefined) {\n      // return a placeholder node for async component, which is rendered\n      // as a comment node but preserves all the raw information for the node.\n      // the information will be used for async server-rendering and hydration.\n      return createAsyncPlaceholder(\n        asyncFactory,\n        data,\n        context,\n        children,\n        tag\n      )\n    }\n  }\n\n  data = data || {};\n\n  // resolve constructor options in case global mixins are applied after\n  // component constructor creation\n  resolveConstructorOptions(Ctor);\n\n  // transform component v-model data into props & events\n  if (isDef(data.model)) {\n    transformModel(Ctor.options, data);\n  }\n\n  // extract props\n  var propsData = extractPropsFromVNodeData(data, Ctor, tag, context); // fixed by xxxxxx\n\n  // functional component\n  if (isTrue(Ctor.options.functional)) {\n    return createFunctionalComponent(Ctor, propsData, data, context, children)\n  }\n\n  // extract listeners, since these needs to be treated as\n  // child component listeners instead of DOM listeners\n  var listeners = data.on;\n  // replace with listeners with .native modifier\n  // so it gets processed during parent component patch.\n  data.on = data.nativeOn;\n\n  if (isTrue(Ctor.options.abstract)) {\n    // abstract components do not keep anything\n    // other than props & listeners & slot\n\n    // work around flow\n    var slot = data.slot;\n    data = {};\n    if (slot) {\n      data.slot = slot;\n    }\n  }\n\n  // install component management hooks onto the placeholder node\n  installComponentHooks(data);\n\n  // return a placeholder vnode\n  var name = Ctor.options.name || tag;\n  var vnode = new VNode(\n    (\"vue-component-\" + (Ctor.cid) + (name ? (\"-\" + name) : '')),\n    data, undefined, undefined, undefined, context,\n    { Ctor: Ctor, propsData: propsData, listeners: listeners, tag: tag, children: children },\n    asyncFactory\n  );\n\n  return vnode\n}\n\nfunction createComponentInstanceForVnode (\n  vnode, // we know it's MountedComponentVNode but flow doesn't\n  parent // activeInstance in lifecycle state\n) {\n  var options = {\n    _isComponent: true,\n    _parentVnode: vnode,\n    parent: parent\n  };\n  // check inline-template render functions\n  var inlineTemplate = vnode.data.inlineTemplate;\n  if (isDef(inlineTemplate)) {\n    options.render = inlineTemplate.render;\n    options.staticRenderFns = inlineTemplate.staticRenderFns;\n  }\n  return new vnode.componentOptions.Ctor(options)\n}\n\nfunction installComponentHooks (data) {\n  var hooks = data.hook || (data.hook = {});\n  for (var i = 0; i < hooksToMerge.length; i++) {\n    var key = hooksToMerge[i];\n    var existing = hooks[key];\n    var toMerge = componentVNodeHooks[key];\n    if (existing !== toMerge && !(existing && existing._merged)) {\n      hooks[key] = existing ? mergeHook$1(toMerge, existing) : toMerge;\n    }\n  }\n}\n\nfunction mergeHook$1 (f1, f2) {\n  var merged = function (a, b) {\n    // flow complains about extra args which is why we use any\n    f1(a, b);\n    f2(a, b);\n  };\n  merged._merged = true;\n  return merged\n}\n\n// transform component v-model info (value and callback) into\n// prop and event handler respectively.\nfunction transformModel (options, data) {\n  var prop = (options.model && options.model.prop) || 'value';\n  var event = (options.model && options.model.event) || 'input'\n  ;(data.attrs || (data.attrs = {}))[prop] = data.model.value;\n  var on = data.on || (data.on = {});\n  var existing = on[event];\n  var callback = data.model.callback;\n  if (isDef(existing)) {\n    if (\n      Array.isArray(existing)\n        ? existing.indexOf(callback) === -1\n        : existing !== callback\n    ) {\n      on[event] = [callback].concat(existing);\n    }\n  } else {\n    on[event] = callback;\n  }\n}\n\n/*  */\n\nvar SIMPLE_NORMALIZE = 1;\nvar ALWAYS_NORMALIZE = 2;\n\n// wrapper function for providing a more flexible interface\n// without getting yelled at by flow\nfunction createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType,\n  alwaysNormalize\n) {\n  if (Array.isArray(data) || isPrimitive(data)) {\n    normalizationType = children;\n    children = data;\n    data = undefined;\n  }\n  if (isTrue(alwaysNormalize)) {\n    normalizationType = ALWAYS_NORMALIZE;\n  }\n  return _createElement(context, tag, data, children, normalizationType)\n}\n\nfunction _createElement (\n  context,\n  tag,\n  data,\n  children,\n  normalizationType\n) {\n  if (isDef(data) && isDef((data).__ob__)) {\n    process.env.NODE_ENV !== 'production' && warn(\n      \"Avoid using observed data object as vnode data: \" + (JSON.stringify(data)) + \"\\n\" +\n      'Always create fresh vnode data objects in each render!',\n      context\n    );\n    return createEmptyVNode()\n  }\n  // object syntax in v-bind\n  if (isDef(data) && isDef(data.is)) {\n    tag = data.is;\n  }\n  if (!tag) {\n    // in case of component :is set to falsy value\n    return createEmptyVNode()\n  }\n  // warn against non-primitive key\n  if (process.env.NODE_ENV !== 'production' &&\n    isDef(data) && isDef(data.key) && !isPrimitive(data.key)\n  ) {\n    {\n      warn(\n        'Avoid using non-primitive value as key, ' +\n        'use string/number value instead.',\n        context\n      );\n    }\n  }\n  // support single function children as default scoped slot\n  if (Array.isArray(children) &&\n    typeof children[0] === 'function'\n  ) {\n    data = data || {};\n    data.scopedSlots = { default: children[0] };\n    children.length = 0;\n  }\n  if (normalizationType === ALWAYS_NORMALIZE) {\n    children = normalizeChildren(children);\n  } else if (normalizationType === SIMPLE_NORMALIZE) {\n    children = simpleNormalizeChildren(children);\n  }\n  var vnode, ns;\n  if (typeof tag === 'string') {\n    var Ctor;\n    ns = (context.$vnode && context.$vnode.ns) || config.getTagNamespace(tag);\n    if (config.isReservedTag(tag)) {\n      // platform built-in elements\n      if (process.env.NODE_ENV !== 'production' && isDef(data) && isDef(data.nativeOn)) {\n        warn(\n          (\"The .native modifier for v-on is only valid on components but it was used on <\" + tag + \">.\"),\n          context\n        );\n      }\n      vnode = new VNode(\n        config.parsePlatformTagName(tag), data, children,\n        undefined, undefined, context\n      );\n    } else if ((!data || !data.pre) && isDef(Ctor = resolveAsset(context.$options, 'components', tag))) {\n      // component\n      vnode = createComponent(Ctor, data, context, children, tag);\n    } else {\n      // unknown or unlisted namespaced elements\n      // check at runtime because it may get assigned a namespace when its\n      // parent normalizes children\n      vnode = new VNode(\n        tag, data, children,\n        undefined, undefined, context\n      );\n    }\n  } else {\n    // direct component options / constructor\n    vnode = createComponent(tag, data, context, children);\n  }\n  if (Array.isArray(vnode)) {\n    return vnode\n  } else if (isDef(vnode)) {\n    if (isDef(ns)) { applyNS(vnode, ns); }\n    if (isDef(data)) { registerDeepBindings(data); }\n    return vnode\n  } else {\n    return createEmptyVNode()\n  }\n}\n\nfunction applyNS (vnode, ns, force) {\n  vnode.ns = ns;\n  if (vnode.tag === 'foreignObject') {\n    // use default namespace inside foreignObject\n    ns = undefined;\n    force = true;\n  }\n  if (isDef(vnode.children)) {\n    for (var i = 0, l = vnode.children.length; i < l; i++) {\n      var child = vnode.children[i];\n      if (isDef(child.tag) && (\n        isUndef(child.ns) || (isTrue(force) && child.tag !== 'svg'))) {\n        applyNS(child, ns, force);\n      }\n    }\n  }\n}\n\n// ref #5318\n// necessary to ensure parent re-render when deep bindings like :style and\n// :class are used on slot nodes\nfunction registerDeepBindings (data) {\n  if (isObject(data.style)) {\n    traverse(data.style);\n  }\n  if (isObject(data.class)) {\n    traverse(data.class);\n  }\n}\n\n/*  */\n\nfunction initRender (vm) {\n  vm._vnode = null; // the root of the child tree\n  vm._staticTrees = null; // v-once cached trees\n  var options = vm.$options;\n  var parentVnode = vm.$vnode = options._parentVnode; // the placeholder node in parent tree\n  var renderContext = parentVnode && parentVnode.context;\n  vm.$slots = resolveSlots(options._renderChildren, renderContext);\n  vm.$scopedSlots = emptyObject;\n  // bind the createElement fn to this instance\n  // so that we get proper render context inside it.\n  // args order: tag, data, children, normalizationType, alwaysNormalize\n  // internal version is used by render functions compiled from templates\n  vm._c = function (a, b, c, d) { return createElement(vm, a, b, c, d, false); };\n  // normalization is always applied for the public version, used in\n  // user-written render functions.\n  vm.$createElement = function (a, b, c, d) { return createElement(vm, a, b, c, d, true); };\n\n  // $attrs & $listeners are exposed for easier HOC creation.\n  // they need to be reactive so that HOCs using them are always updated\n  var parentData = parentVnode && parentVnode.data;\n\n  /* istanbul ignore else */\n  if (process.env.NODE_ENV !== 'production') {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$attrs is readonly.\", vm);\n    }, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, function () {\n      !isUpdatingChildComponent && warn(\"$listeners is readonly.\", vm);\n    }, true);\n  } else {\n    defineReactive$$1(vm, '$attrs', parentData && parentData.attrs || emptyObject, null, true);\n    defineReactive$$1(vm, '$listeners', options._parentListeners || emptyObject, null, true);\n  }\n}\n\nvar currentRenderingInstance = null;\n\nfunction renderMixin (Vue) {\n  // install runtime convenience helpers\n  installRenderHelpers(Vue.prototype);\n\n  Vue.prototype.$nextTick = function (fn) {\n    return nextTick(fn, this)\n  };\n\n  Vue.prototype._render = function () {\n    var vm = this;\n    var ref = vm.$options;\n    var render = ref.render;\n    var _parentVnode = ref._parentVnode;\n\n    if (_parentVnode) {\n      vm.$scopedSlots = normalizeScopedSlots(\n        _parentVnode.data.scopedSlots,\n        vm.$slots,\n        vm.$scopedSlots\n      );\n    }\n\n    // set parent vnode. this allows render functions to have access\n    // to the data on the placeholder node.\n    vm.$vnode = _parentVnode;\n    // render self\n    var vnode;\n    try {\n      // There's no need to maintain a stack because all render fns are called\n      // separately from one another. Nested component's render fns are called\n      // when parent component is patched.\n      currentRenderingInstance = vm;\n      vnode = render.call(vm._renderProxy, vm.$createElement);\n    } catch (e) {\n      handleError(e, vm, \"render\");\n      // return error render result,\n      // or previous vnode to prevent render error causing blank component\n      /* istanbul ignore else */\n      if (process.env.NODE_ENV !== 'production' && vm.$options.renderError) {\n        try {\n          vnode = vm.$options.renderError.call(vm._renderProxy, vm.$createElement, e);\n        } catch (e) {\n          handleError(e, vm, \"renderError\");\n          vnode = vm._vnode;\n        }\n      } else {\n        vnode = vm._vnode;\n      }\n    } finally {\n      currentRenderingInstance = null;\n    }\n    // if the returned array contains only a single node, allow it\n    if (Array.isArray(vnode) && vnode.length === 1) {\n      vnode = vnode[0];\n    }\n    // return empty vnode in case the render function errored out\n    if (!(vnode instanceof VNode)) {\n      if (process.env.NODE_ENV !== 'production' && Array.isArray(vnode)) {\n        warn(\n          'Multiple root nodes returned from render function. Render function ' +\n          'should return a single root node.',\n          vm\n        );\n      }\n      vnode = createEmptyVNode();\n    }\n    // set parent\n    vnode.parent = _parentVnode;\n    return vnode\n  };\n}\n\n/*  */\n\nfunction ensureCtor (comp, base) {\n  if (\n    comp.__esModule ||\n    (hasSymbol && comp[Symbol.toStringTag] === 'Module')\n  ) {\n    comp = comp.default;\n  }\n  return isObject(comp)\n    ? base.extend(comp)\n    : comp\n}\n\nfunction createAsyncPlaceholder (\n  factory,\n  data,\n  context,\n  children,\n  tag\n) {\n  var node = createEmptyVNode();\n  node.asyncFactory = factory;\n  node.asyncMeta = { data: data, context: context, children: children, tag: tag };\n  return node\n}\n\nfunction resolveAsyncComponent (\n  factory,\n  baseCtor\n) {\n  if (isTrue(factory.error) && isDef(factory.errorComp)) {\n    return factory.errorComp\n  }\n\n  if (isDef(factory.resolved)) {\n    return factory.resolved\n  }\n\n  var owner = currentRenderingInstance;\n  if (owner && isDef(factory.owners) && factory.owners.indexOf(owner) === -1) {\n    // already pending\n    factory.owners.push(owner);\n  }\n\n  if (isTrue(factory.loading) && isDef(factory.loadingComp)) {\n    return factory.loadingComp\n  }\n\n  if (owner && !isDef(factory.owners)) {\n    var owners = factory.owners = [owner];\n    var sync = true;\n    var timerLoading = null;\n    var timerTimeout = null\n\n    ;(owner).$on('hook:destroyed', function () { return remove(owners, owner); });\n\n    var forceRender = function (renderCompleted) {\n      for (var i = 0, l = owners.length; i < l; i++) {\n        (owners[i]).$forceUpdate();\n      }\n\n      if (renderCompleted) {\n        owners.length = 0;\n        if (timerLoading !== null) {\n          clearTimeout(timerLoading);\n          timerLoading = null;\n        }\n        if (timerTimeout !== null) {\n          clearTimeout(timerTimeout);\n          timerTimeout = null;\n        }\n      }\n    };\n\n    var resolve = once(function (res) {\n      // cache resolved\n      factory.resolved = ensureCtor(res, baseCtor);\n      // invoke callbacks only if this is not a synchronous resolve\n      // (async resolves are shimmed as synchronous during SSR)\n      if (!sync) {\n        forceRender(true);\n      } else {\n        owners.length = 0;\n      }\n    });\n\n    var reject = once(function (reason) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed to resolve async component: \" + (String(factory)) +\n        (reason ? (\"\\nReason: \" + reason) : '')\n      );\n      if (isDef(factory.errorComp)) {\n        factory.error = true;\n        forceRender(true);\n      }\n    });\n\n    var res = factory(resolve, reject);\n\n    if (isObject(res)) {\n      if (isPromise(res)) {\n        // () => Promise\n        if (isUndef(factory.resolved)) {\n          res.then(resolve, reject);\n        }\n      } else if (isPromise(res.component)) {\n        res.component.then(resolve, reject);\n\n        if (isDef(res.error)) {\n          factory.errorComp = ensureCtor(res.error, baseCtor);\n        }\n\n        if (isDef(res.loading)) {\n          factory.loadingComp = ensureCtor(res.loading, baseCtor);\n          if (res.delay === 0) {\n            factory.loading = true;\n          } else {\n            timerLoading = setTimeout(function () {\n              timerLoading = null;\n              if (isUndef(factory.resolved) && isUndef(factory.error)) {\n                factory.loading = true;\n                forceRender(false);\n              }\n            }, res.delay || 200);\n          }\n        }\n\n        if (isDef(res.timeout)) {\n          timerTimeout = setTimeout(function () {\n            timerTimeout = null;\n            if (isUndef(factory.resolved)) {\n              reject(\n                process.env.NODE_ENV !== 'production'\n                  ? (\"timeout (\" + (res.timeout) + \"ms)\")\n                  : null\n              );\n            }\n          }, res.timeout);\n        }\n      }\n    }\n\n    sync = false;\n    // return in case resolved synchronously\n    return factory.loading\n      ? factory.loadingComp\n      : factory.resolved\n  }\n}\n\n/*  */\n\nfunction isAsyncPlaceholder (node) {\n  return node.isComment && node.asyncFactory\n}\n\n/*  */\n\nfunction getFirstComponentChild (children) {\n  if (Array.isArray(children)) {\n    for (var i = 0; i < children.length; i++) {\n      var c = children[i];\n      if (isDef(c) && (isDef(c.componentOptions) || isAsyncPlaceholder(c))) {\n        return c\n      }\n    }\n  }\n}\n\n/*  */\n\n/*  */\n\nfunction initEvents (vm) {\n  vm._events = Object.create(null);\n  vm._hasHookEvent = false;\n  // init parent attached events\n  var listeners = vm.$options._parentListeners;\n  if (listeners) {\n    updateComponentListeners(vm, listeners);\n  }\n}\n\nvar target;\n\nfunction add (event, fn) {\n  target.$on(event, fn);\n}\n\nfunction remove$1 (event, fn) {\n  target.$off(event, fn);\n}\n\nfunction createOnceHandler (event, fn) {\n  var _target = target;\n  return function onceHandler () {\n    var res = fn.apply(null, arguments);\n    if (res !== null) {\n      _target.$off(event, onceHandler);\n    }\n  }\n}\n\nfunction updateComponentListeners (\n  vm,\n  listeners,\n  oldListeners\n) {\n  target = vm;\n  updateListeners(listeners, oldListeners || {}, add, remove$1, createOnceHandler, vm);\n  target = undefined;\n}\n\nfunction eventsMixin (Vue) {\n  var hookRE = /^hook:/;\n  Vue.prototype.$on = function (event, fn) {\n    var vm = this;\n    if (Array.isArray(event)) {\n      for (var i = 0, l = event.length; i < l; i++) {\n        vm.$on(event[i], fn);\n      }\n    } else {\n      (vm._events[event] || (vm._events[event] = [])).push(fn);\n      // optimize hook:event cost by using a boolean flag marked at registration\n      // instead of a hash lookup\n      if (hookRE.test(event)) {\n        vm._hasHookEvent = true;\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$once = function (event, fn) {\n    var vm = this;\n    function on () {\n      vm.$off(event, on);\n      fn.apply(vm, arguments);\n    }\n    on.fn = fn;\n    vm.$on(event, on);\n    return vm\n  };\n\n  Vue.prototype.$off = function (event, fn) {\n    var vm = this;\n    // all\n    if (!arguments.length) {\n      vm._events = Object.create(null);\n      return vm\n    }\n    // array of events\n    if (Array.isArray(event)) {\n      for (var i$1 = 0, l = event.length; i$1 < l; i$1++) {\n        vm.$off(event[i$1], fn);\n      }\n      return vm\n    }\n    // specific event\n    var cbs = vm._events[event];\n    if (!cbs) {\n      return vm\n    }\n    if (!fn) {\n      vm._events[event] = null;\n      return vm\n    }\n    // specific handler\n    var cb;\n    var i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn || cb.fn === fn) {\n        cbs.splice(i, 1);\n        break\n      }\n    }\n    return vm\n  };\n\n  Vue.prototype.$emit = function (event) {\n    var vm = this;\n    if (process.env.NODE_ENV !== 'production') {\n      var lowerCaseEvent = event.toLowerCase();\n      if (lowerCaseEvent !== event && vm._events[lowerCaseEvent]) {\n        tip(\n          \"Event \\\"\" + lowerCaseEvent + \"\\\" is emitted in component \" +\n          (formatComponentName(vm)) + \" but the handler is registered for \\\"\" + event + \"\\\". \" +\n          \"Note that HTML attributes are case-insensitive and you cannot use \" +\n          \"v-on to listen to camelCase events when using in-DOM templates. \" +\n          \"You should probably use \\\"\" + (hyphenate(event)) + \"\\\" instead of \\\"\" + event + \"\\\".\"\n        );\n      }\n    }\n    var cbs = vm._events[event];\n    if (cbs) {\n      cbs = cbs.length > 1 ? toArray(cbs) : cbs;\n      var args = toArray(arguments, 1);\n      var info = \"event handler for \\\"\" + event + \"\\\"\";\n      for (var i = 0, l = cbs.length; i < l; i++) {\n        invokeWithErrorHandling(cbs[i], vm, args, vm, info);\n      }\n    }\n    return vm\n  };\n}\n\n/*  */\n\nvar activeInstance = null;\nvar isUpdatingChildComponent = false;\n\nfunction setActiveInstance(vm) {\n  var prevActiveInstance = activeInstance;\n  activeInstance = vm;\n  return function () {\n    activeInstance = prevActiveInstance;\n  }\n}\n\nfunction initLifecycle (vm) {\n  var options = vm.$options;\n\n  // locate first non-abstract parent\n  var parent = options.parent;\n  if (parent && !options.abstract) {\n    while (parent.$options.abstract && parent.$parent) {\n      parent = parent.$parent;\n    }\n    parent.$children.push(vm);\n  }\n\n  vm.$parent = parent;\n  vm.$root = parent ? parent.$root : vm;\n\n  vm.$children = [];\n  vm.$refs = {};\n\n  vm._watcher = null;\n  vm._inactive = null;\n  vm._directInactive = false;\n  vm._isMounted = false;\n  vm._isDestroyed = false;\n  vm._isBeingDestroyed = false;\n}\n\nfunction lifecycleMixin (Vue) {\n  Vue.prototype._update = function (vnode, hydrating) {\n    var vm = this;\n    var prevEl = vm.$el;\n    var prevVnode = vm._vnode;\n    var restoreActiveInstance = setActiveInstance(vm);\n    vm._vnode = vnode;\n    // Vue.prototype.__patch__ is injected in entry points\n    // based on the rendering backend used.\n    if (!prevVnode) {\n      // initial render\n      vm.$el = vm.__patch__(vm.$el, vnode, hydrating, false /* removeOnly */);\n    } else {\n      // updates\n      vm.$el = vm.__patch__(prevVnode, vnode);\n    }\n    restoreActiveInstance();\n    // update __vue__ reference\n    if (prevEl) {\n      prevEl.__vue__ = null;\n    }\n    if (vm.$el) {\n      vm.$el.__vue__ = vm;\n    }\n    // if parent is an HOC, update its $el as well\n    if (vm.$vnode && vm.$parent && vm.$vnode === vm.$parent._vnode) {\n      vm.$parent.$el = vm.$el;\n    }\n    // updated hook is called by the scheduler to ensure that children are\n    // updated in a parent's updated hook.\n  };\n\n  Vue.prototype.$forceUpdate = function () {\n    var vm = this;\n    if (vm._watcher) {\n      vm._watcher.update();\n    }\n  };\n\n  Vue.prototype.$destroy = function () {\n    var vm = this;\n    if (vm._isBeingDestroyed) {\n      return\n    }\n    callHook(vm, 'beforeDestroy');\n    vm._isBeingDestroyed = true;\n    // remove self from parent\n    var parent = vm.$parent;\n    if (parent && !parent._isBeingDestroyed && !vm.$options.abstract) {\n      remove(parent.$children, vm);\n    }\n    // teardown watchers\n    if (vm._watcher) {\n      vm._watcher.teardown();\n    }\n    var i = vm._watchers.length;\n    while (i--) {\n      vm._watchers[i].teardown();\n    }\n    // remove reference from data ob\n    // frozen object may not have observer.\n    if (vm._data.__ob__) {\n      vm._data.__ob__.vmCount--;\n    }\n    // call the last hook...\n    vm._isDestroyed = true;\n    // invoke destroy hooks on current rendered tree\n    vm.__patch__(vm._vnode, null);\n    // fire destroyed hook\n    callHook(vm, 'destroyed');\n    // turn off all instance listeners.\n    vm.$off();\n    // remove __vue__ reference\n    if (vm.$el) {\n      vm.$el.__vue__ = null;\n    }\n    // release circular reference (#6759)\n    if (vm.$vnode) {\n      vm.$vnode.parent = null;\n    }\n  };\n}\n\nfunction updateChildComponent (\n  vm,\n  propsData,\n  listeners,\n  parentVnode,\n  renderChildren\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = true;\n  }\n\n  // determine whether component has slot children\n  // we need to do this before overwriting $options._renderChildren.\n\n  // check if there are dynamic scopedSlots (hand-written or compiled but with\n  // dynamic slot names). Static scoped slots compiled from template has the\n  // \"$stable\" marker.\n  var newScopedSlots = parentVnode.data.scopedSlots;\n  var oldScopedSlots = vm.$scopedSlots;\n  var hasDynamicScopedSlot = !!(\n    (newScopedSlots && !newScopedSlots.$stable) ||\n    (oldScopedSlots !== emptyObject && !oldScopedSlots.$stable) ||\n    (newScopedSlots && vm.$scopedSlots.$key !== newScopedSlots.$key)\n  );\n\n  // Any static slot children from the parent may have changed during parent's\n  // update. Dynamic scoped slots may also have changed. In such cases, a forced\n  // update is necessary to ensure correctness.\n  var needsForceUpdate = !!(\n    renderChildren ||               // has new static slots\n    vm.$options._renderChildren ||  // has old static slots\n    hasDynamicScopedSlot\n  );\n\n  vm.$options._parentVnode = parentVnode;\n  vm.$vnode = parentVnode; // update vm's placeholder node without re-render\n\n  if (vm._vnode) { // update child tree's parent\n    vm._vnode.parent = parentVnode;\n  }\n  vm.$options._renderChildren = renderChildren;\n\n  // update $attrs and $listeners hash\n  // these are also reactive so they may trigger child update if the child\n  // used them during render\n  vm.$attrs = parentVnode.data.attrs || emptyObject;\n  vm.$listeners = listeners || emptyObject;\n\n  // update props\n  if (propsData && vm.$options.props) {\n    toggleObserving(false);\n    var props = vm._props;\n    var propKeys = vm.$options._propKeys || [];\n    for (var i = 0; i < propKeys.length; i++) {\n      var key = propKeys[i];\n      var propOptions = vm.$options.props; // wtf flow?\n      props[key] = validateProp(key, propOptions, propsData, vm);\n    }\n    toggleObserving(true);\n    // keep a copy of raw propsData\n    vm.$options.propsData = propsData;\n  }\n  \n  // fixed by xxxxxx update properties(mp runtime)\n  vm._$updateProperties && vm._$updateProperties(vm);\n  \n  // update listeners\n  listeners = listeners || emptyObject;\n  var oldListeners = vm.$options._parentListeners;\n  vm.$options._parentListeners = listeners;\n  updateComponentListeners(vm, listeners, oldListeners);\n\n  // resolve slots + force update if has children\n  if (needsForceUpdate) {\n    vm.$slots = resolveSlots(renderChildren, parentVnode.context);\n    vm.$forceUpdate();\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    isUpdatingChildComponent = false;\n  }\n}\n\nfunction isInInactiveTree (vm) {\n  while (vm && (vm = vm.$parent)) {\n    if (vm._inactive) { return true }\n  }\n  return false\n}\n\nfunction activateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = false;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  } else if (vm._directInactive) {\n    return\n  }\n  if (vm._inactive || vm._inactive === null) {\n    vm._inactive = false;\n    for (var i = 0; i < vm.$children.length; i++) {\n      activateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'activated');\n  }\n}\n\nfunction deactivateChildComponent (vm, direct) {\n  if (direct) {\n    vm._directInactive = true;\n    if (isInInactiveTree(vm)) {\n      return\n    }\n  }\n  if (!vm._inactive) {\n    vm._inactive = true;\n    for (var i = 0; i < vm.$children.length; i++) {\n      deactivateChildComponent(vm.$children[i]);\n    }\n    callHook(vm, 'deactivated');\n  }\n}\n\nfunction callHook (vm, hook) {\n  // #7573 disable dep collection when invoking lifecycle hooks\n  pushTarget();\n  var handlers = vm.$options[hook];\n  var info = hook + \" hook\";\n  if (handlers) {\n    for (var i = 0, j = handlers.length; i < j; i++) {\n      invokeWithErrorHandling(handlers[i], vm, null, vm, info);\n    }\n  }\n  if (vm._hasHookEvent) {\n    vm.$emit('hook:' + hook);\n  }\n  popTarget();\n}\n\n/*  */\n\nvar MAX_UPDATE_COUNT = 100;\n\nvar queue = [];\nvar activatedChildren = [];\nvar has = {};\nvar circular = {};\nvar waiting = false;\nvar flushing = false;\nvar index = 0;\n\n/**\n * Reset the scheduler's state.\n */\nfunction resetSchedulerState () {\n  index = queue.length = activatedChildren.length = 0;\n  has = {};\n  if (process.env.NODE_ENV !== 'production') {\n    circular = {};\n  }\n  waiting = flushing = false;\n}\n\n// Async edge case #6566 requires saving the timestamp when event listeners are\n// attached. However, calling performance.now() has a perf overhead especially\n// if the page has thousands of event listeners. Instead, we take a timestamp\n// every time the scheduler flushes and use that for all event listeners\n// attached during that flush.\nvar currentFlushTimestamp = 0;\n\n// Async edge case fix requires storing an event listener's attach timestamp.\nvar getNow = Date.now;\n\n// Determine what event timestamp the browser is using. Annoyingly, the\n// timestamp can either be hi-res (relative to page load) or low-res\n// (relative to UNIX epoch), so in order to compare time we have to use the\n// same timestamp type when saving the flush timestamp.\n// All IE versions use low-res event timestamps, and have problematic clock\n// implementations (#9632)\nif (inBrowser && !isIE) {\n  var performance = window.performance;\n  if (\n    performance &&\n    typeof performance.now === 'function' &&\n    getNow() > document.createEvent('Event').timeStamp\n  ) {\n    // if the event timestamp, although evaluated AFTER the Date.now(), is\n    // smaller than it, it means the event is using a hi-res timestamp,\n    // and we need to use the hi-res version for event listener timestamps as\n    // well.\n    getNow = function () { return performance.now(); };\n  }\n}\n\n/**\n * Flush both queues and run the watchers.\n */\nfunction flushSchedulerQueue () {\n  currentFlushTimestamp = getNow();\n  flushing = true;\n  var watcher, id;\n\n  // Sort queue before flush.\n  // This ensures that:\n  // 1. Components are updated from parent to child. (because parent is always\n  //    created before the child)\n  // 2. A component's user watchers are run before its render watcher (because\n  //    user watchers are created before the render watcher)\n  // 3. If a component is destroyed during a parent component's watcher run,\n  //    its watchers can be skipped.\n  queue.sort(function (a, b) { return a.id - b.id; });\n\n  // do not cache length because more watchers might be pushed\n  // as we run existing watchers\n  for (index = 0; index < queue.length; index++) {\n    watcher = queue[index];\n    if (watcher.before) {\n      watcher.before();\n    }\n    id = watcher.id;\n    has[id] = null;\n    watcher.run();\n    // in dev build, check and stop circular updates.\n    if (process.env.NODE_ENV !== 'production' && has[id] != null) {\n      circular[id] = (circular[id] || 0) + 1;\n      if (circular[id] > MAX_UPDATE_COUNT) {\n        warn(\n          'You may have an infinite update loop ' + (\n            watcher.user\n              ? (\"in watcher with expression \\\"\" + (watcher.expression) + \"\\\"\")\n              : \"in a component render function.\"\n          ),\n          watcher.vm\n        );\n        break\n      }\n    }\n  }\n\n  // keep copies of post queues before resetting state\n  var activatedQueue = activatedChildren.slice();\n  var updatedQueue = queue.slice();\n\n  resetSchedulerState();\n\n  // call component updated and activated hooks\n  callActivatedHooks(activatedQueue);\n  callUpdatedHooks(updatedQueue);\n\n  // devtool hook\n  /* istanbul ignore if */\n  if (devtools && config.devtools) {\n    devtools.emit('flush');\n  }\n}\n\nfunction callUpdatedHooks (queue) {\n  var i = queue.length;\n  while (i--) {\n    var watcher = queue[i];\n    var vm = watcher.vm;\n    if (vm._watcher === watcher && vm._isMounted && !vm._isDestroyed) {\n      callHook(vm, 'updated');\n    }\n  }\n}\n\n/**\n * Queue a kept-alive component that was activated during patch.\n * The queue will be processed after the entire tree has been patched.\n */\nfunction queueActivatedComponent (vm) {\n  // setting _inactive to false here so that a render function can\n  // rely on checking whether it's in an inactive tree (e.g. router-view)\n  vm._inactive = false;\n  activatedChildren.push(vm);\n}\n\nfunction callActivatedHooks (queue) {\n  for (var i = 0; i < queue.length; i++) {\n    queue[i]._inactive = true;\n    activateChildComponent(queue[i], true /* true */);\n  }\n}\n\n/**\n * Push a watcher into the watcher queue.\n * Jobs with duplicate IDs will be skipped unless it's\n * pushed when the queue is being flushed.\n */\nfunction queueWatcher (watcher) {\n  var id = watcher.id;\n  if (has[id] == null) {\n    has[id] = true;\n    if (!flushing) {\n      queue.push(watcher);\n    } else {\n      // if already flushing, splice the watcher based on its id\n      // if already past its id, it will be run next immediately.\n      var i = queue.length - 1;\n      while (i > index && queue[i].id > watcher.id) {\n        i--;\n      }\n      queue.splice(i + 1, 0, watcher);\n    }\n    // queue the flush\n    if (!waiting) {\n      waiting = true;\n\n      if (process.env.NODE_ENV !== 'production' && !config.async) {\n        flushSchedulerQueue();\n        return\n      }\n      nextTick(flushSchedulerQueue);\n    }\n  }\n}\n\n/*  */\n\n\n\nvar uid$2 = 0;\n\n/**\n * A watcher parses an expression, collects dependencies,\n * and fires callback when the expression value changes.\n * This is used for both the $watch() api and directives.\n */\nvar Watcher = function Watcher (\n  vm,\n  expOrFn,\n  cb,\n  options,\n  isRenderWatcher\n) {\n  this.vm = vm;\n  if (isRenderWatcher) {\n    vm._watcher = this;\n  }\n  vm._watchers.push(this);\n  // options\n  if (options) {\n    this.deep = !!options.deep;\n    this.user = !!options.user;\n    this.lazy = !!options.lazy;\n    this.sync = !!options.sync;\n    this.before = options.before;\n  } else {\n    this.deep = this.user = this.lazy = this.sync = false;\n  }\n  this.cb = cb;\n  this.id = ++uid$2; // uid for batching\n  this.active = true;\n  this.dirty = this.lazy; // for lazy watchers\n  this.deps = [];\n  this.newDeps = [];\n  this.depIds = new _Set();\n  this.newDepIds = new _Set();\n  this.expression = process.env.NODE_ENV !== 'production'\n    ? expOrFn.toString()\n    : '';\n  // parse expression for getter\n  if (typeof expOrFn === 'function') {\n    this.getter = expOrFn;\n  } else {\n    this.getter = parsePath(expOrFn);\n    if (!this.getter) {\n      this.getter = noop;\n      process.env.NODE_ENV !== 'production' && warn(\n        \"Failed watching path: \\\"\" + expOrFn + \"\\\" \" +\n        'Watcher only accepts simple dot-delimited paths. ' +\n        'For full control, use a function instead.',\n        vm\n      );\n    }\n  }\n  this.value = this.lazy\n    ? undefined\n    : this.get();\n};\n\n/**\n * Evaluate the getter, and re-collect dependencies.\n */\nWatcher.prototype.get = function get () {\n  pushTarget(this);\n  var value;\n  var vm = this.vm;\n  try {\n    value = this.getter.call(vm, vm);\n  } catch (e) {\n    if (this.user) {\n      handleError(e, vm, (\"getter for watcher \\\"\" + (this.expression) + \"\\\"\"));\n    } else {\n      throw e\n    }\n  } finally {\n    // \"touch\" every property so they are all tracked as\n    // dependencies for deep watching\n    if (this.deep) {\n      traverse(value);\n    }\n    popTarget();\n    this.cleanupDeps();\n  }\n  return value\n};\n\n/**\n * Add a dependency to this directive.\n */\nWatcher.prototype.addDep = function addDep (dep) {\n  var id = dep.id;\n  if (!this.newDepIds.has(id)) {\n    this.newDepIds.add(id);\n    this.newDeps.push(dep);\n    if (!this.depIds.has(id)) {\n      dep.addSub(this);\n    }\n  }\n};\n\n/**\n * Clean up for dependency collection.\n */\nWatcher.prototype.cleanupDeps = function cleanupDeps () {\n  var i = this.deps.length;\n  while (i--) {\n    var dep = this.deps[i];\n    if (!this.newDepIds.has(dep.id)) {\n      dep.removeSub(this);\n    }\n  }\n  var tmp = this.depIds;\n  this.depIds = this.newDepIds;\n  this.newDepIds = tmp;\n  this.newDepIds.clear();\n  tmp = this.deps;\n  this.deps = this.newDeps;\n  this.newDeps = tmp;\n  this.newDeps.length = 0;\n};\n\n/**\n * Subscriber interface.\n * Will be called when a dependency changes.\n */\nWatcher.prototype.update = function update () {\n  /* istanbul ignore else */\n  if (this.lazy) {\n    this.dirty = true;\n  } else if (this.sync) {\n    this.run();\n  } else {\n    queueWatcher(this);\n  }\n};\n\n/**\n * Scheduler job interface.\n * Will be called by the scheduler.\n */\nWatcher.prototype.run = function run () {\n  if (this.active) {\n    var value = this.get();\n    if (\n      value !== this.value ||\n      // Deep watchers and watchers on Object/Arrays should fire even\n      // when the value is the same, because the value may\n      // have mutated.\n      isObject(value) ||\n      this.deep\n    ) {\n      // set new value\n      var oldValue = this.value;\n      this.value = value;\n      if (this.user) {\n        try {\n          this.cb.call(this.vm, value, oldValue);\n        } catch (e) {\n          handleError(e, this.vm, (\"callback for watcher \\\"\" + (this.expression) + \"\\\"\"));\n        }\n      } else {\n        this.cb.call(this.vm, value, oldValue);\n      }\n    }\n  }\n};\n\n/**\n * Evaluate the value of the watcher.\n * This only gets called for lazy watchers.\n */\nWatcher.prototype.evaluate = function evaluate () {\n  this.value = this.get();\n  this.dirty = false;\n};\n\n/**\n * Depend on all deps collected by this watcher.\n */\nWatcher.prototype.depend = function depend () {\n  var i = this.deps.length;\n  while (i--) {\n    this.deps[i].depend();\n  }\n};\n\n/**\n * Remove self from all dependencies' subscriber list.\n */\nWatcher.prototype.teardown = function teardown () {\n  if (this.active) {\n    // remove self from vm's watcher list\n    // this is a somewhat expensive operation so we skip it\n    // if the vm is being destroyed.\n    if (!this.vm._isBeingDestroyed) {\n      remove(this.vm._watchers, this);\n    }\n    var i = this.deps.length;\n    while (i--) {\n      this.deps[i].removeSub(this);\n    }\n    this.active = false;\n  }\n};\n\n/*  */\n\nvar sharedPropertyDefinition = {\n  enumerable: true,\n  configurable: true,\n  get: noop,\n  set: noop\n};\n\nfunction proxy (target, sourceKey, key) {\n  sharedPropertyDefinition.get = function proxyGetter () {\n    return this[sourceKey][key]\n  };\n  sharedPropertyDefinition.set = function proxySetter (val) {\n    this[sourceKey][key] = val;\n  };\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction initState (vm) {\n  vm._watchers = [];\n  var opts = vm.$options;\n  if (opts.props) { initProps(vm, opts.props); }\n  if (opts.methods) { initMethods(vm, opts.methods); }\n  if (opts.data) {\n    initData(vm);\n  } else {\n    observe(vm._data = {}, true /* asRootData */);\n  }\n  if (opts.computed) { initComputed(vm, opts.computed); }\n  if (opts.watch && opts.watch !== nativeWatch) {\n    initWatch(vm, opts.watch);\n  }\n}\n\nfunction initProps (vm, propsOptions) {\n  var propsData = vm.$options.propsData || {};\n  var props = vm._props = {};\n  // cache prop keys so that future props updates can iterate using Array\n  // instead of dynamic object key enumeration.\n  var keys = vm.$options._propKeys = [];\n  var isRoot = !vm.$parent;\n  // root instance props should be converted\n  if (!isRoot) {\n    toggleObserving(false);\n  }\n  var loop = function ( key ) {\n    keys.push(key);\n    var value = validateProp(key, propsOptions, propsData, vm);\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      var hyphenatedKey = hyphenate(key);\n      if (isReservedAttribute(hyphenatedKey) ||\n          config.isReservedAttr(hyphenatedKey)) {\n        warn(\n          (\"\\\"\" + hyphenatedKey + \"\\\" is a reserved attribute and cannot be used as component prop.\"),\n          vm\n        );\n      }\n      defineReactive$$1(props, key, value, function () {\n        if (!isRoot && !isUpdatingChildComponent) {\n          {\n            if(vm.mpHost === 'mp-baidu' || vm.mpHost === 'mp-kuaishou' || vm.mpHost === 'mp-xhs'){//百度、快手、小红书 observer 在 setData callback 之后触发，直接忽略该 warn\n                return\n            }\n            //fixed by xxxxxx __next_tick_pending,uni://form-field 时不告警\n            if(\n                key === 'value' && \n                Array.isArray(vm.$options.behaviors) &&\n                vm.$options.behaviors.indexOf('uni://form-field') !== -1\n              ){\n              return\n            }\n            if(vm._getFormData){\n              return\n            }\n            var $parent = vm.$parent;\n            while($parent){\n              if($parent.__next_tick_pending){\n                return  \n              }\n              $parent = $parent.$parent;\n            }\n          }\n          warn(\n            \"Avoid mutating a prop directly since the value will be \" +\n            \"overwritten whenever the parent component re-renders. \" +\n            \"Instead, use a data or computed property based on the prop's \" +\n            \"value. Prop being mutated: \\\"\" + key + \"\\\"\",\n            vm\n          );\n        }\n      });\n    } else {\n      defineReactive$$1(props, key, value);\n    }\n    // static props are already proxied on the component's prototype\n    // during Vue.extend(). We only need to proxy props defined at\n    // instantiation here.\n    if (!(key in vm)) {\n      proxy(vm, \"_props\", key);\n    }\n  };\n\n  for (var key in propsOptions) loop( key );\n  toggleObserving(true);\n}\n\nfunction initData (vm) {\n  var data = vm.$options.data;\n  data = vm._data = typeof data === 'function'\n    ? getData(data, vm)\n    : data || {};\n  if (!isPlainObject(data)) {\n    data = {};\n    process.env.NODE_ENV !== 'production' && warn(\n      'data functions should return an object:\\n' +\n      'https://vuejs.org/v2/guide/components.html#data-Must-Be-a-Function',\n      vm\n    );\n  }\n  // proxy data on instance\n  var keys = Object.keys(data);\n  var props = vm.$options.props;\n  var methods = vm.$options.methods;\n  var i = keys.length;\n  while (i--) {\n    var key = keys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (methods && hasOwn(methods, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a data property.\"),\n          vm\n        );\n      }\n    }\n    if (props && hasOwn(props, key)) {\n      process.env.NODE_ENV !== 'production' && warn(\n        \"The data property \\\"\" + key + \"\\\" is already declared as a prop. \" +\n        \"Use prop default value instead.\",\n        vm\n      );\n    } else if (!isReserved(key)) {\n      proxy(vm, \"_data\", key);\n    }\n  }\n  // observe data\n  observe(data, true /* asRootData */);\n}\n\nfunction getData (data, vm) {\n  // #7573 disable dep collection when invoking data getters\n  pushTarget();\n  try {\n    return data.call(vm, vm)\n  } catch (e) {\n    handleError(e, vm, \"data()\");\n    return {}\n  } finally {\n    popTarget();\n  }\n}\n\nvar computedWatcherOptions = { lazy: true };\n\nfunction initComputed (vm, computed) {\n  // $flow-disable-line\n  var watchers = vm._computedWatchers = Object.create(null);\n  // computed properties are just getters during SSR\n  var isSSR = isServerRendering();\n\n  for (var key in computed) {\n    var userDef = computed[key];\n    var getter = typeof userDef === 'function' ? userDef : userDef.get;\n    if (process.env.NODE_ENV !== 'production' && getter == null) {\n      warn(\n        (\"Getter is missing for computed property \\\"\" + key + \"\\\".\"),\n        vm\n      );\n    }\n\n    if (!isSSR) {\n      // create internal watcher for the computed property.\n      watchers[key] = new Watcher(\n        vm,\n        getter || noop,\n        noop,\n        computedWatcherOptions\n      );\n    }\n\n    // component-defined computed properties are already defined on the\n    // component prototype. We only need to define computed properties defined\n    // at instantiation here.\n    if (!(key in vm)) {\n      defineComputed(vm, key, userDef);\n    } else if (process.env.NODE_ENV !== 'production') {\n      if (key in vm.$data) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined in data.\"), vm);\n      } else if (vm.$options.props && key in vm.$options.props) {\n        warn((\"The computed property \\\"\" + key + \"\\\" is already defined as a prop.\"), vm);\n      }\n    }\n  }\n}\n\nfunction defineComputed (\n  target,\n  key,\n  userDef\n) {\n  var shouldCache = !isServerRendering();\n  if (typeof userDef === 'function') {\n    sharedPropertyDefinition.get = shouldCache\n      ? createComputedGetter(key)\n      : createGetterInvoker(userDef);\n    sharedPropertyDefinition.set = noop;\n  } else {\n    sharedPropertyDefinition.get = userDef.get\n      ? shouldCache && userDef.cache !== false\n        ? createComputedGetter(key)\n        : createGetterInvoker(userDef.get)\n      : noop;\n    sharedPropertyDefinition.set = userDef.set || noop;\n  }\n  if (process.env.NODE_ENV !== 'production' &&\n      sharedPropertyDefinition.set === noop) {\n    sharedPropertyDefinition.set = function () {\n      warn(\n        (\"Computed property \\\"\" + key + \"\\\" was assigned to but it has no setter.\"),\n        this\n      );\n    };\n  }\n  Object.defineProperty(target, key, sharedPropertyDefinition);\n}\n\nfunction createComputedGetter (key) {\n  return function computedGetter () {\n    var watcher = this._computedWatchers && this._computedWatchers[key];\n    if (watcher) {\n      if (watcher.dirty) {\n        watcher.evaluate();\n      }\n      if (Dep.SharedObject.target) {// fixed by xxxxxx\n        watcher.depend();\n      }\n      return watcher.value\n    }\n  }\n}\n\nfunction createGetterInvoker(fn) {\n  return function computedGetter () {\n    return fn.call(this, this)\n  }\n}\n\nfunction initMethods (vm, methods) {\n  var props = vm.$options.props;\n  for (var key in methods) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof methods[key] !== 'function') {\n        warn(\n          \"Method \\\"\" + key + \"\\\" has type \\\"\" + (typeof methods[key]) + \"\\\" in the component definition. \" +\n          \"Did you reference the function correctly?\",\n          vm\n        );\n      }\n      if (props && hasOwn(props, key)) {\n        warn(\n          (\"Method \\\"\" + key + \"\\\" has already been defined as a prop.\"),\n          vm\n        );\n      }\n      if ((key in vm) && isReserved(key)) {\n        warn(\n          \"Method \\\"\" + key + \"\\\" conflicts with an existing Vue instance method. \" +\n          \"Avoid defining component methods that start with _ or $.\"\n        );\n      }\n    }\n    vm[key] = typeof methods[key] !== 'function' ? noop : bind(methods[key], vm);\n  }\n}\n\nfunction initWatch (vm, watch) {\n  for (var key in watch) {\n    var handler = watch[key];\n    if (Array.isArray(handler)) {\n      for (var i = 0; i < handler.length; i++) {\n        createWatcher(vm, key, handler[i]);\n      }\n    } else {\n      createWatcher(vm, key, handler);\n    }\n  }\n}\n\nfunction createWatcher (\n  vm,\n  expOrFn,\n  handler,\n  options\n) {\n  if (isPlainObject(handler)) {\n    options = handler;\n    handler = handler.handler;\n  }\n  if (typeof handler === 'string') {\n    handler = vm[handler];\n  }\n  return vm.$watch(expOrFn, handler, options)\n}\n\nfunction stateMixin (Vue) {\n  // flow somehow has problems with directly declared definition object\n  // when using Object.defineProperty, so we have to procedurally build up\n  // the object here.\n  var dataDef = {};\n  dataDef.get = function () { return this._data };\n  var propsDef = {};\n  propsDef.get = function () { return this._props };\n  if (process.env.NODE_ENV !== 'production') {\n    dataDef.set = function () {\n      warn(\n        'Avoid replacing instance root $data. ' +\n        'Use nested data properties instead.',\n        this\n      );\n    };\n    propsDef.set = function () {\n      warn(\"$props is readonly.\", this);\n    };\n  }\n  Object.defineProperty(Vue.prototype, '$data', dataDef);\n  Object.defineProperty(Vue.prototype, '$props', propsDef);\n\n  Vue.prototype.$set = set;\n  Vue.prototype.$delete = del;\n\n  Vue.prototype.$watch = function (\n    expOrFn,\n    cb,\n    options\n  ) {\n    var vm = this;\n    if (isPlainObject(cb)) {\n      return createWatcher(vm, expOrFn, cb, options)\n    }\n    options = options || {};\n    options.user = true;\n    var watcher = new Watcher(vm, expOrFn, cb, options);\n    if (options.immediate) {\n      try {\n        cb.call(vm, watcher.value);\n      } catch (error) {\n        handleError(error, vm, (\"callback for immediate watcher \\\"\" + (watcher.expression) + \"\\\"\"));\n      }\n    }\n    return function unwatchFn () {\n      watcher.teardown();\n    }\n  };\n}\n\n/*  */\n\nvar uid$3 = 0;\n\nfunction initMixin (Vue) {\n  Vue.prototype._init = function (options) {\n    var vm = this;\n    // a uid\n    vm._uid = uid$3++;\n\n    var startTag, endTag;\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      startTag = \"vue-perf-start:\" + (vm._uid);\n      endTag = \"vue-perf-end:\" + (vm._uid);\n      mark(startTag);\n    }\n\n    // a flag to avoid this being observed\n    vm._isVue = true;\n    // merge options\n    if (options && options._isComponent) {\n      // optimize internal component instantiation\n      // since dynamic options merging is pretty slow, and none of the\n      // internal component options needs special treatment.\n      initInternalComponent(vm, options);\n    } else {\n      vm.$options = mergeOptions(\n        resolveConstructorOptions(vm.constructor),\n        options || {},\n        vm\n      );\n    }\n    /* istanbul ignore else */\n    if (process.env.NODE_ENV !== 'production') {\n      initProxy(vm);\n    } else {\n      vm._renderProxy = vm;\n    }\n    // expose real self\n    vm._self = vm;\n    initLifecycle(vm);\n    initEvents(vm);\n    initRender(vm);\n    callHook(vm, 'beforeCreate');\n    !vm._$fallback && initInjections(vm); // resolve injections before data/props  \n    initState(vm);\n    !vm._$fallback && initProvide(vm); // resolve provide after data/props\n    !vm._$fallback && callHook(vm, 'created');      \n\n    /* istanbul ignore if */\n    if (process.env.NODE_ENV !== 'production' && config.performance && mark) {\n      vm._name = formatComponentName(vm, false);\n      mark(endTag);\n      measure((\"vue \" + (vm._name) + \" init\"), startTag, endTag);\n    }\n\n    if (vm.$options.el) {\n      vm.$mount(vm.$options.el);\n    }\n  };\n}\n\nfunction initInternalComponent (vm, options) {\n  var opts = vm.$options = Object.create(vm.constructor.options);\n  // doing this because it's faster than dynamic enumeration.\n  var parentVnode = options._parentVnode;\n  opts.parent = options.parent;\n  opts._parentVnode = parentVnode;\n\n  var vnodeComponentOptions = parentVnode.componentOptions;\n  opts.propsData = vnodeComponentOptions.propsData;\n  opts._parentListeners = vnodeComponentOptions.listeners;\n  opts._renderChildren = vnodeComponentOptions.children;\n  opts._componentTag = vnodeComponentOptions.tag;\n\n  if (options.render) {\n    opts.render = options.render;\n    opts.staticRenderFns = options.staticRenderFns;\n  }\n}\n\nfunction resolveConstructorOptions (Ctor) {\n  var options = Ctor.options;\n  if (Ctor.super) {\n    var superOptions = resolveConstructorOptions(Ctor.super);\n    var cachedSuperOptions = Ctor.superOptions;\n    if (superOptions !== cachedSuperOptions) {\n      // super option changed,\n      // need to resolve new options.\n      Ctor.superOptions = superOptions;\n      // check if there are any late-modified/attached options (#4976)\n      var modifiedOptions = resolveModifiedOptions(Ctor);\n      // update base extend options\n      if (modifiedOptions) {\n        extend(Ctor.extendOptions, modifiedOptions);\n      }\n      options = Ctor.options = mergeOptions(superOptions, Ctor.extendOptions);\n      if (options.name) {\n        options.components[options.name] = Ctor;\n      }\n    }\n  }\n  return options\n}\n\nfunction resolveModifiedOptions (Ctor) {\n  var modified;\n  var latest = Ctor.options;\n  var sealed = Ctor.sealedOptions;\n  for (var key in latest) {\n    if (latest[key] !== sealed[key]) {\n      if (!modified) { modified = {}; }\n      modified[key] = latest[key];\n    }\n  }\n  return modified\n}\n\nfunction Vue (options) {\n  if (process.env.NODE_ENV !== 'production' &&\n    !(this instanceof Vue)\n  ) {\n    warn('Vue is a constructor and should be called with the `new` keyword');\n  }\n  this._init(options);\n}\n\ninitMixin(Vue);\nstateMixin(Vue);\neventsMixin(Vue);\nlifecycleMixin(Vue);\nrenderMixin(Vue);\n\n/*  */\n\nfunction initUse (Vue) {\n  Vue.use = function (plugin) {\n    var installedPlugins = (this._installedPlugins || (this._installedPlugins = []));\n    if (installedPlugins.indexOf(plugin) > -1) {\n      return this\n    }\n\n    // additional parameters\n    var args = toArray(arguments, 1);\n    args.unshift(this);\n    if (typeof plugin.install === 'function') {\n      plugin.install.apply(plugin, args);\n    } else if (typeof plugin === 'function') {\n      plugin.apply(null, args);\n    }\n    installedPlugins.push(plugin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initMixin$1 (Vue) {\n  Vue.mixin = function (mixin) {\n    this.options = mergeOptions(this.options, mixin);\n    return this\n  };\n}\n\n/*  */\n\nfunction initExtend (Vue) {\n  /**\n   * Each instance constructor, including Vue, has a unique\n   * cid. This enables us to create wrapped \"child\n   * constructors\" for prototypal inheritance and cache them.\n   */\n  Vue.cid = 0;\n  var cid = 1;\n\n  /**\n   * Class inheritance\n   */\n  Vue.extend = function (extendOptions) {\n    extendOptions = extendOptions || {};\n    var Super = this;\n    var SuperId = Super.cid;\n    var cachedCtors = extendOptions._Ctor || (extendOptions._Ctor = {});\n    if (cachedCtors[SuperId]) {\n      return cachedCtors[SuperId]\n    }\n\n    var name = extendOptions.name || Super.options.name;\n    if (process.env.NODE_ENV !== 'production' && name) {\n      validateComponentName(name);\n    }\n\n    var Sub = function VueComponent (options) {\n      this._init(options);\n    };\n    Sub.prototype = Object.create(Super.prototype);\n    Sub.prototype.constructor = Sub;\n    Sub.cid = cid++;\n    Sub.options = mergeOptions(\n      Super.options,\n      extendOptions\n    );\n    Sub['super'] = Super;\n\n    // For props and computed properties, we define the proxy getters on\n    // the Vue instances at extension time, on the extended prototype. This\n    // avoids Object.defineProperty calls for each instance created.\n    if (Sub.options.props) {\n      initProps$1(Sub);\n    }\n    if (Sub.options.computed) {\n      initComputed$1(Sub);\n    }\n\n    // allow further extension/mixin/plugin usage\n    Sub.extend = Super.extend;\n    Sub.mixin = Super.mixin;\n    Sub.use = Super.use;\n\n    // create asset registers, so extended classes\n    // can have their private assets too.\n    ASSET_TYPES.forEach(function (type) {\n      Sub[type] = Super[type];\n    });\n    // enable recursive self-lookup\n    if (name) {\n      Sub.options.components[name] = Sub;\n    }\n\n    // keep a reference to the super options at extension time.\n    // later at instantiation we can check if Super's options have\n    // been updated.\n    Sub.superOptions = Super.options;\n    Sub.extendOptions = extendOptions;\n    Sub.sealedOptions = extend({}, Sub.options);\n\n    // cache constructor\n    cachedCtors[SuperId] = Sub;\n    return Sub\n  };\n}\n\nfunction initProps$1 (Comp) {\n  var props = Comp.options.props;\n  for (var key in props) {\n    proxy(Comp.prototype, \"_props\", key);\n  }\n}\n\nfunction initComputed$1 (Comp) {\n  var computed = Comp.options.computed;\n  for (var key in computed) {\n    defineComputed(Comp.prototype, key, computed[key]);\n  }\n}\n\n/*  */\n\nfunction initAssetRegisters (Vue) {\n  /**\n   * Create asset registration methods.\n   */\n  ASSET_TYPES.forEach(function (type) {\n    Vue[type] = function (\n      id,\n      definition\n    ) {\n      if (!definition) {\n        return this.options[type + 's'][id]\n      } else {\n        /* istanbul ignore if */\n        if (process.env.NODE_ENV !== 'production' && type === 'component') {\n          validateComponentName(id);\n        }\n        if (type === 'component' && isPlainObject(definition)) {\n          definition.name = definition.name || id;\n          definition = this.options._base.extend(definition);\n        }\n        if (type === 'directive' && typeof definition === 'function') {\n          definition = { bind: definition, update: definition };\n        }\n        this.options[type + 's'][id] = definition;\n        return definition\n      }\n    };\n  });\n}\n\n/*  */\n\n\n\nfunction getComponentName (opts) {\n  return opts && (opts.Ctor.options.name || opts.tag)\n}\n\nfunction matches (pattern, name) {\n  if (Array.isArray(pattern)) {\n    return pattern.indexOf(name) > -1\n  } else if (typeof pattern === 'string') {\n    return pattern.split(',').indexOf(name) > -1\n  } else if (isRegExp(pattern)) {\n    return pattern.test(name)\n  }\n  /* istanbul ignore next */\n  return false\n}\n\nfunction pruneCache (keepAliveInstance, filter) {\n  var cache = keepAliveInstance.cache;\n  var keys = keepAliveInstance.keys;\n  var _vnode = keepAliveInstance._vnode;\n  for (var key in cache) {\n    var cachedNode = cache[key];\n    if (cachedNode) {\n      var name = getComponentName(cachedNode.componentOptions);\n      if (name && !filter(name)) {\n        pruneCacheEntry(cache, key, keys, _vnode);\n      }\n    }\n  }\n}\n\nfunction pruneCacheEntry (\n  cache,\n  key,\n  keys,\n  current\n) {\n  var cached$$1 = cache[key];\n  if (cached$$1 && (!current || cached$$1.tag !== current.tag)) {\n    cached$$1.componentInstance.$destroy();\n  }\n  cache[key] = null;\n  remove(keys, key);\n}\n\nvar patternTypes = [String, RegExp, Array];\n\nvar KeepAlive = {\n  name: 'keep-alive',\n  abstract: true,\n\n  props: {\n    include: patternTypes,\n    exclude: patternTypes,\n    max: [String, Number]\n  },\n\n  created: function created () {\n    this.cache = Object.create(null);\n    this.keys = [];\n  },\n\n  destroyed: function destroyed () {\n    for (var key in this.cache) {\n      pruneCacheEntry(this.cache, key, this.keys);\n    }\n  },\n\n  mounted: function mounted () {\n    var this$1 = this;\n\n    this.$watch('include', function (val) {\n      pruneCache(this$1, function (name) { return matches(val, name); });\n    });\n    this.$watch('exclude', function (val) {\n      pruneCache(this$1, function (name) { return !matches(val, name); });\n    });\n  },\n\n  render: function render () {\n    var slot = this.$slots.default;\n    var vnode = getFirstComponentChild(slot);\n    var componentOptions = vnode && vnode.componentOptions;\n    if (componentOptions) {\n      // check pattern\n      var name = getComponentName(componentOptions);\n      var ref = this;\n      var include = ref.include;\n      var exclude = ref.exclude;\n      if (\n        // not included\n        (include && (!name || !matches(include, name))) ||\n        // excluded\n        (exclude && name && matches(exclude, name))\n      ) {\n        return vnode\n      }\n\n      var ref$1 = this;\n      var cache = ref$1.cache;\n      var keys = ref$1.keys;\n      var key = vnode.key == null\n        // same constructor may get registered as different local components\n        // so cid alone is not enough (#3269)\n        ? componentOptions.Ctor.cid + (componentOptions.tag ? (\"::\" + (componentOptions.tag)) : '')\n        : vnode.key;\n      if (cache[key]) {\n        vnode.componentInstance = cache[key].componentInstance;\n        // make current key freshest\n        remove(keys, key);\n        keys.push(key);\n      } else {\n        cache[key] = vnode;\n        keys.push(key);\n        // prune oldest entry\n        if (this.max && keys.length > parseInt(this.max)) {\n          pruneCacheEntry(cache, keys[0], keys, this._vnode);\n        }\n      }\n\n      vnode.data.keepAlive = true;\n    }\n    return vnode || (slot && slot[0])\n  }\n};\n\nvar builtInComponents = {\n  KeepAlive: KeepAlive\n};\n\n/*  */\n\nfunction initGlobalAPI (Vue) {\n  // config\n  var configDef = {};\n  configDef.get = function () { return config; };\n  if (process.env.NODE_ENV !== 'production') {\n    configDef.set = function () {\n      warn(\n        'Do not replace the Vue.config object, set individual fields instead.'\n      );\n    };\n  }\n  Object.defineProperty(Vue, 'config', configDef);\n\n  // exposed util methods.\n  // NOTE: these are not considered part of the public API - avoid relying on\n  // them unless you are aware of the risk.\n  Vue.util = {\n    warn: warn,\n    extend: extend,\n    mergeOptions: mergeOptions,\n    defineReactive: defineReactive$$1\n  };\n\n  Vue.set = set;\n  Vue.delete = del;\n  Vue.nextTick = nextTick;\n\n  // 2.6 explicit observable API\n  Vue.observable = function (obj) {\n    observe(obj);\n    return obj\n  };\n\n  Vue.options = Object.create(null);\n  ASSET_TYPES.forEach(function (type) {\n    Vue.options[type + 's'] = Object.create(null);\n  });\n\n  // this is used to identify the \"base\" constructor to extend all plain-object\n  // components with in Weex's multi-instance scenarios.\n  Vue.options._base = Vue;\n\n  extend(Vue.options.components, builtInComponents);\n\n  initUse(Vue);\n  initMixin$1(Vue);\n  initExtend(Vue);\n  initAssetRegisters(Vue);\n}\n\ninitGlobalAPI(Vue);\n\nObject.defineProperty(Vue.prototype, '$isServer', {\n  get: isServerRendering\n});\n\nObject.defineProperty(Vue.prototype, '$ssrContext', {\n  get: function get () {\n    /* istanbul ignore next */\n    return this.$vnode && this.$vnode.ssrContext\n  }\n});\n\n// expose FunctionalRenderContext for ssr runtime helper installation\nObject.defineProperty(Vue, 'FunctionalRenderContext', {\n  value: FunctionalRenderContext\n});\n\nVue.version = '2.6.11';\n\n/**\n * https://raw.githubusercontent.com/Tencent/westore/master/packages/westore/utils/diff.js\n */\nvar ARRAYTYPE = '[object Array]';\nvar OBJECTTYPE = '[object Object]';\nvar NULLTYPE = '[object Null]';\nvar UNDEFINEDTYPE = '[object Undefined]';\n// const FUNCTIONTYPE = '[object Function]'\n\nfunction diff(current, pre) {\n    var result = {};\n    syncKeys(current, pre);\n    _diff(current, pre, '', result);\n    return result\n}\n\nfunction syncKeys(current, pre) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE && rootPreType == OBJECTTYPE) {\n        if(Object.keys(current).length >= Object.keys(pre).length){\n            for (var key in pre) {\n                var currentValue = current[key];\n                if (currentValue === undefined) {\n                    current[key] = null;\n                } else {\n                    syncKeys(currentValue, pre[key]);\n                }\n            }\n        }\n    } else if (rootCurrentType == ARRAYTYPE && rootPreType == ARRAYTYPE) {\n        if (current.length >= pre.length) {\n            pre.forEach(function (item, index) {\n                syncKeys(current[index], item);\n            });\n        }\n    }\n}\n\nfunction nullOrUndefined(currentType, preType) {\n    if(\n        (currentType === NULLTYPE || currentType === UNDEFINEDTYPE) && \n        (preType === NULLTYPE || preType === UNDEFINEDTYPE)\n    ) {\n        return false\n    }\n    return true\n}\n\nfunction _diff(current, pre, path, result) {\n    if (current === pre) { return }\n    var rootCurrentType = type(current);\n    var rootPreType = type(pre);\n    if (rootCurrentType == OBJECTTYPE) {\n        if (rootPreType != OBJECTTYPE || Object.keys(current).length < Object.keys(pre).length) {\n            setResult(result, path, current);\n        } else {\n            var loop = function ( key ) {\n                var currentValue = current[key];\n                var preValue = pre[key];\n                var currentType = type(currentValue);\n                var preType = type(preValue);\n                if (currentType != ARRAYTYPE && currentType != OBJECTTYPE) {\n                    if (currentValue !== pre[key] && nullOrUndefined(currentType, preType)) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    }\n                } else if (currentType == ARRAYTYPE) {\n                    if (preType != ARRAYTYPE) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        if (currentValue.length < preValue.length) {\n                            setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                        } else {\n                            currentValue.forEach(function (item, index) {\n                                _diff(item, preValue[index], (path == '' ? '' : path + \".\") + key + '[' + index + ']', result);\n                            });\n                        }\n                    }\n                } else if (currentType == OBJECTTYPE) {\n                    if (preType != OBJECTTYPE || Object.keys(currentValue).length < Object.keys(preValue).length) {\n                        setResult(result, (path == '' ? '' : path + \".\") + key, currentValue);\n                    } else {\n                        for (var subKey in currentValue) {\n                            _diff(currentValue[subKey], preValue[subKey], (path == '' ? '' : path + \".\") + key + '.' + subKey, result);\n                        }\n                    }\n                }\n            };\n\n            for (var key in current) loop( key );\n        }\n    } else if (rootCurrentType == ARRAYTYPE) {\n        if (rootPreType != ARRAYTYPE) {\n            setResult(result, path, current);\n        } else {\n            if (current.length < pre.length) {\n                setResult(result, path, current);\n            } else {\n                current.forEach(function (item, index) {\n                    _diff(item, pre[index], path + '[' + index + ']', result);\n                });\n            }\n        }\n    } else {\n        setResult(result, path, current);\n    }\n}\n\nfunction setResult(result, k, v) {\n    // if (type(v) != FUNCTIONTYPE) {\n        result[k] = v;\n    // }\n}\n\nfunction type(obj) {\n    return Object.prototype.toString.call(obj)\n}\n\n/*  */\r\n\r\nfunction flushCallbacks$1(vm) {\r\n    if (vm.__next_tick_callbacks && vm.__next_tick_callbacks.length) {\r\n        if (process.env.VUE_APP_DEBUG) {\r\n            var mpInstance = vm.$scope;\r\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\r\n                ']:flushCallbacks[' + vm.__next_tick_callbacks.length + ']');\r\n        }\r\n        var copies = vm.__next_tick_callbacks.slice(0);\r\n        vm.__next_tick_callbacks.length = 0;\r\n        for (var i = 0; i < copies.length; i++) {\r\n            copies[i]();\r\n        }\r\n    }\r\n}\r\n\r\nfunction hasRenderWatcher(vm) {\r\n    return queue.find(function (watcher) { return vm._watcher === watcher; })\r\n}\r\n\r\nfunction nextTick$1(vm, cb) {\r\n    //1.nextTick 之前 已 setData 且 setData 还未回调完成\r\n    //2.nextTick 之前存在 render watcher\r\n    if (!vm.__next_tick_pending && !hasRenderWatcher(vm)) {\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + vm._uid +\n                ']:nextVueTick');\n        }\r\n        return nextTick(cb, vm)\r\n    }else{\n        if(process.env.VUE_APP_DEBUG){\n            var mpInstance$1 = vm.$scope;\n            console.log('[' + (+new Date) + '][' + (mpInstance$1.is || mpInstance$1.route) + '][' + vm._uid +\n                ']:nextMPTick');\n        }\n    }\r\n    var _resolve;\r\n    if (!vm.__next_tick_callbacks) {\r\n        vm.__next_tick_callbacks = [];\r\n    }\r\n    vm.__next_tick_callbacks.push(function () {\r\n        if (cb) {\r\n            try {\r\n                cb.call(vm);\r\n            } catch (e) {\r\n                handleError(e, vm, 'nextTick');\r\n            }\r\n        } else if (_resolve) {\r\n            _resolve(vm);\r\n        }\r\n    });\r\n    // $flow-disable-line\r\n    if (!cb && typeof Promise !== 'undefined') {\r\n        return new Promise(function (resolve) {\r\n            _resolve = resolve;\r\n        })\r\n    }\r\n}\n\n/*  */\r\n\r\nfunction clearInstance(key, value) {\r\n  // 简易去除 Vue 和小程序组件实例\r\n  if (value) {\r\n    if (value._isVue || value.__v_isMPComponent) {\r\n      return {}\r\n    }\r\n  }\r\n  return value\r\n}\r\n\r\nfunction cloneWithData(vm) {\r\n  // 确保当前 vm 所有数据被同步\r\n  var ret = Object.create(null);\r\n  var dataKeys = [].concat(\r\n    Object.keys(vm._data || {}),\r\n    Object.keys(vm._computedWatchers || {}));\r\n\r\n  dataKeys.reduce(function(ret, key) {\r\n    ret[key] = vm[key];\r\n    return ret\r\n  }, ret);\r\n\r\n  // vue-composition-api\r\n  var compositionApiState = vm.__composition_api_state__ || vm.__secret_vfa_state__;\r\n  var rawBindings = compositionApiState && compositionApiState.rawBindings;\r\n  if (rawBindings) {\r\n    Object.keys(rawBindings).forEach(function (key) {\r\n      ret[key] = vm[key];\r\n    });\r\n  }\r\n\r\n  //TODO 需要把无用数据处理掉，比如 list=>l0 则 list 需要移除，否则多传输一份数据\r\n  Object.assign(ret, vm.$mp.data || {});\r\n  if (\r\n    Array.isArray(vm.$options.behaviors) &&\r\n    vm.$options.behaviors.indexOf('uni://form-field') !== -1\r\n  ) { //form-field\r\n    ret['name'] = vm.name;\r\n    ret['value'] = vm.value;\r\n  }\r\n\r\n  return JSON.parse(JSON.stringify(ret, clearInstance))\r\n}\r\n\r\nvar patch = function(oldVnode, vnode) {\n  var this$1 = this;\n\r\n  if (vnode === null) { //destroy\r\n    return\r\n  }\r\n  if (this.mpType === 'page' || this.mpType === 'component') {\r\n    var mpInstance = this.$scope;\r\n    var data = Object.create(null);\r\n    try {\r\n      data = cloneWithData(this);\r\n    } catch (err) {\r\n      console.error(err);\r\n    }\r\n    data.__webviewId__ = mpInstance.data.__webviewId__;\r\n    var mpData = Object.create(null);\r\n    Object.keys(data).forEach(function (key) { //仅同步 data 中有的数据\r\n      mpData[key] = mpInstance.data[key];\r\n    });\r\n    var diffData = this.$shouldDiffData === false ? data : diff(data, mpData);\r\n    if (Object.keys(diffData).length) {\r\n      if (process.env.VUE_APP_DEBUG) {\r\n        console.log('[' + (+new Date) + '][' + (mpInstance.is || mpInstance.route) + '][' + this._uid +\r\n          ']差量更新',\r\n          JSON.stringify(diffData));\r\n      }\r\n      this.__next_tick_pending = true;\r\n      mpInstance.setData(diffData, function () {\r\n        this$1.__next_tick_pending = false;\r\n        flushCallbacks$1(this$1);\r\n      });\r\n    } else {\r\n      flushCallbacks$1(this);\r\n    }\r\n  }\r\n};\n\n/*  */\n\nfunction createEmptyRender() {\n\n}\n\nfunction mountComponent$1(\n  vm,\n  el,\n  hydrating\n) {\n  if (!vm.mpType) {//main.js 中的 new Vue\n    return vm\n  }\n  if (vm.mpType === 'app') {\n    vm.$options.render = createEmptyRender;\n  }\n  if (!vm.$options.render) {\n    vm.$options.render = createEmptyRender;\n    if (process.env.NODE_ENV !== 'production') {\n      /* istanbul ignore if */\n      if ((vm.$options.template && vm.$options.template.charAt(0) !== '#') ||\n        vm.$options.el || el) {\n        warn(\n          'You are using the runtime-only build of Vue where the template ' +\n          'compiler is not available. Either pre-compile the templates into ' +\n          'render functions, or use the compiler-included build.',\n          vm\n        );\n      } else {\n        warn(\n          'Failed to mount component: template or render function not defined.',\n          vm\n        );\n      }\n    }\n  }\n  \n  !vm._$fallback && callHook(vm, 'beforeMount');\n\n  var updateComponent = function () {\n    vm._update(vm._render(), hydrating);\n  };\n\n  // we set this to vm._watcher inside the watcher's constructor\n  // since the watcher's initial patch may call $forceUpdate (e.g. inside child\n  // component's mounted hook), which relies on vm._watcher being already defined\n  new Watcher(vm, updateComponent, noop, {\n    before: function before() {\n      if (vm._isMounted && !vm._isDestroyed) {\n        callHook(vm, 'beforeUpdate');\n      }\n    }\n  }, true /* isRenderWatcher */);\n  hydrating = false;\n  return vm\n}\n\n/*  */\n\nfunction renderClass (\n  staticClass,\n  dynamicClass\n) {\n  if (isDef(staticClass) || isDef(dynamicClass)) {\n    return concat(staticClass, stringifyClass(dynamicClass))\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction concat (a, b) {\n  return a ? b ? (a + ' ' + b) : a : (b || '')\n}\n\nfunction stringifyClass (value) {\n  if (Array.isArray(value)) {\n    return stringifyArray(value)\n  }\n  if (isObject(value)) {\n    return stringifyObject(value)\n  }\n  if (typeof value === 'string') {\n    return value\n  }\n  /* istanbul ignore next */\n  return ''\n}\n\nfunction stringifyArray (value) {\n  var res = '';\n  var stringified;\n  for (var i = 0, l = value.length; i < l; i++) {\n    if (isDef(stringified = stringifyClass(value[i])) && stringified !== '') {\n      if (res) { res += ' '; }\n      res += stringified;\n    }\n  }\n  return res\n}\n\nfunction stringifyObject (value) {\n  var res = '';\n  for (var key in value) {\n    if (value[key]) {\n      if (res) { res += ' '; }\n      res += key;\n    }\n  }\n  return res\n}\n\n/*  */\n\nvar parseStyleText = cached(function (cssText) {\n  var res = {};\n  var listDelimiter = /;(?![^(]*\\))/g;\n  var propertyDelimiter = /:(.+)/;\n  cssText.split(listDelimiter).forEach(function (item) {\n    if (item) {\n      var tmp = item.split(propertyDelimiter);\n      tmp.length > 1 && (res[tmp[0].trim()] = tmp[1].trim());\n    }\n  });\n  return res\n});\n\n// normalize possible array / string values into Object\nfunction normalizeStyleBinding (bindingStyle) {\n  if (Array.isArray(bindingStyle)) {\n    return toObject(bindingStyle)\n  }\n  if (typeof bindingStyle === 'string') {\n    return parseStyleText(bindingStyle)\n  }\n  return bindingStyle\n}\n\n/*  */\r\n\r\nvar MP_METHODS = ['createSelectorQuery', 'createIntersectionObserver', 'selectAllComponents', 'selectComponent'];\r\n\r\nfunction getTarget(obj, path) {\r\n  var parts = path.split('.');\r\n  var key = parts[0];\r\n  if (key.indexOf('__$n') === 0) { //number index\r\n    key = parseInt(key.replace('__$n', ''));\r\n  }\r\n  if (parts.length === 1) {\r\n    return obj[key]\r\n  }\r\n  return getTarget(obj[key], parts.slice(1).join('.'))\r\n}\r\n\r\nfunction internalMixin(Vue) {\r\n\r\n  Vue.config.errorHandler = function(err, vm, info) {\r\n    Vue.util.warn((\"Error in \" + info + \": \\\"\" + (err.toString()) + \"\\\"\"), vm);\r\n    console.error(err);\r\n    /* eslint-disable no-undef */\r\n    var app = typeof getApp === 'function' && getApp();\r\n    if (app && app.onError) {\r\n      app.onError(err);\r\n    }\r\n  };\r\n\r\n  var oldEmit = Vue.prototype.$emit;\r\n\r\n  Vue.prototype.$emit = function(event) {\r\n    if (this.$scope && event) {\r\n      var triggerEvent = this.$scope['_triggerEvent'] || this.$scope['triggerEvent'];\r\n      if (triggerEvent) {\r\n        try {\r\n          triggerEvent.call(this.$scope, event, {\r\n            __args__: toArray(arguments, 1)\r\n          });\r\n        } catch (error) {\r\n\r\n        }\r\n      }\r\n    }\r\n    return oldEmit.apply(this, arguments)\r\n  };\r\n\r\n  Vue.prototype.$nextTick = function(fn) {\r\n    return nextTick$1(this, fn)\r\n  };\r\n\r\n  MP_METHODS.forEach(function (method) {\r\n    Vue.prototype[method] = function(args) {\r\n      if (this.$scope && this.$scope[method]) {\r\n        return this.$scope[method](args)\r\n      }\r\n      // mp-alipay\r\n      if (typeof my === 'undefined') {\r\n        return\r\n      }\r\n      if (method === 'createSelectorQuery') {\r\n        /* eslint-disable no-undef */\r\n        return my.createSelectorQuery(args)\r\n      } else if (method === 'createIntersectionObserver') {\r\n        /* eslint-disable no-undef */\r\n        return my.createIntersectionObserver(args)\r\n      }\r\n      // TODO mp-alipay 暂不支持 selectAllComponents,selectComponent\r\n    };\r\n  });\r\n\r\n  Vue.prototype.__init_provide = initProvide;\r\n\r\n  Vue.prototype.__init_injections = initInjections;\r\n\r\n  Vue.prototype.__call_hook = function(hook, args) {\r\n    var vm = this;\r\n    // #7573 disable dep collection when invoking lifecycle hooks\r\n    pushTarget();\r\n    var handlers = vm.$options[hook];\r\n    var info = hook + \" hook\";\r\n    var ret;\r\n    if (handlers) {\r\n      for (var i = 0, j = handlers.length; i < j; i++) {\r\n        ret = invokeWithErrorHandling(handlers[i], vm, args ? [args] : null, vm, info);\r\n      }\r\n    }\r\n    if (vm._hasHookEvent) {\r\n      vm.$emit('hook:' + hook, args);\r\n    }\r\n    popTarget();\r\n    return ret\r\n  };\r\n\r\n  Vue.prototype.__set_model = function(target, key, value, modifiers) {\r\n    if (Array.isArray(modifiers)) {\r\n      if (modifiers.indexOf('trim') !== -1) {\r\n        value = value.trim();\r\n      }\r\n      if (modifiers.indexOf('number') !== -1) {\r\n        value = this._n(value);\r\n      }\r\n    }\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__set_sync = function(target, key, value) {\r\n    if (!target) {\r\n      target = this;\r\n    }\r\n    // 解决动态属性添加\r\n    Vue.set(target, key, value);\r\n  };\r\n\r\n  Vue.prototype.__get_orig = function(item) {\r\n    if (isPlainObject(item)) {\r\n      return item['$orig'] || item\r\n    }\r\n    return item\r\n  };\r\n\r\n  Vue.prototype.__get_value = function(dataPath, target) {\r\n    return getTarget(target || this, dataPath)\r\n  };\r\n\r\n\r\n  Vue.prototype.__get_class = function(dynamicClass, staticClass) {\r\n    return renderClass(staticClass, dynamicClass)\r\n  };\r\n\r\n  Vue.prototype.__get_style = function(dynamicStyle, staticStyle) {\r\n    if (!dynamicStyle && !staticStyle) {\r\n      return ''\r\n    }\r\n    var dynamicStyleObj = normalizeStyleBinding(dynamicStyle);\r\n    var styleObj = staticStyle ? extend(staticStyle, dynamicStyleObj) : dynamicStyleObj;\r\n    return Object.keys(styleObj).map(function (name) { return ((hyphenate(name)) + \":\" + (styleObj[name])); }).join(';')\r\n  };\r\n\r\n  Vue.prototype.__map = function(val, iteratee) {\r\n    //TODO 暂不考虑 string\r\n    var ret, i, l, keys, key;\r\n    if (Array.isArray(val)) {\r\n      ret = new Array(val.length);\r\n      for (i = 0, l = val.length; i < l; i++) {\r\n        ret[i] = iteratee(val[i], i);\r\n      }\r\n      return ret\r\n    } else if (isObject(val)) {\r\n      keys = Object.keys(val);\r\n      ret = Object.create(null);\r\n      for (i = 0, l = keys.length; i < l; i++) {\r\n        key = keys[i];\r\n        ret[key] = iteratee(val[key], key, i);\r\n      }\r\n      return ret\r\n    } else if (typeof val === 'number') {\r\n      ret = new Array(val);\r\n      for (i = 0, l = val; i < l; i++) {\r\n        // 第一个参数暂时仍和小程序一致\r\n        ret[i] = iteratee(i, i);\r\n      }\r\n      return ret\r\n    }\r\n    return []\r\n  };\r\n\r\n}\n\n/*  */\r\n\r\nvar LIFECYCLE_HOOKS$1 = [\r\n    //App\r\n    'onLaunch',\r\n    'onShow',\r\n    'onHide',\r\n    'onUniNViewMessage',\r\n    'onPageNotFound',\r\n    'onThemeChange',\r\n    'onError',\r\n    'onUnhandledRejection',\r\n    //Page\r\n    'onInit',\r\n    'onLoad',\r\n    // 'onShow',\r\n    'onReady',\r\n    // 'onHide',\r\n    'onUnload',\r\n    'onPullDownRefresh',\r\n    'onReachBottom',\r\n    'onTabItemTap',\r\n    'onAddToFavorites',\r\n    'onShareTimeline',\r\n    'onShareAppMessage',\r\n    'onResize',\r\n    'onPageScroll',\r\n    'onNavigationBarButtonTap',\r\n    'onBackPress',\r\n    'onNavigationBarSearchInputChanged',\r\n    'onNavigationBarSearchInputConfirmed',\r\n    'onNavigationBarSearchInputClicked',\r\n    'onUploadDouyinVideo',\r\n    'onNFCReadMessage',\r\n    //Component\r\n    // 'onReady', // 兼容旧版本，应该移除该事件\r\n    'onPageShow',\r\n    'onPageHide',\r\n    'onPageResize'\r\n];\r\nfunction lifecycleMixin$1(Vue) {\r\n\r\n    //fixed vue-class-component\r\n    var oldExtend = Vue.extend;\r\n    Vue.extend = function(extendOptions) {\r\n        extendOptions = extendOptions || {};\r\n\r\n        var methods = extendOptions.methods;\r\n        if (methods) {\r\n            Object.keys(methods).forEach(function (methodName) {\r\n                if (LIFECYCLE_HOOKS$1.indexOf(methodName)!==-1) {\r\n                    extendOptions[methodName] = methods[methodName];\r\n                    delete methods[methodName];\r\n                }\r\n            });\r\n        }\r\n\r\n        return oldExtend.call(this, extendOptions)\r\n    };\r\n\r\n    var strategies = Vue.config.optionMergeStrategies;\r\n    var mergeHook = strategies.created;\r\n    LIFECYCLE_HOOKS$1.forEach(function (hook) {\r\n        strategies[hook] = mergeHook;\r\n    });\r\n\r\n    Vue.prototype.__lifecycle_hooks__ = LIFECYCLE_HOOKS$1;\r\n}\n\n/*  */\r\n\n// install platform patch function\r\nVue.prototype.__patch__ = patch;\r\n\r\n// public mount method\r\nVue.prototype.$mount = function(\r\n    el ,\r\n    hydrating \r\n) {\r\n    return mountComponent$1(this, el, hydrating)\r\n};\r\n\r\nlifecycleMixin$1(Vue);\r\ninternalMixin(Vue);\n\n/*  */\n\nexport default Vue;\n", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode, /* vue-cli only */\n  components, // fixed by xxxxxx auto components\n  renderjs // fixed by xxxxxx renderjs\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // fixed by xxxxxx auto components\n  if (components) {\n    if (!options.components) {\n      options.components = {}\n    }\n    var hasOwn = Object.prototype.hasOwnProperty\n    for (var name in components) {\n      if (hasOwn.call(components, name) && !hasOwn.call(options.components, name)) {\n        options.components[name] = components[name]\n      }\n    }\n  }\n  // fixed by xxxxxx renderjs\n  if (renderjs) {\n    if(typeof renderjs.beforeCreate === 'function'){\n\t\t\trenderjs.beforeCreate = [renderjs.beforeCreate]\n\t\t}\n    (renderjs.beforeCreate || (renderjs.beforeCreate = [])).unshift(function() {\n      this[renderjs.__module] = this\n    });\n    (options.mixins || (options.mixins = [])).push(renderjs)\n  }\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () { injectStyles.call(this, this.$root.$options.shadowRoot) }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functioal component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n"], "sourceRoot": ""}