<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>websocket GBK字符转换utf</title>
</head>

<body>
    <div id="aa"></div>
    <script language="javascript" src="gbk2uni_array.js"> </script>
    <script language="javascript">

        function Utf8ToUnicode(strUtf8) {
            var i, j;
            var uCode;
            var temp = new Array();
            for (i = 0, j = 0; i < strUtf8.length; i++) {
                uCode = strUtf8.charCodeAt(i);
                if (uCode < 0x100) {         //ASCII字符 
                    temp[j++] = 0x00;
                    temp[j++] = uCode;
                } else if (uCode < 0x10000) {
                    temp[j++] = (uCode >> 8) & 0xff;
                    temp[j++] = uCode & 0xff;
                } else if (uCode < 0x1000000) {
                    temp[j++] = (uCode >> 16) & 0xff;
                    temp[j++] = (uCode >> 8) & 0xff;
                    temp[j++] = uCode & 0xff;
                } else if (uCode < 0x100000000) {
                    temp[j++] = (uCode >> 24) & 0xff;
                    temp[j++] = (uCode >> 16) & 0xff;
                    temp[j++] = (uCode >> 8) & 0xff;
                    temp[j++] = uCode & 0xff;
                } else {
                    break;
                }
            }
            temp.length = j;
            return temp;
        }

        //16进制字符转纯数字，如A转成10 16进制字符10=16
        this.Str2Hex = function (s) {
            var c = "";
            var n = 0;
            var ss = "0123456789ABCDEF";

            //根据长度移动位置D6B0,字符D就向左移动24位
            for (var i = 0; i < s.length; i++) {
                c = s.charAt(i);
                n |= (ss.indexOf(c)) << ((s.length - 1 - i) * 4);
            }
            return n;
        }

        //从文本码表创建码表数组
        // var fso, ts, s ; 
        // var ForReading = 1; 

        // fso = new ActiveXObject("Scripting.FileSystemObject"); 
        // ts = fso.OpenTextFile(".\\gbkuni30.txt", ForReading); 

        // var s="";
        // var i=0;
        // var gbk2uni_array=new Array(65535);
        // var uni2gbk_array=new Array(65535);

        // while (!ts.AtEndOfStream && i<0) 
        // { 
        //     s = ts.ReadLine(); 
        //     var value = s.split(":");
        //     var gbk_value = Str2Hex(value[1]);
        //     var uni_Value = Str2Hex(value[0]);

        //     gbk2uni_array[gbk_value]="0x"+value[0];
        //     uni2gbk_array[uni_Value]="0x"+value[1];

        //     i++;
        // } 
        // ts.Close();

        // ts2 = fso.CreateTextFile("d:\\gbk2uni_array.js", true);
        // ts2.Write("var gbk2uni_array=["+gbk2uni_array.toString()+"];");
        // ts2.Close();

        // ts2 = fso.CreateTextFile("d:\\uni2gbk_array.js", true);
        // ts2.Write("var uni2gbk_array=["+uni2gbk_array.toString()+"];");
        // ts2.Close();

        function gbk2unicodeUint16Array(gbkArrayBuffer){
            var i;
            var j;
            
            //必须转换成无符号
            var gb_temp=new Uint8Array(gbkArrayBuffer);
            var gb_temp16=new Uint16Array(gbkArrayBuffer);      
            var unicode=new Uint16Array(gbkArrayBuffer.byteLength);
           
            i=0;
            for (j = 0; i < gb_temp.byteLength; j++) {
               
                if (gb_temp[i] <= 0x80) {
                    //0值无数据
                    if (gb_temp[i]===0)break;

                    //正常字符
                    unicode[j] = gb_temp[i];
                    i++;
                }
                else {
                    //中文或者其他字符
                    var temp;
                    //alert(gb_temp[i]+" "+gb_temp[i+1]+" "+gb_temp16[0]);

                    temp = (gb_temp[i]<<8)|gb_temp[i+1];//两个字节合成一个值

                    //alert(temp);
                    unicode[j] = gbk2uni_array[temp];//得到unincode值
                    i += 2;
                }
            }

            return unicode.subarray(0,j);;
        }

        //
        function UnicodeToUtf8(Uint16Arraybuffer) {
            var uchar;
            var utf8str = "";
            var bUint16Array = false;
           
            return String.fromCharCode.apply(null, new Uint16Array(Uint16Arraybuffer));
            
            //decoder = new TextDecoder(encoding);utf8str = decoder.decode(ArrayBuffer, 'gbk');//
            for (var i = 0; i < Uint16Arraybuffer.length; i ++) {
                uchar = (Uint16Arraybuffer[i]);
               
                //return String.fromCharCode.apply(null, new Uint16Array(Uint16Arraybuffer));
                utf8str = utf8str + String.fromCharCode(uchar);  //使用String.fromCharCode强制转换 Unicode 为中文字符串
            }
            return utf8str;
        }

        //测试映射表
        var unicodevalue = gbk2uni_array[0xD6D0];

        //unicodebf
        var unicode_arbf = new ArrayBuffer(2);
        var unicode_16buf = new Uint16Array(unicode_arbf);
        //var dv = new DataView(unicode_arbf);
        //dv.setUint16(0, unicodevalue,true);
        unicode_16buf[0]=unicodevalue;
        alert(unicode_16buf);
        alert(UnicodeToUtf8(unicode_16buf));
        
        //GBK码 
        var gbk_arraybuffer = new ArrayBuffer(2);
        var dv2 = new DataView(gbk_arraybuffer);
        dv2.setUint16(0, 0xD6D0); //(模拟SOCKET原始数据"中"字)

        //转unicode码
        var unicode_arraybf= gbk2unicodeUint16Array(gbk_arraybuffer);

        alert(unicode_arraybf);
        
        //unicode转utf8
        var ch = UnicodeToUtf8(unicode_arraybf);

        document.getElementById("aa").innerHTML = "GBK = 0XD6D0 <br>GBK(10进制) = "+0xD6D0+" <br>unicode(10进制) = "+unicode_arraybf+" <br>utf中文 = "+ch;
    </script>

</body>

</html>
