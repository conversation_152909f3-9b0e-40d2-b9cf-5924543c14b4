@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.content.data-v-57280228 {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}
/* 按钮样式 */
.btn.data-v-57280228 {
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.btn.data-v-57280228:disabled {
  opacity: 0.5;
  -webkit-transform: none !important;
          transform: none !important;
  box-shadow: none !important;
}
.btn.data-v-57280228:not(:disabled):active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.btn-primary.data-v-57280228 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.btn-success.data-v-57280228 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}
.btn-warning.data-v-57280228 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}
.btn-danger.data-v-57280228 {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #333;
}
.btn-query.data-v-57280228 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #333;
  font-size: 24rpx;
  padding: 15rpx 20rpx;
}
.btn-control.data-v-57280228 {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #333;
  font-size: 24rpx;
  padding: 15rpx 20rpx;
}
.btn-test.data-v-57280228 {
  background: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);
  color: white;
}
.btn-connect.data-v-57280228 {
  background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  color: #333;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
}
.btn-send.data-v-57280228 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: #333;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  min-width: 120rpx;
}
.btn-clear.data-v-57280228 {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  color: #333;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
}
.btn-toggle.data-v-57280228 {
  background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  color: #333;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  min-width: 200rpx;
}
.btn-toggle.active.data-v-57280228 {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.3);
}
.btn-gear.data-v-57280228 {
  background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
  color: #333;
  font-size: 26rpx;
  padding: 18rpx 30rpx;
  min-width: 120rpx;
}
.btn-gear.active.data-v-57280228 {
  background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
  color: white;
  box-shadow: 0 6rpx 20rpx rgba(9, 132, 227, 0.3);
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
/* 卡片样式 */
.status-card.data-v-57280228, .device-list-card.data-v-57280228, .data-card.data-v-57280228, .command-card.data-v-57280228, .air-quality-card.data-v-57280228 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  overflow: hidden;
}
/* 卡片头部 */
.card-header.data-v-57280228, .status-header.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.card-title.data-v-57280228, .status-title.data-v-57280228 {
  font-size: 32rpx;
  font-weight: bold;
}
.device-count.data-v-57280228 {
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
/* 状态指示器 */
.status-indicator.data-v-57280228 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}
.status-indicator.connected.data-v-57280228 {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}
.status-indicator.disconnected.data-v-57280228 {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}
/* 状态详情 */
.status-details.data-v-57280228 {
  padding: 30rpx;
}
.status-item.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.status-item.data-v-57280228:last-child {
  border-bottom: none;
}
.status-label.data-v-57280228 {
  font-size: 28rpx;
  color: #666;
}
.status-value.data-v-57280228 {
  font-size: 28rpx;
  font-weight: 500;
}
.status-value.success.data-v-57280228 {
  color: #4CAF50;
}
.status-value.error.data-v-57280228 {
  color: #F44336;
}
.status-value.warning.data-v-57280228 {
  color: #FF9800;
}
.status-value.info.data-v-57280228 {
  color: #2196F3;
}
/* 按钮组 */
.button-group.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
/* 调试按钮组 */
.debug-button-group.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 15rpx;
  border: 2rpx dashed #ccc;
}
.btn-debug.data-v-57280228 {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  font-size: 24rpx;
  padding: 15rpx 25rpx;
  margin: 5rpx;
  min-width: 140rpx;
}
/* 设备列表 */
.device-scroll.data-v-57280228 {
  height: 300rpx;
  padding: 20rpx;
}
.device-item.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #667eea;
}
.device-info.data-v-57280228 {
  flex: 1;
}
.device-name.data-v-57280228 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.device-id.data-v-57280228 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
/* 数据显示 */
.data-scroll.data-v-57280228 {
  height: 400rpx;
  padding: 20rpx;
}
.message-item.data-v-57280228 {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #4CAF50;
}
.message-text.data-v-57280228 {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}
/* 命令组 */
.command-group.data-v-57280228 {
  margin-bottom: 40rpx;
  padding: 30rpx;
}
.group-title.data-v-57280228 {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #667eea;
}
.command-buttons.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
/* 参数控制 */
.param-controls.data-v-57280228 {
  margin-top: 30rpx;
}
.control-item.data-v-57280228 {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.control-label.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 15rpx;
}
.control-input.data-v-57280228, .control-input-group.data-v-57280228 {
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.control-input-group.data-v-57280228 {
  flex-wrap: wrap;
}
.input-field.data-v-57280228 {
  flex: 1;
  min-width: 200rpx;
  padding: 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background: white;
}
.input-field.data-v-57280228:focus {
  border-color: #667eea;
  outline: none;
}
/* 快捷控制 */
.quick-control-section.data-v-57280228 {
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15rpx;
  border-left: 6rpx solid #667eea;
}
.control-section-title.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 20rpx;
}
.toggle-buttons.data-v-57280228 {
  display: flex;
  justify-content: center;
}
.gear-buttons.data-v-57280228 {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: space-between;
}
/* 空状态 */
.empty-state.data-v-57280228 {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 28rpx;
}
/* 空气质量卡片样式 */
.air-quality-content.data-v-57280228 {
  padding: 30rpx;
}
.air-quality-main.data-v-57280228 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 25rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15rpx;
}
.air-quality-icon.data-v-57280228 {
  font-size: 60rpx;
  margin-right: 25rpx;
}
.air-quality-info.data-v-57280228 {
  flex: 1;
}
.air-quality-level.data-v-57280228 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.air-quality-desc.data-v-57280228 {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.sensor-data-grid.data-v-57280228 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.sensor-item.data-v-57280228 {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  text-align: center;
  border-left: 4rpx solid #667eea;
}
.sensor-label.data-v-57280228 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.sensor-value.data-v-57280228 {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.sensor-actions.data-v-57280228 {
  text-align: center;
}
.btn-refresh.data-v-57280228, .btn-sensor.data-v-57280228 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  min-width: 120rpx;
}
