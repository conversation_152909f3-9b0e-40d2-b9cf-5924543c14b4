# 汽车空调滤芯净化器蓝牙通信协议

## 一、基本信息

### 1.1 蓝牙版本
- 蓝牙4.0 BLE

### 1.2 UUID定义
- 服务UUID: 0xFF00
- 特征值UUID（读写）: 0xFF01
- 特征值UUID（通知）: 0xFF02

### 1.3 连接参数
- MTU: 23字节
- 连接间隔: 200ms-1000ms（优化以降低功耗）
- 超时时间: 6000ms（延长以提高稳定性）

### 1.4 车载环境BLE配置
- 工作温度范围: -40°C ~ +85°C
- 抗干扰配置:
  - 频率跳跃: 启用（79个信道）
  - 共存机制: 与车载WiFi/GPS协调
  - 信道屏蔽: 自动屏蔽干扰信道
- 传输功率: 0dBm（可调节-20dBm ~ +4dBm）

### 1.5 电源管理配置
- 低功耗模式:
  - 深度睡眠: 支持
  - 快速唤醒: <100ms
  - 待机电流: <10μA
- 电源监控:
  - ACC电源检测: 支持
  - 电池电压监控: 实时
  - 低压保护阈值: 10.6V

### 1.6 安全配置
- 加密级别: AES-128
- 配对模式: Just Works / Passkey
- 认证要求: 无需认证（车载环境）
- 设备绑定: MAC地址绑定

### 1.7 数据传输优化
- 数据压缩: 启用（关键数据）
- 批量传输: 支持（历史数据）
- 优先级队列:
  - 高优先级: 保护告警、紧急控制
  - 中优先级: 实时数据、状态查询
  - 低优先级: 历史数据、配置信息
- 流控制: 启用（防止数据丢失）

### 1.8 连接稳定性配置
- 重连策略:
  - 自动重连: 启用
  - 重连间隔: 5秒
  - 最大重试: 3次
- 信号强度监控:
  - RSSI阈值: -80dBm
  - 信号质量评估: 实时
- 连接参数动态调整:
  - 根据信号质量调整间隔
  - 根据数据量调整MTU

## 二、协议格式

### 2.1 帧结构
每个数据包由以下部分组成：

| 字段     | 长度(字节) | 说明                         |
|----------|------------|------------------------------|
| 帧头     | 2          | 固定为 0xAA 0x55             |
| 命令字   | 1          | 定义命令类型                 |
| 数据长度 | 1          | 数据区长度(不包括校验和)     |
| 数据区   | N (N≤16)   | 数据内容，根据命令类型确定   |
| 校验和   | 1          | CRC-8校验值（替代简单累加和）|

### 2.2 校验和计算方法
```c
// CRC-8校验算法，比简单累加和更可靠
uint8_t crc8(uint8_t *data, uint8_t length) {
    uint8_t crc = 0;
    for (uint8_t i = 0; i < length; i++) {
        crc ^= data[i];
        for (uint8_t j = 0; j < 8; j++) {
            if (crc & 0x80) {
                crc = (crc << 1) ^ 0x07;
            } else {
                crc <<= 1;
            }
        }
    }
    return crc;
}
```

## 三、命令定义

### 3.1 查询命令 (小程序发送至设备)

#### 3.1.1 查询设备状态 (0x01)
- 数据区: 无
- 响应: 0x81

#### 3.1.2 查询传感器数据 (0x02)
- 数据区: 无
- 响应: 0x82

#### 3.1.3 查询保养信息 (0x03)
- 数据区: 无
- 响应: 0x83

#### 3.1.4 查询电气参数 (0x04)
- 数据区: 无
- 响应: 0x84

#### 3.1.5 查询保护状态 (0x05)
- 数据区: 无
- 响应: 0x85

#### 3.1.6 查询空气质量 (0x06)
- 数据区: 无
- 响应: 0x86

#### 3.1.7 心跳包 (0x07)（新增）
- 数据区: 无
- 响应: 0x87

### 3.2 控制命令 (小程序发送至设备)

#### 3.2.1 设置挡位 (0x11)
- 数据区: 1字节
  - 0x00: 关闭
  - 0x01: 低挡
  - 0x02: 中挡
  - 0x03: 高挡
- 响应: 0x91

#### 3.2.2 控制蜂鸣器 (0x12)
- 数据区: 1字节
  - 0x00: 关闭
  - 0x01: 打开
- 响应: 0x92

#### 3.2.3 复位设备 (0x13)
- 数据区: 1字节
  - 0x01: 复位设备
- 响应: 0x93

#### 3.2.4 复位计时器 (0x14)
- 数据区: 1字节
  - 0x01: 复位计时器
- 响应: 0x94

#### 3.2.5 复位保护状态 (0x15)
- 数据区: 1字节
  - 0x01: 复位过流保护
  - 0x02: 复位短路保护
  - 0x03: 复位低压保护
  - 0xFF: 复位所有保护
- 响应: 0x95

#### 3.2.6 档位控制 (0x16)
- 数据区: 1字节
  - 0x00: 关闭
  - 0x01: 1档（低速）
  - 0x02: 2档
  - 0x03: 3档
  - 0x04: 4档
  - 0x05: 5档（高速）
  - 0x06: 自动档
- 响应: 0x96

#### 3.2.7 蜂鸣器高级控制 (0x17)
- 数据区: 4字节
  - 字节1-2: 频率 (Hz, 1000-5000)
  - 字节3-4: 持续时间 (ms, 100-5000)
- 响应: 0x97

#### 3.2.8 负离子计数器复位 (0x18)
- 数据区: 1字节
  - 0x01: 复位计数器
- 响应: 0x98

### 3.3 响应命令 (设备发送至小程序)

#### 3.3.1 设备状态响应 (0x81)
- 数据区: 2字节
  - 字节1: 工作状态
    - Bit0: 开关状态 (0:关闭, 1:打开)
    - Bit1-2: 挡位 (00:关闭, 01:低挡, 10:中挡, 11:高挡)
    - Bit3: 蜂鸣器状态 (0:关闭, 1:打开)
    - Bit4: 运行异常 (0:正常, 1:异常)
    - Bit5-7: 保留
  - 字节2: 设备模式
    - Bit0-1: 工作模式 (00:正常模式, 01:节能模式, 10:强力模式)
    - Bit2-7: 保留

#### 3.3.2 传感器数据响应 (0x82)
- 数据区: 10字节
  - 字节1-2: PM2.5数值 (单位: μg/m³)
  - 字节3-4: VOC数值 (单位: ppm)
  - 字节5-6: 负离子浓度 (单位: 个/cm³)
  - 字节7-8: 温度 (单位: 0.1℃)
  - 字节9-10: 湿度 (单位: 0.1%RH)

#### 3.3.3 保养信息响应 (0x83)
- 数据区: 4字节
  - 字节1-4: 已使用时间 (单位: 小时)

#### 3.3.4 电气参数响应 (0x84)
- 数据区: 6字节
  - 字节1-2: 电压 (单位: 0.01V)
  - 字节3-4: 电流 (单位: 0.001A)
  - 字节5-6: 功率 (单位: 0.1W)

#### 3.3.5 保护状态响应 (0x85)
- 数据区: 2字节
  - 字节1: 保护状态
    - Bit0: 短路保护 (0:未触发, 1:已触发)
    - Bit1: 过流保护 (0:未触发, 1:已触发)
    - Bit2: 低压保护 (0:未触发, 1:已触发)
    - Bit3: 过温保护 (0:未触发, 1:已触发)
    - Bit4-7: 保留
  - 字节2: 保留

#### 3.3.6 空气质量响应 (0x86)
- 数据区: 2字节
  - 字节1: 空气质量等级
    - 0x01: 优
    - 0x02: 良
    - 0x03: 轻度污染
    - 0x04: 中度污染
    - 0x05: 重度污染
  - 字节2: 保留

#### 3.3.7 心跳响应 (0x87)（新增）
- 数据区: 无

#### 3.3.8 控制命令响应 (0x91, 0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98)
- 数据区: 1字节
  - 0x00: 失败
  - 0x01: 成功

#### 3.3.9 负离子数据响应 (0x89)
- 数据区: 6字节
  - 字节1-4: 负离子计数值 (个/cm³)
  - 字节5: 传感器状态 (0:不支持, 1:正常, 2:故障)
  - 字节6: 保留

#### 3.3.10 设备工作状态详细响应 (0x8A)
- 数据区: 8字节
  - 字节1: 工作状态判断
    - 0x00: 停机状态
    - 0x01: 正常工作
    - 0x02: 待机状态
    - 0x03: 保护状态
    - 0x04: 故障状态
  - 字节2: 电压状态 (0:异常, 1:正常)
  - 字节3: 电流状态 (0:异常, 1:正常)
  - 字节4-7: 累计工作时间 (秒)

#### 3.3.9 错误响应 (0x8F)（新增）
- 数据区: 2字节
  - 字节1: 原始命令字
  - 字节2: 错误代码
    - 0x01: 参数错误
    - 0x02: 命令不支持
    - 0x03: 硬件错误

### 3.4 通知命令 (设备主动发送至小程序)

#### 3.4.1 设备状态变化通知 (0xA1)
- 数据区: 与0x81相同

#### 3.4.2 传感器数据变化通知 (0xA2)
- 数据区: 与0x82相同

#### 3.4.3 保养提醒通知 (0xA3)
- 数据区: 1字节
  - 0x01: 需要更换滤芯

#### 3.4.4 保护触发通知 (0xA5)
- 数据区: 2字节
  - 字节1: 保护类型
    - 0x01: 短路保护
    - 0x02: 过流保护
    - 0x03: 低压保护
    - 0x04: 过温保护
  - 字节2: 保留

#### 3.4.5 空气质量变化通知 (0xA6)
- 数据区: 与0x86相同

## 四、通信流程

### 4.1 设备发现
1. 小程序扫描周围BLE设备
2. 设备广播名称为"AirPurifier-XXXX"（XXXX为设备ID后4位）
3. 小程序通过设备名过滤目标设备

### 4.2 设备连接
1. 小程序连接目标设备
2. 连接成功后发现服务
3. 启用通知服务

### 4.3 数据交互
1. 小程序可以发送查询或控制命令
2. 设备响应命令并返回相关数据
3. 设备在状态变化时主动发送通知
4. 小程序每30秒发送一次心跳包(0x07)维持连接

### 4.4 断开连接
1. 小程序发送断开连接请求
2. 设备确认断开连接

### 4.5 错误处理（新增）
1. 当设备接收到无效命令或参数时，返回错误响应(0x8F)
2. 当通信出现校验和错误时，接收方丢弃数据包
3. 命令发送后2秒内未收到响应，自动重试，最多重试2次

## 五、设备功能详解

### 5.1 电源保护功能
- **短路保护**：检测到输出短路时，设备在10ms内切断输出并发送保护触发通知
- **过流保护**：当电流超过设定阈值时，设备切断输出并发送保护触发通知
  - 额定电流：2.5A
  - 保护阈值：3.0A
  - 响应时间：<50ms
- **低压保护**：当电池电压低于10.6V时，设备自动关闭并发送保护触发通知
  - 保护电压：10.6V
  - 恢复电压：12.0V
  - 检测精度：±0.1V
- **高压滤波**：设备内置高压滤波电路，自动滤除电源噪声

### 5.2 远程控制功能
- **档位控制**：支持5档调速 + 自动档
  - 1档：20%功率（静音模式）
  - 2档：40%功率（节能模式）
  - 3档：60%功率（标准模式）
  - 4档：80%功率（强力模式）
  - 5档：100%功率（最大模式）
  - 自动档：根据空气质量自动调节
- **复位控制**：
  - 软复位：重启设备程序，保留配置
  - 硬复位：完全重启设备，恢复出厂设置
  - 计时器复位：重置负离子计数器
- **蜂鸣器控制**：
  - 频率范围：1000-5000Hz
  - 持续时间：100-5000ms
  - 音量等级：3级可调
  - 静音模式：完全关闭

### 5.3 实时监测功能
- **空气质量实时显示**：
  - PM2.5：0-999μg/m³，精度1μg/m³
  - CO2：400-5000ppm，精度10ppm
  - TVOC：0-9999ppb，精度1ppb
  - 温度：-40~85℃，精度0.1℃
  - 湿度：0-100%RH，精度0.1%
- **电压电流监测**：
  - 电压范围：8-18V，精度0.01V
  - 电流范围：0-5A，精度0.001A
  - 功率计算：实时计算并显示
  - 采样频率：100ms/次
- **设备工作状态判断**：
  - 根据电压电流参数自动判断工作状态
  - 状态分类：停机/正常/待机/保护/故障
  - 异常检测：电压电流异常自动告警

### 5.4 负离子功能
- **负离子计数显示**：
  - 计数范围：0-9999万个/cm³
  - 计数精度：1万个/cm³
  - 传感器检测：自动检测是否支持
  - 计时器复位：可远程控制复位
- **传感器状态监控**：
  - 传感器在线检测
  - 故障自诊断
  - 校准状态监控

## 六、示例

### 6.1 查询设备状态
```
发送: AA 55 01 00 5C    // 校验和使用CRC-8
响应: AA 55 81 02 03 00 41    // 校验和使用CRC-8
```
- 解析: 设备当前处于开启状态，高挡位，蜂鸣器关闭

### 6.2 设置挡位为中挡
```
发送: AA 55 11 01 02 B0    // 校验和使用CRC-8
响应: AA 55 91 01 01 33    // 校验和使用CRC-8
```
- 解析: 设置成功

### 6.3 查询电气参数
```
发送: AA 55 04 00 5B    // 校验和使用CRC-8
响应: AA 55 84 06 9C 04 32 01 64 00 65    // 校验和使用CRC-8
```
- 解析:
  - 电压: 12.60V (0x049C)
  - 电流: 0.306A (0x0132)
  - 功率: 3.8W (0x0064)

### 6.4 保护触发通知
```
通知: AA 55 A5 02 03 00 47    // 校验和使用CRC-8
```
- 解析: 低压保护已触发

### 6.5 心跳包示例（新增）
```
发送: AA 55 07 00 5A    // 校验和使用CRC-8
响应: AA 55 87 00 3C    // 校验和使用CRC-8
```
- 解析: 心跳成功

## 七、注意事项

### 7.1 连接稳定性
- 小程序应每30秒发送一次心跳包(0x07)维持连接
- 如设备60秒未收到小程序的任何命令，将自动断开连接
- 连接断开后，小程序可尝试自动重连，最多3次

### 7.2 数据一致性
- 所有多字节数据采用小端序（低字节在前）
- 浮点数据转换为整数传输，接收方按比例转换
- 采用CRC-8校验提高数据传输可靠性

### 7.3 通信安全
- 重要命令（如复位设备）建议增加确认机制
- 敏感操作应有权限控制

### 7.4 异常处理
- 当设备触发保护时，应先查询保护状态再尝试恢复
- 低压保护触发后，需要充电至12V以上才能解除保护
- 短路保护和过流保护需要排除故障后手动复位
- 通信错误时应有重试机制，但避免无限重试

### 7.5 电源管理（新增）
- 小程序应考虑设备电源状态，尽量降低不必要的频繁通信
- 设备处于低功耗状态时，应减少数据交互频率 