<template>
	<view class="content">
		<!-- 操作按钮组 -->
		<view @click="ceshi" style="text-align: center;background-color: antiquewhite;margin: 10px;">ceshi</view>
		<view class="button-group">
			<button class="btn btn-primary" @click="initBluetoothAdapter" :disabled="isBluetoothAvailable">
				🔧 初始化蓝牙
			</button>
			<button class="btn btn-success" @click="startScan" :disabled="!isBluetoothAvailable || isScanning || connected">
				🔍 扫描设备
			</button>
			<button class="btn btn-warning" @click="stopScan" :disabled="!isScanning">
				⏹️ 停止扫描
			</button>
			<button class="btn btn-danger" @click="disconnectDevice" :disabled="!connected">
				🔌 断开连接
			</button>
		</view>

		<!-- 调试和帮助按钮组 -->
		<view class="debug-button-group">
			<button class="btn btn-debug" @click="showConnectionTips">
				💡 连接帮助
			</button>
			<button class="btn btn-debug" @click="clearBluetoothCache">
				🧹 清理缓存
			</button>
			<button class="btn btn-debug" @click="resetConnectionState">
				🔄 重置状态
			</button>
		</view>

		<!-- 连接状态卡片 -->
		<view class="status-card">
			<view class="status-header">
				<text class="status-title">📡 连接状态</text>
				<view class="status-indicator" :class="{ 'connected': connected, 'disconnected': !connected }">
					{{ connected ? '🟢 已连接' : '🔴 未连接' }}
				</view>
			</view>
			<view class="status-details">
				<view class="status-item">
					<text class="status-label">蓝牙适配器:</text>
					<text class="status-value" :class="{ 'success': isBluetoothAvailable, 'error': !isBluetoothAvailable }">
						{{ isBluetoothAvailable ? '✅ 可用' : '❌ 不可用' }}
					</text>
				</view>
				<view class="status-item">
					<text class="status-label">扫描状态:</text>
					<text class="status-value" :class="{ 'warning': isScanning }">
						{{ isScanning ? '🔍 扫描中...' : '⏸️ 未扫描' }}
					</text>
				</view>
				<view class="status-item" v-if="deviceName">
					<text class="status-label">设备名称:</text>
					<text class="status-value success">{{ deviceName }}</text>
				</view>
				<view class="status-item" v-if="serviceId">
					<text class="status-label">服务ID:</text>
					<text class="status-value info">{{ serviceId }}</text>
				</view>
			</view>
		</view>

		<!-- 设备列表卡片 -->
		<view class="device-list-card">
			<view class="card-header">
				<text class="card-title">📱 发现的设备</text>
				<text class="device-count">{{ devices.length }} 个设备</text>
			</view>
			<scroll-view scroll-y class="device-scroll">
				<view v-for="device in devices" :key="device.deviceId" class="device-item">
					<view class="device-info">
						<text class="device-name">{{ device.name || '未知设备' }}</text>
						<text class="device-id">{{ device.deviceId }}</text>
					</view>
					<button class="btn btn-connect" @click="connectDevice(device.deviceId)" :disabled="connected">
						{{ connected ? '已连接' : '连接' }}
					</button>
				</view>
				<view v-if="devices.length === 0" class="empty-state">
					<text>🔍 暂无发现设备，请先扫描</text>
				</view>
			</scroll-view>
		</view>

		<!-- 接收数据卡片 -->
		<view class="data-card">
			<view class="card-header">
				<text class="card-title">📥 接收到的数据</text>
				<button class="btn btn-clear" @click="clearReceivedData">清空</button>
			</view>
			<scroll-view scroll-y class="data-scroll">
				<view v-for="(msg, index) in receivedMessages" :key="index" class="message-item">
					<text class="message-text">{{ msg }}</text>
				</view>
				<view v-if="receivedMessages.length === 0" class="empty-state">
					<text>📭 暂无接收数据</text>
				</view>
			</scroll-view>
		</view>

		<!-- 命令发送卡片 -->
		<view class="command-card">
			<view class="card-header">
				<text class="card-title">📤 发送命令</text>
			</view>

			<!-- 查询命令组 -->
			<view class="command-group">
				<text class="group-title">🔍 查询命令</text>
				<view class="command-buttons">
					<button class="btn btn-query" @click="queryDeviceStatus" :disabled="!connected">
						📊 设备状态
					</button>
					<button class="btn btn-query" @click="querySensorData" :disabled="!connected">
						🌡️ 传感器数据
					</button>
					<button class="btn btn-query" @click="queryMaintenanceInfo" :disabled="!connected">
						🔧 保养信息
					</button>
					<button class="btn btn-query" @click="queryElectricalParameters" :disabled="!connected">
						⚡ 电气参数
					</button>
					<button class="btn btn-query" @click="queryProtectionStatus" :disabled="!connected">
						🛡️ 保护状态
					</button>
					<button class="btn btn-query" @click="queryAirQuality" :disabled="!connected">
						🌬️ 空气质量
					</button>
					<button class="btn btn-query" @click="sendHeartbeat" :disabled="!connected">
						💓 心跳包
					</button>
				</view>
			</view>

			<!-- 快捷控制组 -->
			<view class="command-group">
				<text class="group-title">⚡ 快捷控制</text>

				<!-- 蜂鸣器控制 -->
				<view class="quick-control-section">
					<text class="control-section-title">🔊 蜂鸣器控制</text>
					<view class="toggle-buttons">
						<button
							class="btn btn-toggle"
							:class="{ 'active': currentBuzzerState }"
							@click="toggleBuzzer"
							:disabled="!connected">
							{{ currentBuzzerState ? '🔊 开启' : '🔇 关闭' }}
						</button>
					</view>
				</view>

				<!-- 电压挡位控制 -->
				<view class="quick-control-section">
					<text class="control-section-title">⚡ 电压挡位控制</text>
					<view class="gear-buttons">
						<button
							class="btn btn-gear"
							:class="{ 'active': currentVoltageGear === 0 }"
							@click="setVoltageGear(0)"
							:disabled="!connected">
							关闭
						</button>
						<button
							class="btn btn-gear"
							:class="{ 'active': currentVoltageGear === 1 }"
							@click="setVoltageGear(1)"
							:disabled="!connected">
							1挡
						</button>
						<button
							class="btn btn-gear"
							:class="{ 'active': currentVoltageGear === 2 }"
							@click="setVoltageGear(2)"
							:disabled="!connected">
							2挡
						</button>
						<button
							class="btn btn-gear"
							:class="{ 'active': currentVoltageGear === 3 }"
							@click="setVoltageGear(3)"
							:disabled="!connected">
							3挡
						</button>
					</view>
				</view>
			</view>

			<!-- 控制命令组 -->
			<view class="command-group">
				<text class="group-title">🎛️ 控制命令</text>

				<!-- 简单控制按钮 -->
				<view class="command-buttons">
					<button class="btn btn-control" @click="resetDevice" :disabled="!connected">
						🔄 复位设备
					</button>
					<button class="btn btn-control" @click="resetTimer" :disabled="!connected">
						⏰ 复位计时器
					</button>
					<button class="btn btn-control" @click="resetAnionCounter" :disabled="!connected">
						🔋 复位负离子计数器
					</button>
				</view>

				<!-- 参数控制 -->
				<view class="param-controls">
					<view class="control-item">
						<text class="control-label">⚙️ 设置挡位:</text>
						<view class="control-input">
							<input class="input-field" type="number" v-model.number="gearValue" placeholder="0-3"/>
							<button class="btn btn-send" @click="setGear(gearValue)" :disabled="!connected">发送</button>
						</view>
					</view>

					<view class="control-item">
						<text class="control-label">🔊 控制蜂鸣器:</text>
						<view class="control-input">
							<input class="input-field" type="number" v-model.number="buzzerStatus" placeholder="0/1"/>
							<button class="btn btn-send" @click="controlBuzzer(buzzerStatus)" :disabled="!connected">发送</button>
						</view>
					</view>

					<view class="control-item">
						<text class="control-label">🛡️ 复位保护状态:</text>
						<view class="control-input">
							<input class="input-field" type="number" v-model.number="protectionResetType" placeholder="0x01-0xFF"/>
							<button class="btn btn-send" @click="resetProtectionStatus(protectionResetType)" :disabled="!connected">发送</button>
						</view>
					</view>

					<view class="control-item">
						<text class="control-label">💨 档位控制:</text>
						<view class="control-input">
							<input class="input-field" type="number" v-model.number="fanGear" placeholder="0-6"/>
							<button class="btn btn-send" @click="setFanGear(fanGear)" :disabled="!connected">发送</button>
						</view>
					</view>

					<view class="control-item">
						<text class="control-label">🎵 蜂鸣器高级控制:</text>
						<view class="control-input-group">
							<input class="input-field" type="number" v-model.number="buzzerFrequency" placeholder="频率(Hz)"/>
							<input class="input-field" type="number" v-model.number="buzzerDuration" placeholder="时间(ms)"/>
							<button class="btn btn-send" @click="controlBuzzerAdvanced(buzzerFrequency, buzzerDuration)" :disabled="!connected">发送</button>
						</view>
					</view>
				</view>
			</view>

			<!-- 测试命令组 -->
			<view class="command-group">
				<text class="group-title">🧪 测试命令</text>
				<view class="command-buttons">
					<button class="btn btn-test" @click="testRawData" :disabled="!connected">
						📡 测试原始数据
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isConnect: 0,
			deviceId: "",
			serviceId: "",
			notifyId: "",
			receivedTxt: [],
			receiveBuf: [],
			sentWiFiTxt: "ZHHC",
			sentPwdTxt: "666888999",

			// 新增蓝牙相关状态
			isBluetoothAvailable: false, // 蓝牙适配器是否可用
			isScanning: false, // 是否正在扫描
			connected: false, // 是否已连接
			devices: [], // 扫描到的设备列表
			deviceName: '', // 已连接设备名称
			characteristicId_write: '', // 写特征值ID
			characteristicId_notify: '', // 通知特征值ID
			receivedMessages: [], // 接收到的数据列表

			// 命令发送相关数据
			gearValue: 0, // 设置挡位
			buzzerStatus: 0, // 蜂鸣器状态
			protectionResetType: 0x01, // 复位保护状态类型
			fanGear: 0, // 风扇档位
			buzzerFrequency: 1000, // 蜂鸣器频率
			buzzerDuration: 100, // 蜂鸣器持续时间

			// 快捷控制状态
			currentBuzzerState: false, // 当前蜂鸣器状态 (false=关闭, true=开启)
			currentVoltageGear: 0, // 当前电压挡位 (0=关闭, 1=1挡, 2=2挡, 3=3挡)

			// 连接监控
			connectionMonitorTimer: null, // 连接监控定时器
			lastHeartbeatTime: 0, // 最后心跳时间
			autoReconnectEnabled: true, // 是否启用自动重连
			reconnectAttempts: 0, // 重连尝试次数
			maxReconnectAttempts: 5 // 最大重连次数
		};
	},
	onLoad() {
		// 初始化蓝牙适配器
		this.initBluetoothAdapter();
		// 监听蓝牙适配器状态变化
		uni.onBluetoothAdapterStateChange(this.onBluetoothAdapterStateChange);
		// 监听BLE连接状态变化
		uni.onBLEConnectionStateChange(this.onBLEConnectionStateChange);
		
		// 显式地取消并重新注册BLE特征值变化监听器，确保每次加载都是新的监听器
		uni.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange);
		uni.onBLECharacteristicValueChange(this.onBLECharacteristicValueChange);
		console.log('onBLECharacteristicValueChange 监听器已在onLoad中重新注册');
	},
	onShow() {
		
	},
	onUnload() {
		// 在页面卸载时移除BLE特征值变化监听器，防止内存泄漏
		uni.offBLECharacteristicValueChange(this.onBLECharacteristicValueChange);
		console.log('onBLECharacteristicValueChange 监听器已在onUnload中移除');

		// 停止连接监控
		this.stopConnectionMonitor();

		// 断开蓝牙连接
		if (this.connected && this.deviceId) {
			uni.closeBLEConnection({
				deviceId: this.deviceId,
				success: () => console.log('页面卸载时已断开蓝牙连接'),
				fail: () => console.log('页面卸载时断开蓝牙连接失败')
			});
		}
	},
	methods: {
		ceshi() {
		        uni.redirectTo({
		            url: '/pages/index/ceshi' 
		            // 注意这里如果是对象的最后一个属性，后面不需要分号，
		            // 另外如果有多个属性，属性之间用逗号分隔
		        });
		},
		receiveDataFromBle(data){
			console.log('--- receiveDataFromBle entered with data ---', new Uint8Array(data)); // 新增日志
			var array = new Uint8Array(data);

			// 显示原始数据到界面
			const hexStr = Array.from(array).map(byte => byte.toString(16).padStart(2, '0')).join(' ');
			const rawMsg = `📡 ${hexStr}`;
			this.receivedMessages.push(rawMsg);

			// 检查帧头
			// 检查帧头
			if (array.length < 5 || array[0] !== 0xAA || array[1] !== 0x55) {
				// 静默忽略无效数据
				return;
			}
			
			const command = array[2]; // 命令字
			const dataLength = array[3]; // 数据长度
			const receivedChecksum = array[array.length - 1]; // 校验和
			
			// 校验数据长度
			if (array.length !== (5 + dataLength)) {
				// 静默忽略长度不匹配的数据
				return;
			}
			
			// 尝试多种校验和计算方式
			const checksumData = array.slice(2, array.length - 1); // 从命令字开始到数据区结束
			const checksumDataWithHeader = array.slice(0, array.length - 1); // 包含帧头

			// 方式1: CRC-8 (仅命令和数据)
			const crc8_cmd_data = this.crc8(checksumData, checksumData.length);

			// 方式2: CRC-8 (包含帧头)
			const crc8_with_header = this.crc8(checksumDataWithHeader, checksumDataWithHeader.length);

			// 方式3: 简单累加和 (仅命令和数据)
			const sum_cmd_data = this.simpleSum(checksumData) & 0xFF;

			// 方式4: 简单累加和 (包含帧头)
			const sum_with_header = this.simpleSum(checksumDataWithHeader) & 0xFF;

			// 方式5: XOR校验 (仅命令和数据)
			const xor_cmd_data = this.xorChecksum(checksumData);

			// 方式6-10: 针对心跳包的多种特殊校验
			let heartbeat_checksums = [];
			if (array[2] === 0x87) { // 心跳响应
				// 方式6: 简单累加
				heartbeat_checksums.push({name: '心跳SUM1', value: (0xAA + 0x55 + 0x87 + 0x00) & 0xFF});

				// 方式7: 只对命令字校验
				heartbeat_checksums.push({name: '心跳CMD', value: 0x87});

				// 方式8: 固定值校验
				heartbeat_checksums.push({name: '心跳FIXED', value: 0x3C});

				// 方式9: 命令字取反
				heartbeat_checksums.push({name: '心跳NOT', value: (~0x87) & 0xFF});

				// 方式10: 特殊算法 (0x87 + 0x00 + 某个固定值)
				heartbeat_checksums.push({name: '心跳SPEC1', value: (0x87 + 0x00 + 0xB5) & 0xFF});
				heartbeat_checksums.push({name: '心跳SPEC2', value: (0x87 ^ 0xAB) & 0xFF});
			}

			console.log('校验和测试:');
			console.log(`  CRC-8(cmd+data): 0x${crc8_cmd_data.toString(16).padStart(2, '0')}`);
			console.log(`  CRC-8(all): 0x${crc8_with_header.toString(16).padStart(2, '0')}`);
			console.log(`  SUM(cmd+data): 0x${sum_cmd_data.toString(16).padStart(2, '0')}`);
			console.log(`  SUM(all): 0x${sum_with_header.toString(16).padStart(2, '0')}`);
			console.log(`  XOR(cmd+data): 0x${xor_cmd_data.toString(16).padStart(2, '0')}`);
			if (heartbeat_checksums.length > 0) {
				heartbeat_checksums.forEach(hb => {
					console.log(`  ${hb.name}: 0x${hb.value.toString(16).padStart(2, '0')}`);
				});
			}
			console.log(`  接收到的: 0x${receivedChecksum.toString(16).padStart(2, '0')}`);

			// 检查哪种方式匹配
			let checksumMatch = false;
			let matchMethod = '';

			if (crc8_cmd_data === receivedChecksum) {
				checksumMatch = true;
				matchMethod = 'CRC-8(cmd+data)';
			} else if (crc8_with_header === receivedChecksum) {
				checksumMatch = true;
				matchMethod = 'CRC-8(all)';
			} else if (sum_cmd_data === receivedChecksum) {
				checksumMatch = true;
				matchMethod = 'SUM(cmd+data)';
			} else if (sum_with_header === receivedChecksum) {
				checksumMatch = true;
				matchMethod = 'SUM(all)';
			} else if (xor_cmd_data === receivedChecksum) {
				checksumMatch = true;
				matchMethod = 'XOR(cmd+data)';
			} else {
				// 检查心跳包的特殊校验方式
				for (let hb of heartbeat_checksums) {
					if (hb.value === receivedChecksum) {
						checksumMatch = true;
						matchMethod = hb.name;
						break;
					}
				}
			}

			if (!checksumMatch) {
				console.warn('所有校验方式都不匹配，跳过校验继续处理');
				const warningMsg = `⚠️ 校验和不匹配，但继续处理数据`;
				this.receivedMessages.push(warningMsg);
				// 不再return，继续处理数据
			} else {
				console.log(`✅ 校验和匹配，使用方式: ${matchMethod}`);
				const successMsg = `✅ 校验和验证成功 (${matchMethod})`;
				this.receivedMessages.push(successMsg);
			}
			
			// 提取数据区
			const dataArea = array.slice(4, 4 + dataLength);
			
			console.log('接收到有效数据包:', array);
			console.log('命令字:', command.toString(16));
			console.log('数据长度:', dataLength);
			console.log('数据区:', dataArea);
			
			// 调用解析函数
			this.decodeDataFromBle(command, dataArea);
		},
		// Helper to convert 2 bytes to a 16-bit unsigned integer (little-endian)
		bytesToUint16(byteArray, offset) {
		    return (byteArray[offset + 1] << 8) | byteArray[offset];
		},
		// Helper to convert 4 bytes to a 32-bit unsigned integer (little-endian)
		bytesToUint32(byteArray, offset) {
		    return (byteArray[offset + 3] << 24) | (byteArray[offset + 2] << 16) | (byteArray[offset + 1] << 8) | byteArray[offset];
		},
		// 解析函数
		decodeDataFromBle(command, data){
			console.log("接收到命令: 0x" + command.toString(16), "数据:", data);

			switch (command) {
				case 0x81: // 设备状态响应
				case 0xA1: // 设备状态变化通知
					if (data.length >= 2) {
						const workStatusByte = data[0];
						const deviceModeByte = data[1];

						const switchStatus = (workStatusByte & 0b00000001) ? '打开' : '关闭';
						const gear = (workStatusByte & 0b00000110) >> 1; // Bit 1-2
						let gearStatus = '';
						if (gear === 0b00) gearStatus = '关闭';
						else if (gear === 0b01) gearStatus = '低挡';
						else if (gear === 0b10) gearStatus = '中挡';
						else if (gear === 0b11) gearStatus = '高挡';

						const buzzerStatus = (workStatusByte & 0b00001000) ? '打开' : '关闭'; // Bit 3
						const runException = (workStatusByte & 0b00010000) ? '异常' : '正常'; // Bit 4

						const workMode = (deviceModeByte & 0b00000011); // Bit 0-1
						let workModeStatus = '';
						if (workMode === 0b00) workModeStatus = '正常模式';
						else if (workMode === 0b01) workModeStatus = '节能模式';
						else if (workMode === 0b10) workModeStatus = '强力模式';

						// 检查状态是否发生变化
						const oldBuzzerState = this.currentBuzzerState;
						const oldVoltageGear = this.currentVoltageGear;

						// 更新快捷控制状态
						const newBuzzerState = (workStatusByte & 0b00001000) ? true : false;
						const newVoltageGear = gear; // 0=关闭, 1=低挡, 2=中挡, 3=高挡

						this.currentBuzzerState = newBuzzerState;
						this.currentVoltageGear = newVoltageGear;

						console.log(`设备状态: 开关: ${switchStatus}, 挡位: ${gearStatus}, 蜂鸣器: ${buzzerStatus}, 运行: ${runException}, 工作模式: ${workModeStatus}`);

						// 在界面显示解析后的状态
						const statusMsg = `📊 设备状态更新: 挡位=${gearStatus}, 蜂鸣器=${buzzerStatus}, 运行=${runException}`;
						this.receivedMessages.push(statusMsg);

						// 如果状态发生变化，显示确认提示
						if (oldBuzzerState !== newBuzzerState) {
							uni.showToast({
								title: `蜂鸣器已${newBuzzerState ? '开启' : '关闭'}`,
								icon: 'success',
								duration: 1500
							});
						}

						if (oldVoltageGear !== newVoltageGear) {
							const gearText = newVoltageGear === 0 ? '关闭' : `${newVoltageGear}挡`;
							uni.showToast({
								title: `电压挡位已设置为${gearText}`,
								icon: 'success',
								duration: 1500
							});
						}
					} else {
						console.warn('设备状态响应数据长度不足');
					}
					break;

				case 0x82: // 传感器数据响应
				case 0xA2: // 传感器数据变化通知
					if (data.length >= 10) {
						const pm25 = this.bytesToUint16(data, 0);
						const voc = this.bytesToUint16(data, 2);
						const anionConcentration = this.bytesToUint16(data, 4);
						const temperature = this.bytesToUint16(data, 6) / 10;
						const humidity = this.bytesToUint16(data, 8) / 10;
						console.log(`传感器数据: PM2.5: ${pm25}μg/m³, VOC: ${voc}ppm, 负离子浓度: ${anionConcentration}个/cm³, 温度: ${temperature}°C, 湿度: ${humidity}%RH`);
					} else {
						console.warn('传感器数据响应数据长度不足');
					}
					break;

				case 0x83: // 保养信息响应
					if (data.length >= 4) {
						const usedTime = this.bytesToUint32(data, 0);
						console.log(`保养信息: 已使用时间: ${usedTime}小时`);
					} else {
						console.warn('保养信息响应数据长度不足');
					}
					break;

				case 0x84: // 电气参数响应
					if (data.length >= 6) {
						const voltage = this.bytesToUint16(data, 0) / 100;
						const current = this.bytesToUint16(data, 2) / 1000;
						const power = this.bytesToUint16(data, 4) / 10;
						console.log(`电气参数: 电压: ${voltage}V, 电流: ${current}A, 功率: ${power}W`);
					} else {
						console.warn('电气参数响应数据长度不足');
					}
					break;

				case 0x85: // 保护状态响应
				case 0xA5: // 保护触发通知
					if (data.length >= 1) {
						const protectionStatusByte = data[0];
						const shortCircuit = (protectionStatusByte & 0b00000001) ? '已触发' : '未触发';
						const overcurrent = (protectionStatusByte & 0b00000010) ? '已触发' : '未触发';
						const undervoltage = (protectionStatusByte & 0b00000100) ? '已触发' : '未触发';
						const overtemperature = (protectionStatusByte & 0b00001000) ? '已触发' : '未触发';
						console.log(`保护状态: 短路: ${shortCircuit}, 过流: ${overcurrent}, 低压: ${undervoltage}, 过温: ${overtemperature}`);
					} else {
						console.warn('保护状态响应数据长度不足');
					}
					break;

				case 0x86: // 空气质量响应
				case 0xA6: // 空气质量变化通知
					if (data.length >= 1) {
						const airQualityLevel = data[0];
						let qualityText = '';
						switch (airQualityLevel) {
							case 0x01: qualityText = '优'; break;
							case 0x02: qualityText = '良'; break;
							case 0x03: qualityText = '轻度污染'; break;
							case 0x04: qualityText = '中度污染'; break;
							case 0x05: qualityText = '重度污染'; break;
							default: qualityText = '未知'; break;
						}
						console.log(`空气质量: ${qualityText}`);
					} else {
						console.warn('空气质量响应数据长度不足');
					}
					break;

				case 0x87: // 心跳响应
					console.log('✅ 心跳响应成功');
					this.lastHeartbeatTime = Date.now(); // 更新最后心跳时间
					const heartbeatMsg = `💓 心跳响应成功 - 设备连接正常`;
					this.receivedMessages.push(heartbeatMsg);
					uni.showToast({
						title: '心跳响应成功',
						icon: 'success',
						duration: 1000
					});
					break;

				case 0x91: // 设置挡位响应
				case 0x92: // 控制蜂鸣器响应
				case 0x93: // 复位设备响应
				case 0x94: // 复位计时器响应
				case 0x95: // 复位保护状态响应
				case 0x96: // 档位控制响应
				case 0x97: // 蜂鸣器高级控制响应
				case 0x98: // 负离子计数器复位响应
					if (data.length >= 1) {
						const result = data[0];
						const commandName = `0x${command.toString(16)}`
						if (result === 0x01) {
							console.log(`${commandName} 命令执行成功`);
							uni.showToast({ title: `${commandName} 命令执行成功`, icon: 'none'});
						} else {
							console.log(`${commandName} 命令执行失败`);
							uni.showToast({ title: `${commandName} 命令执行失败`, icon: 'none'});
						}
					} else {
						console.warn('控制命令响应数据长度不足');
					}
					break;
				
				case 0x89: // 负离子数据响应
					if (data.length >= 5) {
						const anionCount = this.bytesToUint32(data, 0);
						const sensorStatus = data[4];
						let statusText = '';
						switch (sensorStatus) {
							case 0x00: statusText = '不支持'; break;
							case 0x01: statusText = '正常'; break;
							case 0x02: statusText = '故障'; break;
							default: statusText = '未知'; break;
						}
						console.log(`负离子数据: 计数值: ${anionCount}个/cm³, 传感器状态: ${statusText}`);
					} else {
						console.warn('负离子数据响应数据长度不足');
					}
					break;

				case 0x8A: // 设备工作状态详细响应
					if (data.length >= 8) {
						const workStatus = data[0];
						let workStatusText = '';
						switch (workStatus) {
							case 0x00: workStatusText = '停机状态'; break;
							case 0x01: workStatusText = '正常工作'; break;
							case 0x02: workStatusText = '待机状态'; break;
							case 0x03: workStatusText = '保护状态'; break;
							case 0x04: workStatusText = '故障状态'; break;
							default: workStatusText = '未知'; break;
						}
						const voltageStatus = (data[1] === 0x01) ? '正常' : '异常';
						const currentStatus = (data[2] === 0x01) ? '正常' : '异常';
						const totalWorkTime = this.bytesToUint32(data, 3);
						console.log(`设备工作状态: ${workStatusText}, 电压状态: ${voltageStatus}, 电流状态: ${currentStatus}, 累计工作时间: ${totalWorkTime}秒`);
					} else {
						console.warn('设备工作状态详细响应数据长度不足');
					}
					break;

				case 0x8F: // 错误响应
					if (data.length >= 2) {
						const originalCommand = data[0];
						const errorCode = data[1];
						let errorText = '';
						switch (errorCode) {
							case 0x01: errorText = '参数错误'; break;
							case 0x02: errorText = '命令不支持'; break;
							case 0x03: errorText = '硬件错误'; break;
							default: errorText = '未知错误'; break;
						}
						console.warn(`错误响应: 原始命令字: 0x${originalCommand.toString(16)}, 错误代码: 0x${errorCode.toString(16)} (${errorText})`);
					} else {
						console.warn('错误响应数据长度不足');
					}
					break;
				
				case 0xA3: // 保养提醒通知
					if (data.length >= 1 && data[0] === 0x01) {
						console.log('保养提醒: 需要更换滤芯');
						uni.showToast({ title: '需要更换滤芯', icon: 'none'});
					} else {
						console.warn('保养提醒通知数据异常或长度不足');
					}
					break;

				default:
					console.log('未知命令: 0x' + command.toString(16));
					break;
			}
		},
		// 构建并发送BLE命令包
		sendBleCommand(command, dataArea = []) {
			if (!this.connected || !this.deviceId || !this.serviceId || !this.characteristicId_write) {
				uni.showToast({ title: '蓝牙未连接或特征值不可用', icon: 'none' });
				console.warn('发送失败：蓝牙未连接或特征值不可用');
				return;
			}
		    const dataLength = dataArea.length;
		    const packet = new Uint8Array(5 + dataLength);
		
		    packet[0] = 0xAA;
		    packet[1] = 0x55;
		    packet[2] = command;
		    packet[3] = dataLength;
		
		    for (let i = 0; i < dataLength; i++) {
		        packet[4 + i] = dataArea[i];
		    }
		
		    const checksumData = packet.slice(2, 4 + dataLength);
		    const checksum = this.crc8(checksumData, checksumData.length);
		    packet[4 + dataLength] = checksum;
		
		    console.log('准备发送BLE命令包:', Array.from(packet).map(byte => byte.toString(16).padStart(2, '0')).join(' '));
		
		    uni.writeBLECharacteristicValue({
		        deviceId: this.deviceId,
		        serviceId: this.serviceId,
		        characteristicId: this.characteristicId_write,
		        value: packet.buffer,
		        success(res) {
		            console.log('发送成功', res);
		            uni.showToast({ title: '命令发送成功', icon: 'success', duration: 1000 });
		        },
		        fail(err) {
		            console.error('发送失败', err);
		            uni.showToast({ title: '命令发送失败', icon: 'error', duration: 1000 });
		        }
		    });
		    return packet; // 返回以便测试或进一步处理
		},
		crc8(data, length) {
		    let crc = 0;
		    for (let i = 0; i < length; i++) {
		        crc ^= data[i];
		        for (let j = 0; j < 8; j++) {
		            if (crc & 0x80) {
		                crc = (crc << 1) ^ 0x07;
		            } else {
		                crc <<= 1;
		            }
		            // 确保结果在8位范围内
		            crc &= 0xFF;
		        }
		    }
		    return crc & 0xFF; // 确保返回值在0-255范围内
		},

		// 简单累加和校验
		simpleSum(data) {
			let sum = 0;
			for (let i = 0; i < data.length; i++) {
				sum += data[i];
			}
			return sum & 0xFF;
		},

		// XOR校验
		xorChecksum(data) {
			let xor = 0;
			for (let i = 0; i < data.length; i++) {
				xor ^= data[i];
			}
			return xor & 0xFF;
		},
		// 查询命令
		queryDeviceStatus() {
			console.log('发送：查询设备状态 (0x01)');
			return this.sendBleCommand(0x01);
		},
		querySensorData() {
			console.log('发送：查询传感器数据 (0x02)');
			return this.sendBleCommand(0x02);
		},
		queryMaintenanceInfo() {
			console.log('发送：查询保养信息 (0x03)');
			return this.sendBleCommand(0x03);
		},
		queryElectricalParameters() {
			console.log('发送：查询电气参数 (0x04)');
			return this.sendBleCommand(0x04);
		},
		queryProtectionStatus() {
			console.log('发送：查询保护状态 (0x05)');
			return this.sendBleCommand(0x05);
		},
		queryAirQuality() {
			console.log('发送：查询空气质量 (0x06)');
			return this.sendBleCommand(0x06);
		},
		sendHeartbeat() {
			console.log('发送：心跳包 (0x07)');
			const result = this.sendBleCommand(0x07);
			if (result) {
				uni.showToast({
					title: '心跳包已发送',
					icon: 'loading',
					duration: 1000
				});
			}
			return result;
		},
		// 控制命令
		setGear(gearValue) {
			console.log(`发送：设置挡位 (0x11), 挡位: ${gearValue}`);
			return this.sendBleCommand(0x11, [gearValue]);
		},
		controlBuzzer(status) {
			console.log(`发送：控制蜂鸣器 (0x12), 状态: ${status}`);
			return this.sendBleCommand(0x12, [status]);
		},
		resetDevice() {
			console.log('发送：复位设备 (0x13)');
			return this.sendBleCommand(0x13, [0x01]);
		},
		resetTimer() {
			console.log('发送：复位计时器 (0x14)');
			return this.sendBleCommand(0x14, [0x01]);
		},
		resetProtectionStatus(type) {
			console.log(`发送：复位保护状态 (0x15), 类型: ${type}`);
			return this.sendBleCommand(0x15, [type]);
		},
		setFanGear(gear) {
			console.log(`发送：档位控制 (0x16), 档位: ${gear}`);
			return this.sendBleCommand(0x16, [gear]);
		},
		controlBuzzerAdvanced(frequency, duration) {
			console.log(`发送：蜂鸣器高级控制 (0x17), 频率: ${frequency}Hz, 持续时间: ${duration}ms`);
			const freqBytes = [
				frequency & 0xFF,
				(frequency >> 8) & 0xFF
			];
			const durationBytes = [
				duration & 0xFF,
				(duration >> 8) & 0xFF
			];
			return this.sendBleCommand(0x17, [...freqBytes, ...durationBytes]);
		},
		resetAnionCounter() {
			console.log(`发送：负离子计数器复位 (0x18)`);
			return this.sendBleCommand(0x18, [0x01]);
		},
		// 测试发送原始数据
		testRawData() {
			console.log('测试发送简单数据');
			// 发送一个简单的测试数据包
			const testData = new Uint8Array([0x01, 0x02, 0x03, 0x04]);

			if (!this.connected || !this.deviceId || !this.serviceId || !this.characteristicId_write) {
				uni.showToast({ title: '蓝牙未连接', icon: 'none' });
				return;
			}

			uni.writeBLECharacteristicValue({
				deviceId: this.deviceId,
				serviceId: this.serviceId,
				characteristicId: this.characteristicId_write,
				value: testData.buffer,
				success: (res) => {
					console.log('测试数据发送成功', res);
					uni.showToast({ title: '测试数据发送成功', icon: 'success' });
				},
				fail: (err) => {
					console.error('测试数据发送失败', err);
					uni.showToast({ title: '测试数据发送失败', icon: 'error' });
				}
			});
		},
		// 清空接收数据
		clearReceivedData() {
			this.receivedMessages = [];
			uni.showToast({ title: '数据已清空', icon: 'success', duration: 1000 });
		},

		// 快捷控制方法
		// 切换蜂鸣器状态
		toggleBuzzer() {
			const newState = !this.currentBuzzerState;
			const status = newState ? 1 : 0;

			console.log(`快捷控制：切换蜂鸣器 ${newState ? '开启' : '关闭'} (0x12)`);

			// 发送命令
			const result = this.sendBleCommand(0x12, [status]);
			if (result) {
				// 不立即更新状态，等待设备响应
				uni.showToast({
					title: `正在${newState ? '开启' : '关闭'}蜂鸣器...`,
					icon: 'loading',
					duration: 1000
				});

				// 延迟查询设备状态以获取真实状态
				setTimeout(() => {
					this.queryDeviceStatus();
				}, 500);
			}
		},

		// 设置电压挡位
		setVoltageGear(gear) {
			console.log(`快捷控制：设置电压挡位 ${gear} (0x11)`);

			// 发送命令
			const result = this.sendBleCommand(0x11, [gear]);
			if (result) {
				// 不立即更新状态，等待设备响应
				const gearText = gear === 0 ? '关闭' : `${gear}挡`;
				uni.showToast({
					title: `正在设置为${gearText}...`,
					icon: 'loading',
					duration: 1000
				});

				// 延迟查询设备状态以获取真实状态
				setTimeout(() => {
					this.queryDeviceStatus();
				}, 500);
			}
		},
		// 蓝牙适配器状态变化监听
		onBluetoothAdapterStateChange(res) {
			console.log('蓝牙适配器状态变化:', res.available, res.discovering);
			this.isBluetoothAvailable = res.available;
			this.isScanning = res.discovering;

			if (!res.available) {
				// 蓝牙不可用时，重置所有状态
				this.resetConnectionState();
				this.devices = [];
				uni.showToast({ title: '蓝牙适配器不可用，请检查蓝牙是否开启', icon: 'none' });
			} else {
				// 蓝牙恢复可用
				console.log('蓝牙适配器已恢复可用');
			}
		},
		// 监听发现新设备
		onBluetoothDeviceFound(res) {
			res.devices.forEach(device => {
				if (!this.devices.some(item => item.deviceId === device.deviceId)) {
					this.devices.push(device);
				}
			});
		},
		// BLE连接状态变化监听
		onBLEConnectionStateChange(res) {
			console.log('BLE连接状态变化:', res.deviceId, res.connected);
			this.connected = res.connected;
			if (!res.connected) {
				uni.showToast({ title: `设备 ${res.deviceId} 已断开连接`, icon: 'none' });
				this.deviceName = '';
				this.deviceId = '';
				this.serviceId = '';
				this.characteristicId_write = '';
				this.characteristicId_notify = '';
			}
		},
		// BLE特征值变化监听
		onBLECharacteristicValueChange(res) {
			console.log('--- onBLECharacteristicValueChange triggered ---', res); // 新增日志
			const buffer = new Uint8Array(res.value);
			const hexStr = Array.prototype.map.call(buffer, byte => ('00' + byte.toString(16)).slice(-2)).join(' ');
			const msg = `收到通知: CharacteristicId ${res.characteristicId}, Value: [${buffer}], Hex: ${hexStr}`;
			this.receivedMessages.push(msg);

			// 添加调试信息，显示接收到数据的特征值UUID
			console.log('接收数据的特征值UUID:', res.characteristicId);
			console.log('接收到的原始数据:', hexStr);

			this.receiveDataFromBle(res.value); // 调用协议解析函数
		},

		// 初始化蓝牙适配器
		initBluetoothAdapter() {
			uni.openBluetoothAdapter({
				success: (res) => {
					console.log('initBluetoothAdapter success', res);
					this.isBluetoothAvailable = true;
					uni.showToast({ title: '蓝牙适配器初始化成功', icon: 'success' });
				},
				fail: (err) => {
					console.error('initBluetoothAdapter fail', err);
					this.isBluetoothAvailable = false;
					uni.showToast({ title: '蓝牙适配器初始化失败，请检查蓝牙是否打开', icon: 'none' });
				}
			});
		},
		// 开始扫描设备
		startScan() {
			if (!this.isBluetoothAvailable) {
				uni.showToast({ title: '蓝牙适配器不可用', icon: 'none' });
				return;
			}
			uni.startBluetoothDevicesDiscovery({
				allowDuplicatesKey: false,
				interval: 0,
				success: (res) => {
					console.log('startBluetoothDevicesDiscovery success', res);
					this.isScanning = true;
					this.devices = []; // 清空设备列表
					uni.onBluetoothDeviceFound(this.onBluetoothDeviceFound); // 监听发现新设备
					uni.showToast({ title: '开始扫描蓝牙设备', icon: 'success' });
				},
				fail: (err) => {
					console.error('startBluetoothDevicesDiscovery fail', err);
					this.isScanning = false;
					uni.showToast({ title: '扫描失败', icon: 'error' });
				}
			});
		},
		// 停止扫描设备
		stopScan() {
			uni.stopBluetoothDevicesDiscovery({
				success: (res) => {
					console.log('stopBluetoothDevicesDiscovery success', res);
					this.isScanning = false;
					uni.offBluetoothDeviceFound(this.onBluetoothDeviceFound); // 停止监听发现新设备
					uni.showToast({ title: '停止扫描', icon: 'success' });
				},
				fail: (err) => {
					console.error('stopBluetoothDevicesDiscovery fail', err);
					uni.showToast({ title: '停止扫描失败', icon: 'error' });
				}
			});
		},
		// 连接设备
		connectDevice(deviceId) {
			this.connectDeviceWithRetry(deviceId, 0);
		},

		// 带重试机制的连接设备方法
		connectDeviceWithRetry(deviceId, retryCount) {
			const maxRetries = 3;
			const retryDelay = 2000; // 2秒延迟

			uni.showLoading({ title: `连接中${retryCount > 0 ? ` (重试${retryCount}/${maxRetries})` : ''}...` });

			// 先停止扫描
			if (this.isScanning) {
				this.stopScan();
			}

			// 如果之前有连接，先断开
			if (this.connected && this.deviceId) {
				console.log('断开之前的连接...');
				uni.closeBLEConnection({
					deviceId: this.deviceId,
					success: () => console.log('之前连接已断开'),
					fail: () => console.log('断开之前连接失败，继续新连接')
				});
			}

			// 延迟一下再连接，给设备一些时间
			setTimeout(() => {
				uni.createBLEConnection({
					deviceId: deviceId,
					timeout: 15000, // 增加超时时间到15秒
					success: (res) => {
						console.log('createBLEConnection success', res);
						this.connected = true;
						this.deviceId = deviceId;
						const connectedDevice = this.devices.find(d => d.deviceId === deviceId);
						if (connectedDevice) {
							this.deviceName = connectedDevice.name || '未知设备';
						}
						uni.hideLoading();
						uni.showToast({ title: '连接成功', icon: 'success' });

						// 连接成功处理
						this.onConnectionSuccess();

						// 获取服务和特征值
						this.getServicesAndCharacteristics(deviceId);
					},
					fail: (err) => {
						console.error('createBLEConnection fail', err);
						this.connected = false;

						// 分析错误类型
						let errorMsg = '连接失败';
						if (err.errCode === 10003) {
							errorMsg = '设备连接失败，可能设备忙碌或距离过远';
						} else if (err.errCode === 10012) {
							errorMsg = '连接超时，请检查设备是否开启';
						} else if (err.errCode === 10001) {
							errorMsg = '蓝牙适配器未初始化';
						}

						// 重试逻辑
						if (retryCount < maxRetries) {
							uni.hideLoading();
							uni.showToast({
								title: `${errorMsg}，${retryDelay/1000}秒后重试...`,
								icon: 'none',
								duration: 1500
							});

							setTimeout(() => {
								this.connectDeviceWithRetry(deviceId, retryCount + 1);
							}, retryDelay);
						} else {
							uni.hideLoading();
							uni.showModal({
								title: '连接失败',
								content: `${errorMsg}\n\n建议操作：\n1. 确认设备已开启且在附近\n2. 重启设备蓝牙功能\n3. 清除设备缓存后重试\n4. 检查设备是否被其他应用占用`,
								showCancel: true,
								cancelText: '取消',
								confirmText: '重试',
								success: (modalRes) => {
									if (modalRes.confirm) {
										// 用户选择重试，重置重试计数
										this.connectDeviceWithRetry(deviceId, 0);
									}
								}
							});
						}
					}
				});
			}, retryCount > 0 ? 1000 : 500); // 重试时延迟更长
		},
		// 断开连接
		disconnectDevice() {
			if (!this.deviceId) {
				uni.showToast({ title: '没有连接的设备', icon: 'none' });
				return;
			}

			uni.closeBLEConnection({
				deviceId: this.deviceId,
				success: (res) => {
					console.log('closeBLEConnection success', res);
					this.resetConnectionState();
					uni.showToast({ title: '断开连接成功', icon: 'success' });
				},
				fail: (err) => {
					console.error('closeBLEConnection fail', err);
					// 即使断开失败，也重置连接状态
					this.resetConnectionState();
					uni.showToast({ title: '断开连接失败，已重置状态', icon: 'none' });
				}
			});
		},

		// 重置连接状态
		resetConnectionState() {
			this.connected = false;
			this.deviceName = '';
			this.deviceId = '';
			this.serviceId = '';
			this.characteristicId_write = '';
			this.characteristicId_notify = '';
			this.receivedMessages = [];
			// 重置快捷控制状态
			this.currentBuzzerState = false;
			this.currentVoltageGear = 0;
			// 停止连接监控
			this.stopConnectionMonitor();
			// 重置重连计数
			this.reconnectAttempts = 0;
			this.lastHeartbeatTime = 0;
		},

		// 清理蓝牙缓存（Android专用）
		clearBluetoothCache() {
			// #ifdef APP-PLUS
			if (uni.getSystemInfoSync().platform === 'android') {
				uni.showModal({
					title: '清理蓝牙缓存',
					content: '是否清理蓝牙缓存？这可能有助于解决连接问题。',
					success: (res) => {
						if (res.confirm) {
							// 这里可以调用原生插件清理蓝牙缓存
							// 目前只是提示用户手动操作
							uni.showModal({
								title: '手动清理提示',
								content: '请前往系统设置 > 应用管理 > 蓝牙 > 存储 > 清除缓存',
								showCancel: false
							});
						}
					}
				});
			}
			// #endif
		},

		// 显示连接帮助提示
		showConnectionTips() {
			uni.showModal({
				title: '蓝牙连接帮助',
				content: `连接失败的常见原因和解决方法：

1. 设备距离过远
   → 靠近设备重试

2. 设备被其他应用占用
   → 关闭其他蓝牙应用

3. 蓝牙缓存问题
   → 点击"清理缓存"按钮

4. 设备蓝牙故障
   → 重启设备蓝牙功能

5. 系统蓝牙异常
   → 重启手机蓝牙

错误码说明：
• 10003: 连接失败，设备忙碌
• 10012: 连接超时
• 10001: 蓝牙未初始化`,
				showCancel: false,
				confirmText: '知道了'
			});
		},

		// 启动连接监控
		startConnectionMonitor() {
			if (this.connectionMonitorTimer) {
				clearInterval(this.connectionMonitorTimer);
			}

			this.connectionMonitorTimer = setInterval(() => {
				if (this.connected && this.deviceId) {
					// 发送心跳包检测连接状态
					this.sendHeartbeat();

					// 检查心跳响应超时
					const now = Date.now();
					if (this.lastHeartbeatTime > 0 && (now - this.lastHeartbeatTime) > 60000) {
						console.warn('心跳超时，可能连接已断开');
						this.handleConnectionLost();
					}
				}
			}, 30000); // 每30秒检查一次
		},

		// 停止连接监控
		stopConnectionMonitor() {
			if (this.connectionMonitorTimer) {
				clearInterval(this.connectionMonitorTimer);
				this.connectionMonitorTimer = null;
			}
		},

		// 处理连接丢失
		handleConnectionLost() {
			console.log('检测到连接丢失');
			this.connected = false;

			if (this.autoReconnectEnabled && this.reconnectAttempts < this.maxReconnectAttempts) {
				this.reconnectAttempts++;
				console.log(`尝试自动重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

				uni.showToast({
					title: `连接丢失，正在重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
					icon: 'loading',
					duration: 2000
				});

				setTimeout(() => {
					if (this.deviceId) {
						this.connectDeviceWithRetry(this.deviceId, 0);
					}
				}, 3000);
			} else {
				uni.showToast({
					title: '连接已断开',
					icon: 'error'
				});
				this.resetConnectionState();
			}
		},

		// 连接成功后的处理
		onConnectionSuccess() {
			this.reconnectAttempts = 0;
			this.lastHeartbeatTime = Date.now();
			this.startConnectionMonitor();
		},

		// 获取服务和特征值
		getServicesAndCharacteristics(deviceId) {
			uni.getBLEDeviceServices({
				deviceId: deviceId,
				success: (res) => {
					console.log('getBLEDeviceServices success', res.services);
					const targetService = res.services.find(s => s.uuid.toUpperCase().includes('0000FF00'));
					if (targetService) {
						this.serviceId = targetService.uuid;
						uni.getBLEDeviceCharacteristics({
							deviceId: deviceId,
							serviceId: targetService.uuid,
							success: (charRes) => {
								console.log('getBLEDeviceCharacteristics success', charRes.characteristics);

								// 打印所有特征值的详细信息
								charRes.characteristics.forEach((char, index) => {
									console.log(`特征值${index}: UUID=${char.uuid}, 属性=`, char.properties);
								});

								// 查找写入特征值 - 使用FF02
								let writeChar = charRes.characteristics.find(c => c.uuid.toUpperCase().includes('0000FF02') && c.properties.write);

								// 如果找不到0xFF02，尝试查找其他可写特征值
								if (!writeChar) {
									writeChar = charRes.characteristics.find(c => c.properties.write);
									if (writeChar) {
										console.log('未找到0xFF02，使用其他可写特征值:', writeChar.uuid);
									}
								}

								// 使用协议定义的通知特征值 FF02
								const notifyChar = charRes.characteristics.find(c =>
									c.uuid.toUpperCase().includes('0000FF02') &&
									c.properties.notify
								);

								if (notifyChar) {
									console.log('找到协议定义的通知特征值:', notifyChar.uuid);
								}
								if (writeChar) {
									this.characteristicId_write = writeChar.uuid;
									console.log('找到可写特征值 (FF02):', this.characteristicId_write); // 新增日志
								} else {
									console.warn('未找到可写特征值 (0x0000FF02)');
								}
								if (notifyChar) {
									this.characteristicId_notify = notifyChar.uuid;
									console.log('找到可通知特征值:', this.characteristicId_notify); // 新增日志
									
									// 延迟订阅通知，以解决可能的时序问题
									setTimeout(() => {
										this.notifyCharacteristicValueChange(deviceId, targetService.uuid, notifyChar.uuid); // 订阅通知
									}, 200); // 延迟200ms
								} else {
									console.warn('未找到可通知特征值 (0x0000FF02)');
								}
								uni.showToast({ title: '服务和特征值发现成功', icon: 'success' });
							},
							fail: (charErr) => {
								console.error('getBLEDeviceCharacteristics fail', charErr);
								uni.showToast({ title: '获取特征值失败', icon: 'error' });
							}
						});
					} else {
						console.warn('未找到服务 0x0000FF00');
						uni.showToast({ title: '未找到指定服务', icon: 'none' });
					}
				},
				fail: (err) => {
					console.error('getBLEDeviceServices fail', err);
					uni.showToast({ title: '获取服务失败', icon: 'error' });
				}
			});
		},

		// 启用特征值通知
		notifyCharacteristicValueChange(deviceId, serviceId, characteristicId) {
			uni.notifyBLECharacteristicValueChange({
				state: true, // 启用notify
				deviceId: deviceId,
				serviceId: serviceId,
				characteristicId: characteristicId,
				success: (res) => {
					console.log('notifyBLECharacteristicValueChange success', res.errMsg);
					uni.showToast({ title: '已订阅通知', icon: 'success' });
				},
				fail: (err) => {
					console.error('notifyBLECharacteristicValueChange fail', err);
					uni.showToast({ title: '订阅通知失败', icon: 'error' });
				}
			});
		},
    }
};
</script>

<style scoped lang="scss">
.content {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	min-height: 100vh;
}

/* 按钮样式 */
.btn {
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 30rpx;
	margin: 10rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

	&:disabled {
		opacity: 0.5;
		transform: none !important;
		box-shadow: none !important;
	}

	&:not(:disabled):active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.btn-primary {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.btn-success {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	color: white;
}

.btn-warning {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	color: white;
}

.btn-danger {
	background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	color: #333;
}

.btn-query {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
	color: #333;
	font-size: 24rpx;
	padding: 15rpx 20rpx;
}

.btn-control {
	background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
	color: #333;
	font-size: 24rpx;
	padding: 15rpx 20rpx;
}

.btn-test {
	background: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);
	color: white;
}

.btn-connect {
	background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
	color: #333;
	font-size: 24rpx;
	padding: 12rpx 20rpx;
}

.btn-send {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
	color: #333;
	font-size: 24rpx;
	padding: 12rpx 20rpx;
	min-width: 120rpx;
}

.btn-clear {
	background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
	color: #333;
	font-size: 22rpx;
	padding: 8rpx 16rpx;
}

.btn-toggle {
	background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
	color: #333;
	font-size: 28rpx;
	padding: 20rpx 40rpx;
	min-width: 200rpx;

	&.active {
		background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
		color: white;
		box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.3);
	}
}

.btn-gear {
	background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
	color: #333;
	font-size: 26rpx;
	padding: 18rpx 30rpx;
	min-width: 120rpx;

	&.active {
		background: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
		color: white;
		box-shadow: 0 6rpx 20rpx rgba(9, 132, 227, 0.3);
		transform: scale(1.05);
	}
}

/* 卡片样式 */
.status-card, .device-list-card, .data-card, .command-card {
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	backdrop-filter: blur(10rpx);
	overflow: hidden;
}

/* 卡片头部 */
.card-header, .status-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.card-title, .status-title {
	font-size: 32rpx;
	font-weight: bold;
}

.device-count {
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 状态指示器 */
.status-indicator {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: bold;

	&.connected {
		background: rgba(76, 175, 80, 0.2);
		color: #4CAF50;
	}

	&.disconnected {
		background: rgba(244, 67, 54, 0.2);
		color: #F44336;
	}
}

/* 状态详情 */
.status-details {
	padding: 30rpx;
}

.status-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.status-label {
	font-size: 28rpx;
	color: #666;
}

.status-value {
	font-size: 28rpx;
	font-weight: 500;

	&.success { color: #4CAF50; }
	&.error { color: #F44336; }
	&.warning { color: #FF9800; }
	&.info { color: #2196F3; }
}

/* 按钮组 */
.button-group {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

/* 调试按钮组 */
.debug-button-group {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-around;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.7);
	border-radius: 15rpx;
	border: 2rpx dashed #ccc;
}

.btn-debug {
	background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
	color: white;
	font-size: 24rpx;
	padding: 15rpx 25rpx;
	margin: 5rpx;
	min-width: 140rpx;
}

/* 设备列表 */
.device-scroll {
	height: 300rpx;
	padding: 20rpx;
}

.device-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	margin-bottom: 15rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border-left: 6rpx solid #667eea;
}

.device-info {
	flex: 1;
}

.device-name {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
}

.device-id {
	display: block;
	font-size: 24rpx;
	color: #999;
}

/* 数据显示 */
.data-scroll {
	height: 400rpx;
	padding: 20rpx;
}

.message-item {
	padding: 15rpx;
	margin-bottom: 10rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #4CAF50;
}

.message-text {
	font-size: 26rpx;
	color: #333;
	word-break: break-all;
}

/* 命令组 */
.command-group {
	margin-bottom: 40rpx;
	padding: 30rpx;
}

.group-title {
	display: block;
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	padding-bottom: 10rpx;
	border-bottom: 2rpx solid #667eea;
}

.command-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

/* 参数控制 */
.param-controls {
	margin-top: 30rpx;
}

.control-item {
	margin-bottom: 25rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.control-label {
	display: block;
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 15rpx;
}

.control-input, .control-input-group {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.control-input-group {
	flex-wrap: wrap;
}

.input-field {
	flex: 1;
	min-width: 200rpx;
	padding: 15rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 26rpx;
	background: white;

	&:focus {
		border-color: #667eea;
		outline: none;
	}
}

/* 快捷控制 */
.quick-control-section {
	margin-bottom: 30rpx;
	padding: 25rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 15rpx;
	border-left: 6rpx solid #667eea;
}

.control-section-title {
	display: block;
	font-size: 28rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 20rpx;
}

.toggle-buttons {
	display: flex;
	justify-content: center;
}

.gear-buttons {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
	justify-content: space-between;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 60rpx 20rpx;
	color: #999;
	font-size: 28rpx;
}
</style>
