# 汽车空调滤芯净化器蓝牙通信协议实现

## 上下文
用户提供了一份详细的汽车空调滤芯净化器蓝牙通信协议，要求将现有小程序代码进行修改，使其符合该协议。

## 计划
1.  **分析现有 `index.vue` 的 `decodeDataFromBle` 函数**：比对现有逻辑与协议定义。
2.  **实现 CRC-8 校验函数**：在 `index.vue` 中添加 `crc8` 方法。
3.  **修改 `receiveDataFromBle` 函数**：根据协议的帧结构识别数据包，并进行 CRC-8 校验。
4.  **重构 `decodeDataFromBle` 函数**：根据协议的"响应命令"和"通知命令"重新解析不同命令字的数据。
5.  **移除旧的发送命令和辅助函数**：删除 `bookidTo4byte` 和 `strEncode`。
6.  **实现新的发送命令通用函数 `sendBleCommand`**：构建符合协议帧结构的数据包。
7.  **实现特定命令的发送函数**：根据协议"查询命令"和"控制命令"实现具体发送函数。

## 执行情况
以上所有计划步骤均已完成。代码已修改，以支持新的蓝牙通信协议。 