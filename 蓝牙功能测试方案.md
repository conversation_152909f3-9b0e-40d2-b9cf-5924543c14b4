# 蓝牙空气质量功能测试方案

## 🎯 测试目标
验证uni-app应用与汽车空调滤芯净化器的蓝牙通信功能，特别是空气质量监测功能。

## 📋 测试环境
- **设备**: 汽车空调滤芯净化器（支持蓝牙4.0 BLE）
- **应用**: uni-app蓝牙控制应用
- **手机**: 支持蓝牙4.0的Android/iOS设备
- **蓝牙协议**: 服务UUID 0xFF00，特征值 0xFF01/0xFF02

## 🔧 基础功能测试

### 测试1：蓝牙连接
**步骤**：
1. 开启设备电源
2. 打开应用，点击"初始化蓝牙"
3. 点击"扫描设备"
4. 选择目标设备，点击"连接"

**预期结果**：
- ✅ 扫描到设备（名称包含净化器标识）
- ✅ 连接成功，状态显示"已连接"
- ✅ 服务ID显示"0xFF00"
- ✅ 特征值获取成功

**失败处理**：
- 如连接失败，尝试"调试模式连接"
- 检查设备是否被其他应用占用
- 重启设备蓝牙功能

### 测试2：心跳通信
**步骤**：
1. 连接成功后，点击"💓 心跳"按钮
2. 观察响应

**预期结果**：
- ✅ 收到心跳响应（0x87）
- ✅ 显示"心跳响应成功"
- ✅ 连接状态保持稳定

## 🌬️ 空气质量功能测试

### 测试3：空气质量查询
**步骤**：
1. 点击"🌬️ 空气质量"按钮
2. 观察空气质量卡片显示

**预期结果**：
- ✅ 收到空气质量响应（0x86）
- ✅ 空气质量卡片显示等级（优/良/轻度污染/中度污染/重度污染）
- ✅ 显示对应颜色和表情图标
- ✅ 控制台输出详细数据

**测试数据验证**：
| 返回值 | 显示等级 | 颜色 | 图标 |
|--------|----------|------|------|
| 0x01 | 优 | 绿色 | 😊 |
| 0x02 | 良 | 黄色 | 🙂 |
| 0x03 | 轻度污染 | 橙色 | 😐 |
| 0x04 | 中度污染 | 红色 | 😷 |
| 0x05 | 重度污染 | 深红色 | 😵 |

### 测试4：传感器数据获取
**步骤**：
1. 点击"🌡️ 获取传感器数据"按钮
2. 观察传感器数据网格

**预期结果**：
- ✅ 收到传感器数据响应（0x82）
- ✅ PM2.5数值显示（范围：0-999 μg/m³）
- ✅ VOC数值显示（范围：0-999 ppm）
- ✅ 负离子浓度显示（范围：0-65535 个/cm³）
- ✅ 温度显示（范围：-40.0~85.0°C）
- ✅ 湿度显示（范围：0.0~100.0%RH）
- ✅ 更新时间显示（HH:MM:SS格式）

### 测试5：实时通知功能
**步骤**：
1. 保持设备连接
2. 改变环境条件（如点燃香烟、喷洒香水等）
3. 观察应用响应

**预期结果**：
- ✅ 收到空气质量变化通知（0xA6）
- ✅ 收到传感器数据变化通知（0xA2）
- ✅ 界面自动更新数据
- ✅ 显示Toast通知消息
- ✅ 数据变化合理（污染源增加时数值上升）

## 🎛️ 控制功能测试

### 测试6：设备控制
**步骤**：
1. 测试档位控制（1-5档 + 自动档）
2. 测试蜂鸣器控制
3. 测试电压挡位控制

**预期结果**：
- ✅ 档位切换成功，设备响应
- ✅ 蜂鸣器开关正常
- ✅ 电压挡位切换正常

### 测试7：设备状态查询
**步骤**：
1. 查询设备状态
2. 查询保养信息
3. 查询电气参数

**预期结果**：
- ✅ 获取设备工作状态
- ✅ 获取保养计时信息
- ✅ 获取电压、电流、功率数据

## 🔄 稳定性测试

### 测试8：长时间连接
**步骤**：
1. 保持连接30分钟以上
2. 定期查询数据
3. 观察连接稳定性

**预期结果**：
- ✅ 连接保持稳定
- ✅ 心跳机制正常工作
- ✅ 自动重连功能正常

### 测试9：断线重连
**步骤**：
1. 人为断开设备电源
2. 重新开启设备
3. 观察自动重连

**预期结果**：
- ✅ 检测到连接丢失
- ✅ 自动尝试重连
- ✅ 重连成功后功能正常

## 📊 性能测试

### 测试10：响应时间
**测试项目**：
- 连接建立时间：< 10秒
- 命令响应时间：< 3秒
- 数据更新频率：按设备推送频率

### 测试11：数据准确性
**验证方法**：
- 对比设备显示屏数据
- 使用第三方空气质量检测仪对比
- 多次测量验证一致性

## 🐛 异常情况测试

### 测试12：错误处理
**测试场景**：
1. 设备断电
2. 蓝牙信号干扰
3. 应用后台运行
4. 系统蓝牙关闭

**预期结果**：
- ✅ 错误提示清晰
- ✅ 应用不崩溃
- ✅ 能够恢复连接

## 📝 测试记录表

### 基础功能测试记录
| 测试项 | 预期结果 | 实际结果 | 通过/失败 | 备注 |
|--------|----------|----------|-----------|------|
| 蓝牙连接 | 连接成功 |  |  |  |
| 心跳通信 | 响应正常 |  |  |  |
| 空气质量查询 | 数据正确 |  |  |  |
| 传感器数据 | 数据完整 |  |  |  |
| 实时通知 | 自动更新 |  |  |  |

### 数据准确性记录
| 时间 | PM2.5 | VOC | 负离子 | 温度 | 湿度 | 空气质量 |
|------|-------|-----|--------|------|------|----------|
|      |       |     |        |      |      |          |
|      |       |     |        |      |      |          |

## 🎯 测试通过标准
- ✅ 蓝牙连接成功率 > 90%
- ✅ 数据获取成功率 > 95%
- ✅ 实时通知响应及时
- ✅ 界面显示正确无误
- ✅ 长时间运行稳定
- ✅ 异常情况处理得当

## 🔧 故障排除指南
1. **连接失败**: 使用调试模式连接，清理蓝牙缓存
2. **数据异常**: 检查设备状态，重新查询
3. **界面不更新**: 检查通知权限，重启应用
4. **响应超时**: 检查信号强度，靠近设备
