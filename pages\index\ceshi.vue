<template>
  <view class="container">
    <!-- 使用官方头像选择按钮 -->
    <button 
      open-type="chooseAvatar" 
      @chooseavatar="onChooseAvatar"
      class="avatar-btn"
    >
      <image 
        :src="avatarUrl || defaultAvatar" 
        class="avatar" 
        mode="aspectFill"
      ></image>
    </button>
    
    <!-- 表单区域 -->
    <view class="form">
      <view class="form-item">
        <view class="form-label">昵称</view>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入昵称" 
          v-model="nickname"
        />
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <button class="save-btn" @click="saveUserInfo">保存</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      avatarUrl: '',           // 用户头像URL
      nickname: '',            // 用户昵称
      defaultAvatar: '/static/default-avatar.png' // 默认头像
    }
  },
  onLoad() {
    // 加载本地缓存的用户信息
    this.loadUserInfo();
  },
  methods: {
    // 处理头像选择事件
    onChooseAvatar(e) {
      this.avatarUrl = e.detail.avatarUrl;
      this.saveUserInfo(); // 自动保存
    },
    
    // 保存用户信息到本地
    saveUserInfo() {
      uni.setStorageSync('userInfo', {
        avatarUrl: this.avatarUrl,
        nickname: this.nickname
      });
      uni.showToast({ title: '保存成功' });
    },
    
    // 从本地加载用户信息
    loadUserInfo() {
      const userInfo = uni.getStorageSync('userInfo');
      if (userInfo) {
        this.avatarUrl = userInfo.avatarUrl;
        this.nickname = userInfo.nickname;
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 30rpx;
}

.avatar-btn {
  display: block;
  width: 200rpx;
  height: 200rpx;
  padding: 0;
  margin: 40rpx auto;
  border: none;
  background-color: transparent;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.form {
  background-color: #fff;
  border-radius: 12rpx;
  margin-top: 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.form-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  text-align: right;
}

.save-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  margin-top: 60rpx;
}
</style>