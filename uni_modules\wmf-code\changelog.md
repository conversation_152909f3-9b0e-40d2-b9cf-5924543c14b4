## 1.6.2（2022-10-24）
* 修复已知问题
## 1.6.1（2022-10-19）
* 支付宝小程序适配问题
* 修改文档 
## 1.6.0（2022-07-27）
* 修改条形码bug
## 1.5.9（2022-07-26）
* 修复条形码保存图片部分黑底问题
## 1.5.8（2022-07-26）
* 修复支付宝小程序图片保存缺失问题
## 1.5.7（2022-07-09）
* 修复条形码保存缺失问题
## 1.5.6（2022-07-01）
修改引入错误
## 1.5.5（2022-07-01）
* 修复vue3无法通过ref获取保存图片方法
## 1.5.4（2022-06-01）
* 条形码支持添加文字
## 1.5.3（2022-05-31）
* 修复二维码有时候无法识别问题
* 建议使用npm方式
## 1.5.2（2022-05-31）
修改条形码垂直方向生成图片 参数互换
## 1.5.1（2022-05-31）
条形码增加垂直方向 默认水平方向
## 1.5.0（2022-05-30）
修改uni_modules引入错误
## 1.4.9（2022-05-25）
支持vue3
## 1.4.8（2021-12-06）
* 百度小程序不支持canvas的arcTo方法 如果在百度小程序中不支持的全部默认值
* 小程序平台绘制文字不支持渐变 取color数组第一个
## 1.4.7（2021-12-05）
* 修复已知问题
## 1.4.5（2021-12-05）
* 修复已知问题
## 1.4.4（2021-11-30）
*  level默认值修改为2
## 1.4.3（2021-11-30）
* 修复引用js_sdk引用路径
## 1.4.2（2021-11-29）
* 修复设置画布大小时没有带单位px导致显示不全
## 1.4.1（2021-11-29）
1. 修复在高分辨率屏幕上支付宝小程序显示模糊问题
2. 修复保存相册图片模糊问题
## 1.4.0（2021-11-23）
1.  使用TS重构
2.  新增二维码文字绘制
3.  新增二维码padding
4.  新增二维码中间图片圆角 圆形
5.  新增长按事件
6.  二维码边框支持透明
7.  颜色最多支持10中颜色渐变
## 1.3.7（2021-11-08）
修改引入
## 1.3.6（2021-11-02）
修改引用路径
## 1.3.5（2021-10-19）
canvas 增加 type="2d"
## 1.3.4（2021-10-14）
1. 修改百度小程序修改参数无法重新渲染问题
2. 修改纯中文无法识别问题
## 1.3.3（2021-09-04）
修改uni_module引用路径
## 1.3.2（2021-09-03）
* 【重要】多个条形码或者二维码同时渲染
* 增加示例
## 1.3.1（2021-08-31）
uni_modules支持组件方式
## 1.3.0（2021-08-31）
支持纯文字以及文字数字 字母的混合
## 1.2.9（2021-08-30）
新增二维码创建成功回调方法中返回生成的二维码图片
## 1.2.8（2021-08-20）
支持vue3
## 1.2.7（2021-08-20）
修改版本号
## 1.2.6（2021-06-28）
1. 修改自定义组件时微信小程序无法显示二维码的问题
2. 统一二维码的大小单位为rpx
## 1.2.5（2021-06-25）
二维码logo大小增加默认值：30
## 1.2.4（2021-06-24）
1. nvue 二维码支持渐变色
## 1.2.3（2021-06-23）
1. 修改app不支持颜色渐变问题
2. 修改判断方式Object.prototype.toString.call()
## 1.2.2（2021-06-23）
修改插件描述
## 1.2.1（2021-06-23）
修改return 错误
## 1.1.2（2021-06-23）
修改文档
## 1.1.1（2021-06-23）
 二维码支持渐变色
## 1.0.8（2021-06-22）
兼容nvue
## 1.0.7（2021-06-09）
1. 修改保存二维码图片的方法名以及传参方式
2. 修改微信小程序保存二维码时显示为黑色
3. 二维码增加参数
## 1.0.6（2021-06-04）
修改 description说明
## 1.0.5（2021-06-04）
修改文档展示名称
## 1.0.4（2021-06-04）
修改符合uni_modules规范
